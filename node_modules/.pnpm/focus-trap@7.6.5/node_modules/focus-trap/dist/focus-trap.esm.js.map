{"version": 3, "file": "focus-trap.esm.js", "sources": ["../index.js"], "sourcesContent": ["import {\n  tabbable,\n  focusable,\n  isFocusable,\n  isTabbable,\n  getTabIndex,\n} from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap._setPausedState(true);\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (\n      trapStack.length > 0 &&\n      !trapStack[trapStack.length - 1]._isManuallyPaused()\n    ) {\n      trapStack[trapStack.length - 1]._setPausedState(false);\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e?.key === 'Escape' || e?.key === 'Esc' || e?.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e?.key === 'Tab' || e?.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n    manuallyPaused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element, event) {\n    const composedPath =\n      typeof event?.composedPath === 'function'\n        ? event.composedPath()\n        : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        composedPath?.includes(container) ||\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @param {Object} options\n   * @param {boolean} [options.hasFallback] True if the option could be a selector string\n   *  and the option allows for a fallback scenario in the case where the selector is\n   *  valid but does not match a node (i.e. the queried node doesn't exist in the DOM).\n   * @param {Array} [options.params] Params to pass to the option if it's a function.\n   * @returns {undefined | null | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `null` if the option didn't resolve\n   *  to a node but `options.hasFallback=true`, `false` if the option resolved to `false`\n   *  (node explicitly not given); otherwise, the resolved DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node, unless the option is a selector string and `options.hasFallback=true`.\n   */\n  const getNodeForOption = function (\n    optionName,\n    { hasFallback = false, params = [] } = {}\n  ) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      try {\n        node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      } catch (err) {\n        throw new Error(\n          `\\`${optionName}\\` appears to be an invalid selector; error=\"${err.message}\"`\n        );\n      }\n\n      if (!node) {\n        if (!hasFallback) {\n          throw new Error(\n            `\\`${optionName}\\` as selector refers to no known node`\n          );\n        }\n        // else, `node` MUST be `null` because that's what `Document.querySelector()` returns\n        //  if the selector is valid but doesn't match anything\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus', { hasFallback: true });\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (\n      node === undefined ||\n      (node && !isFocusable(node, config.tabbableOptions))\n    ) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    } else if (node === null) {\n      // option is a VALID selector string that doesn't yield a node: use the `fallbackFocus`\n      //  option instead of the default behavior when the option isn't specified at all\n      node = getNodeForOption('fallbackFocus');\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      const firstTabbableNode =\n        tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      const lastTabbableNode =\n        tabbableNodes.length > 0\n          ? tabbableNodes[tabbableNodes.length - 1]\n          : undefined;\n\n      const firstDomTabbableNode = focusableNodes.find((node) =>\n        isTabbable(node)\n      );\n      const lastDomTabbableNode = focusableNodes\n        .slice()\n        .reverse()\n        .find((node) => isTabbable(node));\n\n      const posTabIndexesFound = !!tabbableNodes.find(\n        (node) => getTabIndex(node) > 0\n      );\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound,\n\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode,\n\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          const nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes\n                .slice(focusableNodes.indexOf(node) + 1)\n                .find((el) => isTabbable(el));\n            }\n\n            return focusableNodes\n              .slice(0, focusableNodes.indexOf(node))\n              .reverse()\n              .find((el) => isTabbable(el));\n          }\n\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (\n      state.containerGroups.find((g) => g.posTabIndexesFound) &&\n      state.containerGroups.length > 1\n    ) {\n      throw new Error(\n        \"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\"\n      );\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  const getActiveElement = function (el) {\n    const activeElement = el.activeElement;\n\n    if (!activeElement) {\n      return;\n    }\n\n    if (\n      activeElement.shadowRoot &&\n      activeElement.shadowRoot.activeElement !== null\n    ) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n\n    return activeElement;\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === getActiveElement(document)) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', {\n      params: [previousActiveElement],\n    });\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  const findNextNavNode = function ({ target, event, isBackward = false }) {\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target, event);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = state.tabbableGroups.findIndex(\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.lastTabbableNode\n              : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = state.tabbableGroups.findIndex(\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.firstTabbableNode\n              : destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate,\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  const checkFocusIn = function (event) {\n    const target = getActualTarget(event);\n    const targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      let nextNode; // next node to focus, if we find one\n      let navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if (getTabIndex(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          const mruContainerIdx = findContainerIndex(\n            state.mostRecentlyFocusedNode\n          );\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          const { tabbableNodes } = state.containerGroups[mruContainerIdx];\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            const mruTabIdx = tabbableNodes.findIndex(\n              (node) => node === state.mostRecentlyFocusedNode\n            );\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (\n            !state.containerGroups.some((g) =>\n              g.tabbableNodes.some((n) => getTabIndex(n) > 0)\n            )\n          ) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent),\n        });\n      }\n\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    state.recentNavEvent = event;\n\n    const destinationNode = findNextNavNode({ event, isBackward });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkTabKey = function (event) {\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  // we use a different event phase for the Escape key to allow canceling the event and checking for this in escapeDeactivates\n  const checkEscapeKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkTabKey, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkEscapeKey);\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkTabKey, true);\n    doc.removeEventListener('keydown', checkEscapeKey);\n\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  const checkDomRemoval = function (mutations) {\n    const isFocusedNodeRemoved = mutations.some(function (mutation) {\n      const removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  const mutationObserver =\n    typeof window !== 'undefined' && 'MutationObserver' in window\n      ? new MutationObserver(checkDomRemoval)\n      : undefined;\n\n  const updateObservedNodes = function () {\n    if (!mutationObserver) {\n      return;\n    }\n\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true,\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = getActiveElement(doc);\n\n      onActivate?.();\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate?.();\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      onDeactivate?.();\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate?.();\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause(pauseOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      state.manuallyPaused = true;\n\n      return this._setPausedState(true, pauseOptions);\n    },\n\n    unpause(unpauseOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      state.manuallyPaused = false;\n\n      if (trapStack[trapStack.length - 1] !== this) {\n        return this;\n      }\n\n      return this._setPausedState(false, unpauseOptions);\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      updateObservedNodes();\n\n      return this;\n    },\n  };\n\n  Object.defineProperties(trap, {\n    _isManuallyPaused: {\n      value() {\n        return state.manuallyPaused;\n      },\n    },\n    _setPausedState: {\n      value(paused, options) {\n        if (state.paused === paused) {\n          return this;\n        }\n\n        state.paused = paused;\n        if (paused) {\n          const onPause = getOption(options, 'onPause');\n          const onPostPause = getOption(options, 'onPostPause');\n          onPause?.();\n\n          removeListeners();\n          updateObservedNodes();\n\n          onPostPause?.();\n        } else {\n          const onUnpause = getOption(options, 'onUnpause');\n          const onPostUnpause = getOption(options, 'onPostUnpause');\n\n          onUnpause?.();\n\n          updateTabbableNodes();\n          addListeners();\n          updateObservedNodes();\n\n          onPostUnpause?.();\n        }\n\n        return this;\n      },\n    },\n  });\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["activeFocusTraps", "activateTrap", "trapStack", "trap", "length", "activeTrap", "_setPausedState", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "_isManuallyPaused", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "valueOrHandler", "value", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "manuallyPaused", "delayInitialFocusTimer", "undefined", "recentNavEvent", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "findIndex", "_ref", "container", "tabbableNodes", "contains", "includes", "find", "getNodeForOption", "_ref2", "_ref2$hasFallback", "<PERSON><PERSON><PERSON><PERSON>", "_ref2$params", "optionValue", "_toConsumableArray", "Error", "concat", "querySelector", "err", "message", "getInitialFocusNode", "isFocusable", "tabbableOptions", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "focusableNodes", "focusable", "lastTabbableNode", "firstDomTabbableNode", "isTabbable", "lastDomTabbableNode", "slice", "reverse", "posTabIndexesFound", "getTabIndex", "nextTabbableNode", "forward", "nodeIdx", "el", "filter", "group", "g", "getActiveElement", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "findNextNavNode", "_ref3", "_ref3$isBackward", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "_ref4", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "_ref5", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "nextNode", "navAcrossContainers", "mruContainerIdx", "mruTabIdx", "some", "n", "checkKeyNav", "checkTabKey", "checkEscapeKey", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "checkDomRemoval", "mutations", "isFocusedNodeRemoved", "mutation", "removedNodes", "from", "mutationObserver", "window", "MutationObserver", "updateObservedNodes", "disconnect", "observe", "subtree", "childList", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "pause", "pauseOptions", "unpause", "unpauseOptions", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean", "Object", "defineProperties", "onPause", "onPostPause", "onUnpause", "onPostUnpause"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAMA,gBAAgB,GAAG;AACvBC,EAAAA,YAAY,WAAZA,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAE;AAC5B,IAAA,IAAID,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;MACxB,IAAMC,UAAU,GAAGH,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC;MAClD,IAAIC,UAAU,KAAKF,IAAI,EAAE;AACvBE,QAAAA,UAAU,CAACC,eAAe,CAAC,IAAI,CAAC;AAClC;AACF;AAEA,IAAA,IAAMC,SAAS,GAAGL,SAAS,CAACM,OAAO,CAACL,IAAI,CAAC;AACzC,IAAA,IAAII,SAAS,KAAK,EAAE,EAAE;AACpBL,MAAAA,SAAS,CAACO,IAAI,CAACN,IAAI,CAAC;AACtB,KAAC,MAAM;AACL;AACAD,MAAAA,SAAS,CAACQ,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;AAC9BL,MAAAA,SAAS,CAACO,IAAI,CAACN,IAAI,CAAC;AACtB;GACD;AAEDQ,EAAAA,cAAc,WAAdA,cAAcA,CAACT,SAAS,EAAEC,IAAI,EAAE;AAC9B,IAAA,IAAMI,SAAS,GAAGL,SAAS,CAACM,OAAO,CAACL,IAAI,CAAC;AACzC,IAAA,IAAII,SAAS,KAAK,EAAE,EAAE;AACpBL,MAAAA,SAAS,CAACQ,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;AAChC;AAEA,IAAA,IACEL,SAAS,CAACE,MAAM,GAAG,CAAC,IACpB,CAACF,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACQ,iBAAiB,EAAE,EACpD;MACAV,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACE,eAAe,CAAC,KAAK,CAAC;AACxD;AACF;AACF,CAAC;AAED,IAAMO,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,IAAI,EAAE;AACxC,EAAA,OACEA,IAAI,CAACC,OAAO,IACZD,IAAI,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IACtC,OAAOF,IAAI,CAACG,MAAM,KAAK,UAAU;AAErC,CAAC;AAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,CAAC,EAAE;AACjC,EAAA,OAAO,CAAAA,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,MAAAA,GAAAA,MAAAA,GAAAA,CAAC,CAAEC,GAAG,MAAK,QAAQ,IAAI,CAAAD,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,MAAAA,GAAAA,MAAAA,GAAAA,CAAC,CAAEC,GAAG,MAAK,KAAK,IAAI,CAAAD,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,MAAAA,GAAAA,MAAAA,GAAAA,CAAC,CAAEE,OAAO,MAAK,EAAE;AACrE,CAAC;AAED,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAaH,CAAC,EAAE;EAC9B,OAAO,CAAAA,CAAC,KAADA,IAAAA,IAAAA,CAAC,uBAADA,CAAC,CAAEC,GAAG,MAAK,KAAK,IAAI,CAAAD,CAAC,aAADA,CAAC,KAAA,MAAA,GAAA,MAAA,GAADA,CAAC,CAAEE,OAAO,MAAK,CAAC;AAC7C,CAAC;;AAED;AACA,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAaJ,CAAC,EAAE;EAChC,OAAOG,UAAU,CAACH,CAAC,CAAC,IAAI,CAACA,CAAC,CAACK,QAAQ;AACrC,CAAC;;AAED;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaN,CAAC,EAAE;AACjC,EAAA,OAAOG,UAAU,CAACH,CAAC,CAAC,IAAIA,CAAC,CAACK,QAAQ;AACpC,CAAC;AAED,IAAME,KAAK,GAAG,SAARA,KAAKA,CAAaC,EAAE,EAAE;AAC1B,EAAA,OAAOC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAME,cAAc,GAAG,SAAjBA,cAAcA,CAAaC,KAAK,EAAa;EAAA,KAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAA5B,MAAA,EAAR6B,MAAM,OAAAC,KAAA,CAAAH,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAI,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA,EAAA,EAAA;AAANF,IAAAA,MAAM,CAAAE,IAAA,GAAAH,CAAAA,CAAAA,GAAAA,SAAA,CAAAG,IAAA,CAAA;AAAA;AAC/C,EAAA,OAAO,OAAOL,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAAM,KAAA,CAAIH,MAAAA,EAAAA,MAAM,CAAC,GAAGH,KAAK;AAC/D,CAAC;AAED,IAAMO,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,KAAK,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;EACA,OAAOA,KAAK,CAACC,MAAM,CAACC,UAAU,IAAI,OAAOF,KAAK,CAACG,YAAY,KAAK,UAAU,GACtEH,KAAK,CAACG,YAAY,EAAE,CAAC,CAAC,CAAC,GACvBH,KAAK,CAACC,MAAM;AAClB,CAAC;;AAED;AACA;AACA,IAAMG,iBAAiB,GAAG,EAAE;AAEtBC,IAAAA,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,QAAQ,EAAEC,WAAW,EAAE;AACvD;AACA;EACA,IAAMC,GAAG,GAAG,CAAAD,WAAW,KAAA,IAAA,IAAXA,WAAW,KAAA,MAAA,GAAA,MAAA,GAAXA,WAAW,CAAEE,QAAQ,KAAIA,QAAQ;EAE7C,IAAM7C,SAAS,GAAG,CAAA2C,WAAW,KAAA,IAAA,IAAXA,WAAW,KAAA,MAAA,GAAA,MAAA,GAAXA,WAAW,CAAE3C,SAAS,KAAIwC,iBAAiB;EAE7D,IAAMM,MAAM,GAAAC,cAAA,CAAA;AACVC,IAAAA,uBAAuB,EAAE,IAAI;AAC7BC,IAAAA,iBAAiB,EAAE,IAAI;AACvBC,IAAAA,iBAAiB,EAAE,IAAI;AACvB7B,IAAAA,YAAY,EAAZA,YAAY;AACZE,IAAAA,aAAa,EAAbA;AAAa,GAAA,EACVoB,WAAW,CACf;AAED,EAAA,IAAMQ,KAAK,GAAG;AACZ;AACA;AACAC,IAAAA,UAAU,EAAE,EAAE;AAEd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,IAAAA,eAAe,EAAE,EAAE;AAAE;;AAErB;AACA;AACA;AACA;AACAC,IAAAA,cAAc,EAAE,EAAE;AAElBC,IAAAA,2BAA2B,EAAE,IAAI;AACjCC,IAAAA,uBAAuB,EAAE,IAAI;AAC7BC,IAAAA,MAAM,EAAE,KAAK;AACbC,IAAAA,MAAM,EAAE,KAAK;AACbC,IAAAA,cAAc,EAAE,KAAK;AAErB;AACA;AACAC,IAAAA,sBAAsB,EAAEC,SAAS;AAEjC;AACAC,IAAAA,cAAc,EAAED;GACjB;EAED,IAAI5D,IAAI,CAAC;;AAET;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAM8D,SAAS,GAAG,SAAZA,SAASA,CAAIC,qBAAqB,EAAEC,UAAU,EAAEC,gBAAgB,EAAK;AACzE,IAAA,OAAOF,qBAAqB,IAC1BA,qBAAqB,CAACC,UAAU,CAAC,KAAKJ,SAAS,GAC7CG,qBAAqB,CAACC,UAAU,CAAC,GACjCnB,MAAM,CAACoB,gBAAgB,IAAID,UAAU,CAAC;GAC3C;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,OAAO,EAAEhC,KAAK,EAAE;AACnD,IAAA,IAAMG,YAAY,GAChB,QAAOH,KAAK,KAALA,IAAAA,IAAAA,KAAK,uBAALA,KAAK,CAAEG,YAAY,CAAK,KAAA,UAAU,GACrCH,KAAK,CAACG,YAAY,EAAE,GACpBsB,SAAS;AACf;AACA;AACA;AACA,IAAA,OAAOV,KAAK,CAACE,eAAe,CAACgB,SAAS,CACpC,UAAAC,IAAA,EAAA;AAAA,MAAA,IAAGC,SAAS,GAAAD,IAAA,CAATC,SAAS;QAAEC,aAAa,GAAAF,IAAA,CAAbE,aAAa;AAAA,MAAA,OACzBD,SAAS,CAACE,QAAQ,CAACL,OAAO,CAAC;AAE3B;AACA;AACA;AACA7B,MAAAA,YAAY,KAAZA,IAAAA,IAAAA,YAAY,KAAZA,MAAAA,GAAAA,MAAAA,GAAAA,YAAY,CAAEmC,QAAQ,CAACH,SAAS,CAAC,KACjCC,aAAa,CAACG,IAAI,CAAC,UAAC/D,IAAI,EAAA;QAAA,OAAKA,IAAI,KAAKwD,OAAO;OAAC,CAAA;AAAA,KAClD,CAAC;GACF;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMQ,gBAAgB,GAAG,SAAnBA,gBAAgBA,CACpBX,UAAU,EAEV;AAAA,IAAA,IAAAY,KAAA,GAAA/C,SAAA,CAAA5B,MAAA,GAAA,CAAA,IAAA4B,SAAA,CAAA,CAAA,CAAA,KAAA+B,SAAA,GAAA/B,SAAA,CAAA,CAAA,CAAA,GADuC,EAAE;MAAAgD,iBAAA,GAAAD,KAAA,CAAvCE,WAAW;AAAXA,MAAAA,WAAW,GAAAD,iBAAA,KAAG,MAAA,GAAA,KAAK,GAAAA,iBAAA;MAAAE,YAAA,GAAAH,KAAA,CAAE9C,MAAM;AAANA,MAAAA,MAAM,GAAAiD,YAAA,KAAG,MAAA,GAAA,EAAE,GAAAA,YAAA;AAElC,IAAA,IAAIC,WAAW,GAAGnC,MAAM,CAACmB,UAAU,CAAC;AAEpC,IAAA,IAAI,OAAOgB,WAAW,KAAK,UAAU,EAAE;MACrCA,WAAW,GAAGA,WAAW,CAAA/C,KAAA,SAAAgD,kBAAA,CAAInD,MAAM,CAAC,CAAA;AACtC;IAEA,IAAIkD,WAAW,KAAK,IAAI,EAAE;MACxBA,WAAW,GAAGpB,SAAS,CAAC;AAC1B;IAEA,IAAI,CAACoB,WAAW,EAAE;AAChB,MAAA,IAAIA,WAAW,KAAKpB,SAAS,IAAIoB,WAAW,KAAK,KAAK,EAAE;AACtD,QAAA,OAAOA,WAAW;AACpB;AACA;;AAEA,MAAA,MAAM,IAAIE,KAAK,CAAA,GAAA,CAAAC,MAAA,CACRnB,UAAU,iEACjB,CAAC;AACH;AAEA,IAAA,IAAIrD,IAAI,GAAGqE,WAAW,CAAC;;AAEvB,IAAA,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAI;QACFrE,IAAI,GAAGgC,GAAG,CAACyC,aAAa,CAACJ,WAAW,CAAC,CAAC;OACvC,CAAC,OAAOK,GAAG,EAAE;AACZ,QAAA,MAAM,IAAIH,KAAK,CAAAC,GAAAA,CAAAA,MAAA,CACRnB,UAAU,EAAAmB,+CAAAA,CAAAA,CAAAA,MAAA,CAAgDE,GAAG,CAACC,OAAO,OAC5E,CAAC;AACH;MAEA,IAAI,CAAC3E,IAAI,EAAE;QACT,IAAI,CAACmE,WAAW,EAAE;AAChB,UAAA,MAAM,IAAII,KAAK,CAAA,GAAA,CAAAC,MAAA,CACRnB,UAAU,0CACjB,CAAC;AACH;AACA;AACA;AACF;AACF;AAEA,IAAA,OAAOrD,IAAI;GACZ;AAED,EAAA,IAAM4E,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;AACtC,IAAA,IAAI5E,IAAI,GAAGgE,gBAAgB,CAAC,cAAc,EAAE;AAAEG,MAAAA,WAAW,EAAE;AAAK,KAAC,CAAC;;AAElE;IACA,IAAInE,IAAI,KAAK,KAAK,EAAE;AAClB,MAAA,OAAO,KAAK;AACd;AAEA,IAAA,IACEA,IAAI,KAAKiD,SAAS,IACjBjD,IAAI,IAAI,CAAC6E,WAAW,CAAC7E,IAAI,EAAEkC,MAAM,CAAC4C,eAAe,CAAE,EACpD;AACA;MACA,IAAIvB,kBAAkB,CAACvB,GAAG,CAAC+C,aAAa,CAAC,IAAI,CAAC,EAAE;QAC9C/E,IAAI,GAAGgC,GAAG,CAAC+C,aAAa;AAC1B,OAAC,MAAM;AACL,QAAA,IAAMC,kBAAkB,GAAGzC,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC;AAClD,QAAA,IAAMuC,iBAAiB,GACrBD,kBAAkB,IAAIA,kBAAkB,CAACC,iBAAiB;;AAE5D;AACAjF,QAAAA,IAAI,GAAGiF,iBAAiB,IAAIjB,gBAAgB,CAAC,eAAe,CAAC;AAC/D;AACF,KAAC,MAAM,IAAIhE,IAAI,KAAK,IAAI,EAAE;AACxB;AACA;AACAA,MAAAA,IAAI,GAAGgE,gBAAgB,CAAC,eAAe,CAAC;AAC1C;IAEA,IAAI,CAAChE,IAAI,EAAE;AACT,MAAA,MAAM,IAAIuE,KAAK,CACb,8DACF,CAAC;AACH;AAEA,IAAA,OAAOvE,IAAI;GACZ;AAED,EAAA,IAAMkF,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;IACtC3C,KAAK,CAACE,eAAe,GAAGF,KAAK,CAACC,UAAU,CAAC2C,GAAG,CAAC,UAACxB,SAAS,EAAK;MAC1D,IAAMC,aAAa,GAAGwB,QAAQ,CAACzB,SAAS,EAAEzB,MAAM,CAAC4C,eAAe,CAAC;;AAEjE;AACA;AACA;MACA,IAAMO,cAAc,GAAGC,SAAS,CAAC3B,SAAS,EAAEzB,MAAM,CAAC4C,eAAe,CAAC;AAEnE,MAAA,IAAMG,iBAAiB,GACrBrB,aAAa,CAACtE,MAAM,GAAG,CAAC,GAAGsE,aAAa,CAAC,CAAC,CAAC,GAAGX,SAAS;AACzD,MAAA,IAAMsC,gBAAgB,GACpB3B,aAAa,CAACtE,MAAM,GAAG,CAAC,GACpBsE,aAAa,CAACA,aAAa,CAACtE,MAAM,GAAG,CAAC,CAAC,GACvC2D,SAAS;AAEf,MAAA,IAAMuC,oBAAoB,GAAGH,cAAc,CAACtB,IAAI,CAAC,UAAC/D,IAAI,EAAA;QAAA,OACpDyF,UAAU,CAACzF,IAAI,CAAC;AAAA,OAClB,CAAC;AACD,MAAA,IAAM0F,mBAAmB,GAAGL,cAAc,CACvCM,KAAK,EAAE,CACPC,OAAO,EAAE,CACT7B,IAAI,CAAC,UAAC/D,IAAI,EAAA;QAAA,OAAKyF,UAAU,CAACzF,IAAI,CAAC;OAAC,CAAA;MAEnC,IAAM6F,kBAAkB,GAAG,CAAC,CAACjC,aAAa,CAACG,IAAI,CAC7C,UAAC/D,IAAI,EAAA;AAAA,QAAA,OAAK8F,WAAW,CAAC9F,IAAI,CAAC,GAAG,CAAC;AAAA,OACjC,CAAC;MAED,OAAO;AACL2D,QAAAA,SAAS,EAATA,SAAS;AACTC,QAAAA,aAAa,EAAbA,aAAa;AACbyB,QAAAA,cAAc,EAAdA,cAAc;AAEd;AACAQ,QAAAA,kBAAkB,EAAlBA,kBAAkB;AAElB;AACAZ,QAAAA,iBAAiB,EAAjBA,iBAAiB;AACjB;AACAM,QAAAA,gBAAgB,EAAhBA,gBAAgB;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,QAAAA,oBAAoB,EAApBA,oBAAoB;AACpB;AACAE,QAAAA,mBAAmB,EAAnBA,mBAAmB;AAEnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACQK,QAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC/F,IAAI,EAAkB;AAAA,UAAA,IAAhBgG,OAAO,GAAA9E,SAAA,CAAA5B,MAAA,GAAA,CAAA,IAAA4B,SAAA,CAAA,CAAA,CAAA,KAAA+B,SAAA,GAAA/B,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI;AACnC,UAAA,IAAM+E,OAAO,GAAGrC,aAAa,CAAClE,OAAO,CAACM,IAAI,CAAC;UAC3C,IAAIiG,OAAO,GAAG,CAAC,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;AACA,YAAA,IAAID,OAAO,EAAE;AACX,cAAA,OAAOX,cAAc,CAClBM,KAAK,CAACN,cAAc,CAAC3F,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAC,CACvC+D,IAAI,CAAC,UAACmC,EAAE,EAAA;gBAAA,OAAKT,UAAU,CAACS,EAAE,CAAC;eAAC,CAAA;AACjC;YAEA,OAAOb,cAAc,CAClBM,KAAK,CAAC,CAAC,EAAEN,cAAc,CAAC3F,OAAO,CAACM,IAAI,CAAC,CAAC,CACtC4F,OAAO,EAAE,CACT7B,IAAI,CAAC,UAACmC,EAAE,EAAA;cAAA,OAAKT,UAAU,CAACS,EAAE,CAAC;aAAC,CAAA;AACjC;UAEA,OAAOtC,aAAa,CAACqC,OAAO,IAAID,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;AACpD;OACD;AACH,KAAC,CAAC;IAEFzD,KAAK,CAACG,cAAc,GAAGH,KAAK,CAACE,eAAe,CAAC0D,MAAM,CACjD,UAACC,KAAK,EAAA;AAAA,MAAA,OAAKA,KAAK,CAACxC,aAAa,CAACtE,MAAM,GAAG,CAAC;AAAA,KAC3C,CAAC;;AAED;AACA,IAAA,IACEiD,KAAK,CAACG,cAAc,CAACpD,MAAM,IAAI,CAAC,IAChC,CAAC0E,gBAAgB,CAAC,eAAe,CAAC;MAClC;AACA,MAAA,MAAM,IAAIO,KAAK,CACb,qGACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACEhC,KAAK,CAACE,eAAe,CAACsB,IAAI,CAAC,UAACsC,CAAC,EAAA;MAAA,OAAKA,CAAC,CAACR,kBAAkB;KAAC,CAAA,IACvDtD,KAAK,CAACE,eAAe,CAACnD,MAAM,GAAG,CAAC,EAChC;AACA,MAAA,MAAM,IAAIiF,KAAK,CACb,+KACF,CAAC;AACH;GACD;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAM+B,iBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaJ,EAAE,EAAE;AACrC,IAAA,IAAMnB,aAAa,GAAGmB,EAAE,CAACnB,aAAa;IAEtC,IAAI,CAACA,aAAa,EAAE;AAClB,MAAA;AACF;IAEA,IACEA,aAAa,CAACrD,UAAU,IACxBqD,aAAa,CAACrD,UAAU,CAACqD,aAAa,KAAK,IAAI,EAC/C;AACA,MAAA,OAAOuB,iBAAgB,CAACvB,aAAa,CAACrD,UAAU,CAAC;AACnD;AAEA,IAAA,OAAOqD,aAAa;GACrB;AAED,EAAA,IAAMwB,SAAQ,GAAG,SAAXA,QAAQA,CAAavG,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAK,KAAK,EAAE;AAClB,MAAA;AACF;AAEA,IAAA,IAAIA,IAAI,KAAKsG,iBAAgB,CAACrE,QAAQ,CAAC,EAAE;AACvC,MAAA;AACF;AAEA,IAAA,IAAI,CAACjC,IAAI,IAAI,CAACA,IAAI,CAACwG,KAAK,EAAE;AACxBD,MAAAA,SAAQ,CAAC3B,mBAAmB,EAAE,CAAC;AAC/B,MAAA;AACF;IAEA5E,IAAI,CAACwG,KAAK,CAAC;AAAEC,MAAAA,aAAa,EAAE,CAAC,CAACvE,MAAM,CAACuE;AAAc,KAAC,CAAC;AACrD;IACAlE,KAAK,CAACK,uBAAuB,GAAG5C,IAAI;AAEpC,IAAA,IAAID,iBAAiB,CAACC,IAAI,CAAC,EAAE;MAC3BA,IAAI,CAACG,MAAM,EAAE;AACf;GACD;AAED,EAAA,IAAMuG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,qBAAqB,EAAE;AAC1D,IAAA,IAAM3G,IAAI,GAAGgE,gBAAgB,CAAC,gBAAgB,EAAE;MAC9C7C,MAAM,EAAE,CAACwF,qBAAqB;AAChC,KAAC,CAAC;IACF,OAAO3G,IAAI,GAAGA,IAAI,GAAGA,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG2G,qBAAqB;GACpE;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,KAAA,EAAoD;AAAA,IAAA,IAArCpF,MAAM,GAAAoF,KAAA,CAANpF,MAAM;MAAED,KAAK,GAAAqF,KAAA,CAALrF,KAAK;MAAAsF,gBAAA,GAAAD,KAAA,CAAEE,UAAU;AAAVA,MAAAA,UAAU,GAAAD,gBAAA,KAAG,MAAA,GAAA,KAAK,GAAAA,gBAAA;AACnErF,IAAAA,MAAM,GAAGA,MAAM,IAAIF,eAAe,CAACC,KAAK,CAAC;AACzC0D,IAAAA,mBAAmB,EAAE;IAErB,IAAI8B,eAAe,GAAG,IAAI;AAE1B,IAAA,IAAIzE,KAAK,CAACG,cAAc,CAACpD,MAAM,GAAG,CAAC,EAAE;AACnC;AACA;AACA;AACA,MAAA,IAAM2H,cAAc,GAAG1D,kBAAkB,CAAC9B,MAAM,EAAED,KAAK,CAAC;AACxD,MAAA,IAAM0F,cAAc,GAClBD,cAAc,IAAI,CAAC,GAAG1E,KAAK,CAACE,eAAe,CAACwE,cAAc,CAAC,GAAGhE,SAAS;MAEzE,IAAIgE,cAAc,GAAG,CAAC,EAAE;AACtB;AACA;AACA,QAAA,IAAIF,UAAU,EAAE;AACd;AACAC,UAAAA,eAAe,GACbzE,KAAK,CAACG,cAAc,CAACH,KAAK,CAACG,cAAc,CAACpD,MAAM,GAAG,CAAC,CAAC,CAClDiG,gBAAgB;AACvB,SAAC,MAAM;AACL;UACAyB,eAAe,GAAGzE,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC,CAACuC,iBAAiB;AAC7D;OACD,MAAM,IAAI8B,UAAU,EAAE;AACrB;;AAEA;QACA,IAAII,iBAAiB,GAAG5E,KAAK,CAACG,cAAc,CAACe,SAAS,CACpD,UAAA2D,KAAA,EAAA;AAAA,UAAA,IAAGnC,iBAAiB,GAAAmC,KAAA,CAAjBnC,iBAAiB;UAAA,OAAOxD,MAAM,KAAKwD,iBAAiB;AAAA,SACzD,CAAC;AAED,QAAA,IACEkC,iBAAiB,GAAG,CAAC,KACpBD,cAAc,CAACvD,SAAS,KAAKlC,MAAM,IACjCoD,WAAW,CAACpD,MAAM,EAAES,MAAM,CAAC4C,eAAe,CAAC,IAC1C,CAACW,UAAU,CAAChE,MAAM,EAAES,MAAM,CAAC4C,eAAe,CAAC,IAC3C,CAACoC,cAAc,CAACnB,gBAAgB,CAACtE,MAAM,EAAE,KAAK,CAAE,CAAC,EACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA0F,UAAAA,iBAAiB,GAAGF,cAAc;AACpC;QAEA,IAAIE,iBAAiB,IAAI,CAAC,EAAE;AAC1B;AACA;AACA;AACA,UAAA,IAAME,qBAAqB,GACzBF,iBAAiB,KAAK,CAAC,GACnB5E,KAAK,CAACG,cAAc,CAACpD,MAAM,GAAG,CAAC,GAC/B6H,iBAAiB,GAAG,CAAC;AAE3B,UAAA,IAAMG,gBAAgB,GAAG/E,KAAK,CAACG,cAAc,CAAC2E,qBAAqB,CAAC;AAEpEL,UAAAA,eAAe,GACblB,WAAW,CAACrE,MAAM,CAAC,IAAI,CAAC,GACpB6F,gBAAgB,CAAC/B,gBAAgB,GACjC+B,gBAAgB,CAAC5B,mBAAmB;AAC5C,SAAC,MAAM,IAAI,CAAClF,UAAU,CAACgB,KAAK,CAAC,EAAE;AAC7B;AACA;UACAwF,eAAe,GAAGE,cAAc,CAACnB,gBAAgB,CAACtE,MAAM,EAAE,KAAK,CAAC;AAClE;AACF,OAAC,MAAM;AACL;;AAEA;QACA,IAAI8F,gBAAgB,GAAGhF,KAAK,CAACG,cAAc,CAACe,SAAS,CACnD,UAAA+D,KAAA,EAAA;AAAA,UAAA,IAAGjC,gBAAgB,GAAAiC,KAAA,CAAhBjC,gBAAgB;UAAA,OAAO9D,MAAM,KAAK8D,gBAAgB;AAAA,SACvD,CAAC;AAED,QAAA,IACEgC,gBAAgB,GAAG,CAAC,KACnBL,cAAc,CAACvD,SAAS,KAAKlC,MAAM,IACjCoD,WAAW,CAACpD,MAAM,EAAES,MAAM,CAAC4C,eAAe,CAAC,IAC1C,CAACW,UAAU,CAAChE,MAAM,EAAES,MAAM,CAAC4C,eAAe,CAAC,IAC3C,CAACoC,cAAc,CAACnB,gBAAgB,CAACtE,MAAM,CAAE,CAAC,EAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA8F,UAAAA,gBAAgB,GAAGN,cAAc;AACnC;QAEA,IAAIM,gBAAgB,IAAI,CAAC,EAAE;AACzB;AACA;AACA;AACA,UAAA,IAAMF,sBAAqB,GACzBE,gBAAgB,KAAKhF,KAAK,CAACG,cAAc,CAACpD,MAAM,GAAG,CAAC,GAChD,CAAC,GACDiI,gBAAgB,GAAG,CAAC;AAE1B,UAAA,IAAMD,iBAAgB,GAAG/E,KAAK,CAACG,cAAc,CAAC2E,sBAAqB,CAAC;AAEpEL,UAAAA,eAAe,GACblB,WAAW,CAACrE,MAAM,CAAC,IAAI,CAAC,GACpB6F,iBAAgB,CAACrC,iBAAiB,GAClCqC,iBAAgB,CAAC9B,oBAAoB;AAC7C,SAAC,MAAM,IAAI,CAAChF,UAAU,CAACgB,KAAK,CAAC,EAAE;AAC7B;AACA;AACAwF,UAAAA,eAAe,GAAGE,cAAc,CAACnB,gBAAgB,CAACtE,MAAM,CAAC;AAC3D;AACF;AACF,KAAC,MAAM;AACL;AACA;AACAuF,MAAAA,eAAe,GAAGhD,gBAAgB,CAAC,eAAe,CAAC;AACrD;AAEA,IAAA,OAAOgD,eAAe;GACvB;;AAED;AACA;AACA,EAAA,IAAMS,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAapH,CAAC,EAAE;AACpC,IAAA,IAAMoB,MAAM,GAAGF,eAAe,CAAClB,CAAC,CAAC;IAEjC,IAAIkD,kBAAkB,CAAC9B,MAAM,EAAEpB,CAAC,CAAC,IAAI,CAAC,EAAE;AACtC;AACA,MAAA;AACF;IAEA,IAAIU,cAAc,CAACmB,MAAM,CAACwF,uBAAuB,EAAErH,CAAC,CAAC,EAAE;AACrD;MACAhB,IAAI,CAACsI,UAAU,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;QACAC,WAAW,EAAE1F,MAAM,CAACE;AACtB,OAAC,CAAC;AACF,MAAA;AACF;;AAEA;AACA;AACA;IACA,IAAIrB,cAAc,CAACmB,MAAM,CAAC2F,iBAAiB,EAAExH,CAAC,CAAC,EAAE;AAC/C;AACA,MAAA;AACF;;AAEA;IACAA,CAAC,CAACyH,cAAc,EAAE;GACnB;;AAED;AACA;AACA;AACA;AACA,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAavG,KAAK,EAAE;AACpC,IAAA,IAAMC,MAAM,GAAGF,eAAe,CAACC,KAAK,CAAC;IACrC,IAAMwG,eAAe,GAAGzE,kBAAkB,CAAC9B,MAAM,EAAED,KAAK,CAAC,IAAI,CAAC;;AAE9D;AACA,IAAA,IAAIwG,eAAe,IAAIvG,MAAM,YAAYwG,QAAQ,EAAE;AACjD,MAAA,IAAID,eAAe,EAAE;QACnBzF,KAAK,CAACK,uBAAuB,GAAGnB,MAAM;AACxC;AACF,KAAC,MAAM;AACL;MACAD,KAAK,CAAC0G,wBAAwB,EAAE;;AAEhC;AACA;AACA;MACA,IAAIC,QAAQ,CAAC;MACb,IAAIC,mBAAmB,GAAG,IAAI;MAC9B,IAAI7F,KAAK,CAACK,uBAAuB,EAAE;QACjC,IAAIkD,WAAW,CAACvD,KAAK,CAACK,uBAAuB,CAAC,GAAG,CAAC,EAAE;AAClD;AACA,UAAA,IAAMyF,eAAe,GAAG9E,kBAAkB,CACxChB,KAAK,CAACK,uBACR,CAAC;AACD;AACA;AACA;AACA;UACA,IAAQgB,aAAa,GAAKrB,KAAK,CAACE,eAAe,CAAC4F,eAAe,CAAC,CAAxDzE,aAAa;AACrB,UAAA,IAAIA,aAAa,CAACtE,MAAM,GAAG,CAAC,EAAE;AAC5B;AACA,YAAA,IAAMgJ,SAAS,GAAG1E,aAAa,CAACH,SAAS,CACvC,UAACzD,IAAI,EAAA;AAAA,cAAA,OAAKA,IAAI,KAAKuC,KAAK,CAACK,uBAAuB;AAAA,aAClD,CAAC;YACD,IAAI0F,SAAS,IAAI,CAAC,EAAE;cAClB,IAAIpG,MAAM,CAACzB,YAAY,CAAC8B,KAAK,CAACW,cAAc,CAAC,EAAE;AAC7C,gBAAA,IAAIoF,SAAS,GAAG,CAAC,GAAG1E,aAAa,CAACtE,MAAM,EAAE;AACxC6I,kBAAAA,QAAQ,GAAGvE,aAAa,CAAC0E,SAAS,GAAG,CAAC,CAAC;AACvCF,kBAAAA,mBAAmB,GAAG,KAAK;AAC7B;AACA;AACA;AACF,eAAC,MAAM;AACL,gBAAA,IAAIE,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE;AACtBH,kBAAAA,QAAQ,GAAGvE,aAAa,CAAC0E,SAAS,GAAG,CAAC,CAAC;AACvCF,kBAAAA,mBAAmB,GAAG,KAAK;AAC7B;AACA;AACA;AACF;AACA;AACF;AACF;AACA;AACA;AACA;AACA;AACF,SAAC,MAAM;AACL;AACA;AACA;AACA;UACA,IACE,CAAC7F,KAAK,CAACE,eAAe,CAAC8F,IAAI,CAAC,UAAClC,CAAC,EAAA;AAAA,YAAA,OAC5BA,CAAC,CAACzC,aAAa,CAAC2E,IAAI,CAAC,UAACC,CAAC,EAAA;AAAA,cAAA,OAAK1C,WAAW,CAAC0C,CAAC,CAAC,GAAG,CAAC;aAAC,CAAA;AAAA,WACjD,CAAC,EACD;AACA;AACA;AACA;AACAJ,YAAAA,mBAAmB,GAAG,KAAK;AAC7B;AACF;AACF,OAAC,MAAM;AACL;AACA;AACA;AACA;AACAA,QAAAA,mBAAmB,GAAG,KAAK;AAC7B;AAEA,MAAA,IAAIA,mBAAmB,EAAE;QACvBD,QAAQ,GAAGvB,eAAe,CAAC;AACzB;AACA;UACAnF,MAAM,EAAEc,KAAK,CAACK,uBAAuB;AACrCmE,UAAAA,UAAU,EAAE7E,MAAM,CAACvB,aAAa,CAAC4B,KAAK,CAACW,cAAc;AACvD,SAAC,CAAC;AACJ;AAEA,MAAA,IAAIiF,QAAQ,EAAE;QACZ5B,SAAQ,CAAC4B,QAAQ,CAAC;AACpB,OAAC,MAAM;QACL5B,SAAQ,CAAChE,KAAK,CAACK,uBAAuB,IAAIgC,mBAAmB,EAAE,CAAC;AAClE;AACF;AAEArC,IAAAA,KAAK,CAACW,cAAc,GAAGD,SAAS,CAAC;GAClC;;AAED;AACA;AACA;AACA;AACA,EAAA,IAAMwF,WAAW,GAAG,SAAdA,WAAWA,CAAajH,KAAK,EAAsB;AAAA,IAAA,IAApBuF,UAAU,GAAA7F,SAAA,CAAA5B,MAAA,GAAA,CAAA,IAAA4B,SAAA,CAAA,CAAA,CAAA,KAAA+B,SAAA,GAAA/B,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK;IACrDqB,KAAK,CAACW,cAAc,GAAG1B,KAAK;IAE5B,IAAMwF,eAAe,GAAGJ,eAAe,CAAC;AAAEpF,MAAAA,KAAK,EAALA,KAAK;AAAEuF,MAAAA,UAAU,EAAVA;AAAW,KAAC,CAAC;AAC9D,IAAA,IAAIC,eAAe,EAAE;AACnB,MAAA,IAAIxG,UAAU,CAACgB,KAAK,CAAC,EAAE;AACrB;AACA;AACA;AACA;QACAA,KAAK,CAACsG,cAAc,EAAE;AACxB;MACAvB,SAAQ,CAACS,eAAe,CAAC;AAC3B;AACA;GACD;AAED,EAAA,IAAM0B,WAAW,GAAG,SAAdA,WAAWA,CAAalH,KAAK,EAAE;AACnC,IAAA,IAAIU,MAAM,CAACzB,YAAY,CAACe,KAAK,CAAC,IAAIU,MAAM,CAACvB,aAAa,CAACa,KAAK,CAAC,EAAE;MAC7DiH,WAAW,CAACjH,KAAK,EAAEU,MAAM,CAACvB,aAAa,CAACa,KAAK,CAAC,CAAC;AACjD;GACD;;AAED;AACA,EAAA,IAAMmH,cAAc,GAAG,SAAjBA,cAAcA,CAAanH,KAAK,EAAE;AACtC,IAAA,IACEpB,aAAa,CAACoB,KAAK,CAAC,IACpBT,cAAc,CAACmB,MAAM,CAACG,iBAAiB,EAAEb,KAAK,CAAC,KAAK,KAAK,EACzD;MACAA,KAAK,CAACsG,cAAc,EAAE;MACtBzI,IAAI,CAACsI,UAAU,EAAE;AACnB;GACD;AAED,EAAA,IAAMiB,UAAU,GAAG,SAAbA,UAAUA,CAAavI,CAAC,EAAE;AAC9B,IAAA,IAAMoB,MAAM,GAAGF,eAAe,CAAClB,CAAC,CAAC;IAEjC,IAAIkD,kBAAkB,CAAC9B,MAAM,EAAEpB,CAAC,CAAC,IAAI,CAAC,EAAE;AACtC,MAAA;AACF;IAEA,IAAIU,cAAc,CAACmB,MAAM,CAACwF,uBAAuB,EAAErH,CAAC,CAAC,EAAE;AACrD,MAAA;AACF;IAEA,IAAIU,cAAc,CAACmB,MAAM,CAAC2F,iBAAiB,EAAExH,CAAC,CAAC,EAAE;AAC/C,MAAA;AACF;IAEAA,CAAC,CAACyH,cAAc,EAAE;IAClBzH,CAAC,CAAC6H,wBAAwB,EAAE;GAC7B;;AAED;AACA;AACA;;AAEA,EAAA,IAAMW,YAAY,GAAG,SAAfA,YAAYA,GAAe;AAC/B,IAAA,IAAI,CAACtG,KAAK,CAACM,MAAM,EAAE;AACjB,MAAA;AACF;;AAEA;AACA3D,IAAAA,gBAAgB,CAACC,YAAY,CAACC,SAAS,EAAEC,IAAI,CAAC;;AAE9C;AACA;IACAkD,KAAK,CAACS,sBAAsB,GAAGd,MAAM,CAACI,iBAAiB,GACnD1B,KAAK,CAAC,YAAY;AAChB2F,MAAAA,SAAQ,CAAC3B,mBAAmB,EAAE,CAAC;AACjC,KAAC,CAAC,GACF2B,SAAQ,CAAC3B,mBAAmB,EAAE,CAAC;IAEnC5C,GAAG,CAAC8G,gBAAgB,CAAC,SAAS,EAAEf,YAAY,EAAE,IAAI,CAAC;AACnD/F,IAAAA,GAAG,CAAC8G,gBAAgB,CAAC,WAAW,EAAErB,gBAAgB,EAAE;AAClDsB,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE;AACX,KAAC,CAAC;AACFhH,IAAAA,GAAG,CAAC8G,gBAAgB,CAAC,YAAY,EAAErB,gBAAgB,EAAE;AACnDsB,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE;AACX,KAAC,CAAC;AACFhH,IAAAA,GAAG,CAAC8G,gBAAgB,CAAC,OAAO,EAAEF,UAAU,EAAE;AACxCG,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE;AACX,KAAC,CAAC;AACFhH,IAAAA,GAAG,CAAC8G,gBAAgB,CAAC,SAAS,EAAEJ,WAAW,EAAE;AAC3CK,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE;AACX,KAAC,CAAC;AACFhH,IAAAA,GAAG,CAAC8G,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;AAE/C,IAAA,OAAOtJ,IAAI;GACZ;AAED,EAAA,IAAM4J,eAAe,GAAG,SAAlBA,eAAeA,GAAe;AAClC,IAAA,IAAI,CAAC1G,KAAK,CAACM,MAAM,EAAE;AACjB,MAAA;AACF;IAEAb,GAAG,CAACkH,mBAAmB,CAAC,SAAS,EAAEnB,YAAY,EAAE,IAAI,CAAC;IACtD/F,GAAG,CAACkH,mBAAmB,CAAC,WAAW,EAAEzB,gBAAgB,EAAE,IAAI,CAAC;IAC5DzF,GAAG,CAACkH,mBAAmB,CAAC,YAAY,EAAEzB,gBAAgB,EAAE,IAAI,CAAC;IAC7DzF,GAAG,CAACkH,mBAAmB,CAAC,OAAO,EAAEN,UAAU,EAAE,IAAI,CAAC;IAClD5G,GAAG,CAACkH,mBAAmB,CAAC,SAAS,EAAER,WAAW,EAAE,IAAI,CAAC;AACrD1G,IAAAA,GAAG,CAACkH,mBAAmB,CAAC,SAAS,EAAEP,cAAc,CAAC;AAElD,IAAA,OAAOtJ,IAAI;GACZ;;AAED;AACA;AACA;;AAEA,EAAA,IAAM8J,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,SAAS,EAAE;IAC3C,IAAMC,oBAAoB,GAAGD,SAAS,CAACb,IAAI,CAAC,UAAUe,QAAQ,EAAE;MAC9D,IAAMC,YAAY,GAAGnI,KAAK,CAACoI,IAAI,CAACF,QAAQ,CAACC,YAAY,CAAC;AACtD,MAAA,OAAOA,YAAY,CAAChB,IAAI,CAAC,UAAUvI,IAAI,EAAE;AACvC,QAAA,OAAOA,IAAI,KAAKuC,KAAK,CAACK,uBAAuB;AAC/C,OAAC,CAAC;AACJ,KAAC,CAAC;;AAEF;AACA;AACA,IAAA,IAAIyG,oBAAoB,EAAE;AACxB9C,MAAAA,SAAQ,CAAC3B,mBAAmB,EAAE,CAAC;AACjC;GACD;;AAED;AACA;AACA,EAAA,IAAM6E,gBAAgB,GACpB,OAAOC,MAAM,KAAK,WAAW,IAAI,kBAAkB,IAAIA,MAAM,GACzD,IAAIC,gBAAgB,CAACR,eAAe,CAAC,GACrClG,SAAS;AAEf,EAAA,IAAM2G,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;IACtC,IAAI,CAACH,gBAAgB,EAAE;AACrB,MAAA;AACF;IAEAA,gBAAgB,CAACI,UAAU,EAAE;IAC7B,IAAItH,KAAK,CAACM,MAAM,IAAI,CAACN,KAAK,CAACO,MAAM,EAAE;AACjCP,MAAAA,KAAK,CAACC,UAAU,CAAC2C,GAAG,CAAC,UAAUxB,SAAS,EAAE;AACxC8F,QAAAA,gBAAgB,CAACK,OAAO,CAACnG,SAAS,EAAE;AAClCoG,UAAAA,OAAO,EAAE,IAAI;AACbC,UAAAA,SAAS,EAAE;AACb,SAAC,CAAC;AACJ,OAAC,CAAC;AACJ;GACD;;AAED;AACA;AACA;;AAEA3K,EAAAA,IAAI,GAAG;IACL,IAAIwD,MAAMA,GAAG;MACX,OAAON,KAAK,CAACM,MAAM;KACpB;IAED,IAAIC,MAAMA,GAAG;MACX,OAAOP,KAAK,CAACO,MAAM;KACpB;AAEDmH,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACC,eAAe,EAAE;MACxB,IAAI3H,KAAK,CAACM,MAAM,EAAE;AAChB,QAAA,OAAO,IAAI;AACb;AAEA,MAAA,IAAMsH,UAAU,GAAGhH,SAAS,CAAC+G,eAAe,EAAE,YAAY,CAAC;AAC3D,MAAA,IAAME,cAAc,GAAGjH,SAAS,CAAC+G,eAAe,EAAE,gBAAgB,CAAC;AACnE,MAAA,IAAMG,iBAAiB,GAAGlH,SAAS,CAAC+G,eAAe,EAAE,mBAAmB,CAAC;MAEzE,IAAI,CAACG,iBAAiB,EAAE;AACtBnF,QAAAA,mBAAmB,EAAE;AACvB;MAEA3C,KAAK,CAACM,MAAM,GAAG,IAAI;MACnBN,KAAK,CAACO,MAAM,GAAG,KAAK;AACpBP,MAAAA,KAAK,CAACI,2BAA2B,GAAG2D,iBAAgB,CAACtE,GAAG,CAAC;AAEzDmI,MAAAA,UAAU,KAAVA,IAAAA,IAAAA,UAAU,KAAVA,MAAAA,IAAAA,UAAU,EAAI;AAEd,MAAA,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,GAAS;AAC7B,QAAA,IAAID,iBAAiB,EAAE;AACrBnF,UAAAA,mBAAmB,EAAE;AACvB;AACA2D,QAAAA,YAAY,EAAE;AACde,QAAAA,mBAAmB,EAAE;AACrBQ,QAAAA,cAAc,KAAdA,IAAAA,IAAAA,cAAc,KAAdA,MAAAA,IAAAA,cAAc,EAAI;OACnB;AAED,MAAA,IAAIC,iBAAiB,EAAE;AACrBA,QAAAA,iBAAiB,CAAC9H,KAAK,CAACC,UAAU,CAACgC,MAAM,EAAE,CAAC,CAAC+F,IAAI,CAC/CD,gBAAgB,EAChBA,gBACF,CAAC;AACD,QAAA,OAAO,IAAI;AACb;AAEAA,MAAAA,gBAAgB,EAAE;AAClB,MAAA,OAAO,IAAI;KACZ;AAED3C,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAC6C,iBAAiB,EAAE;AAC5B,MAAA,IAAI,CAACjI,KAAK,CAACM,MAAM,EAAE;AACjB,QAAA,OAAO,IAAI;AACb;MAEA,IAAM4H,OAAO,GAAAtI,cAAA,CAAA;QACXuI,YAAY,EAAExI,MAAM,CAACwI,YAAY;QACjCC,gBAAgB,EAAEzI,MAAM,CAACyI,gBAAgB;QACzCC,mBAAmB,EAAE1I,MAAM,CAAC0I;AAAmB,OAAA,EAC5CJ,iBAAiB,CACrB;AAEDK,MAAAA,YAAY,CAACtI,KAAK,CAACS,sBAAsB,CAAC,CAAC;MAC3CT,KAAK,CAACS,sBAAsB,GAAGC,SAAS;AAExCgG,MAAAA,eAAe,EAAE;MACjB1G,KAAK,CAACM,MAAM,GAAG,KAAK;MACpBN,KAAK,CAACO,MAAM,GAAG,KAAK;AACpB8G,MAAAA,mBAAmB,EAAE;AAErB1K,MAAAA,gBAAgB,CAACW,cAAc,CAACT,SAAS,EAAEC,IAAI,CAAC;AAEhD,MAAA,IAAMqL,YAAY,GAAGvH,SAAS,CAACsH,OAAO,EAAE,cAAc,CAAC;AACvD,MAAA,IAAME,gBAAgB,GAAGxH,SAAS,CAACsH,OAAO,EAAE,kBAAkB,CAAC;AAC/D,MAAA,IAAMG,mBAAmB,GAAGzH,SAAS,CAACsH,OAAO,EAAE,qBAAqB,CAAC;MACrE,IAAM7C,WAAW,GAAGzE,SAAS,CAC3BsH,OAAO,EACP,aAAa,EACb,yBACF,CAAC;AAEDC,MAAAA,YAAY,KAAZA,IAAAA,IAAAA,YAAY,KAAZA,MAAAA,IAAAA,YAAY,EAAI;AAEhB,MAAA,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,GAAS;AAC/BlK,QAAAA,KAAK,CAAC,YAAM;AACV,UAAA,IAAIgH,WAAW,EAAE;AACfrB,YAAAA,SAAQ,CAACG,kBAAkB,CAACnE,KAAK,CAACI,2BAA2B,CAAC,CAAC;AACjE;AACAgI,UAAAA,gBAAgB,KAAhBA,IAAAA,IAAAA,gBAAgB,KAAhBA,MAAAA,IAAAA,gBAAgB,EAAI;AACtB,SAAC,CAAC;OACH;MAED,IAAI/C,WAAW,IAAIgD,mBAAmB,EAAE;AACtCA,QAAAA,mBAAmB,CACjBlE,kBAAkB,CAACnE,KAAK,CAACI,2BAA2B,CACtD,CAAC,CAAC4H,IAAI,CAACO,kBAAkB,EAAEA,kBAAkB,CAAC;AAC9C,QAAA,OAAO,IAAI;AACb;AAEAA,MAAAA,kBAAkB,EAAE;AACpB,MAAA,OAAO,IAAI;KACZ;AAEDC,IAAAA,KAAK,EAALA,SAAAA,KAAKA,CAACC,YAAY,EAAE;AAClB,MAAA,IAAI,CAACzI,KAAK,CAACM,MAAM,EAAE;AACjB,QAAA,OAAO,IAAI;AACb;MAEAN,KAAK,CAACQ,cAAc,GAAG,IAAI;AAE3B,MAAA,OAAO,IAAI,CAACvD,eAAe,CAAC,IAAI,EAAEwL,YAAY,CAAC;KAChD;AAEDC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,cAAc,EAAE;AACtB,MAAA,IAAI,CAAC3I,KAAK,CAACM,MAAM,EAAE;AACjB,QAAA,OAAO,IAAI;AACb;MAEAN,KAAK,CAACQ,cAAc,GAAG,KAAK;MAE5B,IAAI3D,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;AAC5C,QAAA,OAAO,IAAI;AACb;AAEA,MAAA,OAAO,IAAI,CAACE,eAAe,CAAC,KAAK,EAAE0L,cAAc,CAAC;KACnD;AAEDC,IAAAA,uBAAuB,EAAvBA,SAAAA,uBAAuBA,CAACC,iBAAiB,EAAE;AACzC,MAAA,IAAMC,eAAe,GAAG,EAAE,CAAC7G,MAAM,CAAC4G,iBAAiB,CAAC,CAACjF,MAAM,CAACmF,OAAO,CAAC;MAEpE/I,KAAK,CAACC,UAAU,GAAG6I,eAAe,CAAClG,GAAG,CAAC,UAAC3B,OAAO,EAAA;AAAA,QAAA,OAC7C,OAAOA,OAAO,KAAK,QAAQ,GAAGxB,GAAG,CAACyC,aAAa,CAACjB,OAAO,CAAC,GAAGA,OAAO;AAAA,OACpE,CAAC;MAED,IAAIjB,KAAK,CAACM,MAAM,EAAE;AAChBqC,QAAAA,mBAAmB,EAAE;AACvB;AAEA0E,MAAAA,mBAAmB,EAAE;AAErB,MAAA,OAAO,IAAI;AACb;GACD;AAED2B,EAAAA,MAAM,CAACC,gBAAgB,CAACnM,IAAI,EAAE;AAC5BS,IAAAA,iBAAiB,EAAE;MACjBkB,KAAK,EAAA,SAALA,KAAKA,GAAG;QACN,OAAOuB,KAAK,CAACQ,cAAc;AAC7B;KACD;AACDvD,IAAAA,eAAe,EAAE;AACfwB,MAAAA,KAAK,WAALA,KAAKA,CAAC8B,MAAM,EAAE2H,OAAO,EAAE;AACrB,QAAA,IAAIlI,KAAK,CAACO,MAAM,KAAKA,MAAM,EAAE;AAC3B,UAAA,OAAO,IAAI;AACb;QAEAP,KAAK,CAACO,MAAM,GAAGA,MAAM;AACrB,QAAA,IAAIA,MAAM,EAAE;AACV,UAAA,IAAM2I,OAAO,GAAGtI,SAAS,CAACsH,OAAO,EAAE,SAAS,CAAC;AAC7C,UAAA,IAAMiB,WAAW,GAAGvI,SAAS,CAACsH,OAAO,EAAE,aAAa,CAAC;AACrDgB,UAAAA,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAPA,MAAAA,IAAAA,OAAO,EAAI;AAEXxC,UAAAA,eAAe,EAAE;AACjBW,UAAAA,mBAAmB,EAAE;AAErB8B,UAAAA,WAAW,KAAXA,IAAAA,IAAAA,WAAW,KAAXA,MAAAA,IAAAA,WAAW,EAAI;AACjB,SAAC,MAAM;AACL,UAAA,IAAMC,SAAS,GAAGxI,SAAS,CAACsH,OAAO,EAAE,WAAW,CAAC;AACjD,UAAA,IAAMmB,aAAa,GAAGzI,SAAS,CAACsH,OAAO,EAAE,eAAe,CAAC;AAEzDkB,UAAAA,SAAS,KAATA,IAAAA,IAAAA,SAAS,KAATA,MAAAA,IAAAA,SAAS,EAAI;AAEbzG,UAAAA,mBAAmB,EAAE;AACrB2D,UAAAA,YAAY,EAAE;AACde,UAAAA,mBAAmB,EAAE;AAErBgC,UAAAA,aAAa,KAAbA,IAAAA,IAAAA,aAAa,KAAbA,MAAAA,IAAAA,aAAa,EAAI;AACnB;AAEA,QAAA,OAAO,IAAI;AACb;AACF;AACF,GAAC,CAAC;;AAEF;AACAvM,EAAAA,IAAI,CAAC8L,uBAAuB,CAACrJ,QAAQ,CAAC;AAEtC,EAAA,OAAOzC,IAAI;AACb;;;;"}