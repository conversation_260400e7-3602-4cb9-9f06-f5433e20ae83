{"version": 3, "file": "array.cjs", "sources": ["../../src/array.ts"], "sourcesContent": ["import { isArray, isFunction } from './typed'\n\n/**\n * Sorts an array of items into groups. The return value is a map where the keys are\n * the group ids the given getGroupId function produced and the value is an array of\n * each item in that group.\n */\nexport const group = <T, Key extends string | number | symbol>(\n  array: readonly T[],\n  getGroupId: (item: T) => Key\n): Partial<Record<Key, T[]>> => {\n  return array.reduce((acc, item) => {\n    const groupId = getGroupId(item)\n    if (!acc[groupId]) acc[groupId] = []\n    acc[groupId].push(item)\n    return acc\n  }, {} as Record<Key, T[]>)\n}\n\n/**\n * Creates an array of grouped elements, the first of which contains the\n * first elements of the given arrays, the second of which contains the\n * second elements of the given arrays, and so on.\n *\n * Ex. const zipped = zip(['a', 'b'], [1, 2], [true, false]) // [['a', 1, true], ['b', 2, false]]\n */\nexport function zip<T1, T2, T3, T4, T5>(\n  array1: T1[],\n  array2: T2[],\n  array3: T3[],\n  array4: T4[],\n  array5: T5[]\n): [T1, T2, T3, T4, T5][]\nexport function zip<T1, T2, T3, T4>(\n  array1: T1[],\n  array2: T2[],\n  array3: T3[],\n  array4: T4[]\n): [T1, T2, T3, T4][]\nexport function zip<T1, T2, T3>(\n  array1: T1[],\n  array2: T2[],\n  array3: T3[]\n): [T1, T2, T3][]\nexport function zip<T1, T2>(array1: T1[], array2: T2[]): [T1, T2][]\nexport function zip<T>(...arrays: T[][]): T[][] {\n  if (!arrays || !arrays.length) return []\n  return new Array(Math.max(...arrays.map(({ length }) => length)))\n    .fill([])\n    .map((_, idx) => arrays.map(array => array[idx]))\n}\n\n/**\n * Creates an object mapping the specified keys to their corresponding values\n *\n * Ex. const zipped = zipToObject(['a', 'b'], [1, 2]) // { a: 1, b: 2 }\n * Ex. const zipped = zipToObject(['a', 'b'], (k, i) => k + i) // { a: 'a0', b: 'b1' }\n * Ex. const zipped = zipToObject(['a', 'b'], 1) // { a: 1, b: 1 }\n */\nexport function zipToObject<K extends string | number | symbol, V>(\n  keys: K[],\n  values: V | ((key: K, idx: number) => V) | V[]\n): Record<K, V> {\n  if (!keys || !keys.length) {\n    return {} as Record<K, V>\n  }\n\n  const getValue = isFunction(values)\n    ? values\n    : isArray(values)\n    ? (_k: K, i: number) => values[i]\n    : (_k: K, _i: number) => values\n\n  return keys.reduce((acc, key, idx) => {\n    acc[key] = getValue(key, idx)\n    return acc\n  }, {} as Record<K, V>)\n}\n\n/**\n * Go through a list of items, starting with the first item,\n * and comparing with the second. Keep the one you want then\n * compare that to the next item in the list with the same\n *\n * Ex. const greatest = () => boil(numbers, (a, b) => a > b)\n */\nexport const boil = <T>(\n  array: readonly T[],\n  compareFunc: (a: T, b: T) => T\n) => {\n  if (!array || (array.length ?? 0) === 0) return null\n  return array.reduce(compareFunc)\n}\n\n/**\n * Sum all numbers in an array. Optionally provide a function\n * to convert objects in the array to number values.\n */\nexport function sum<T extends number>(array: readonly T[]): number\nexport function sum<T extends object>(\n  array: readonly T[],\n  fn: (item: T) => number\n): number\nexport function sum<T extends object | number>(\n  array: readonly any[],\n  fn?: (item: T) => number\n): number {\n  return (array || []).reduce((acc, item) => acc + (fn ? fn(item) : item), 0)\n}\n\n/**\n * Get the first item in an array or a default value\n */\nexport const first = <T>(\n  array: readonly T[],\n  defaultValue: T | null | undefined = undefined\n) => {\n  return array?.length > 0 ? array[0] : defaultValue\n}\n\n/**\n * Get the last item in an array or a default value\n */\nexport const last = <T>(\n  array: readonly T[],\n  defaultValue: T | null | undefined = undefined\n) => {\n  return array?.length > 0 ? array[array.length - 1] : defaultValue\n}\n\n/**\n * Sort an array without modifying it and return\n * the newly sorted value\n */\nexport const sort = <T>(\n  array: readonly T[],\n  getter: (item: T) => number,\n  desc = false\n) => {\n  if (!array) return []\n  const asc = (a: T, b: T) => getter(a) - getter(b)\n  const dsc = (a: T, b: T) => getter(b) - getter(a)\n  return array.slice().sort(desc === true ? dsc : asc)\n}\n\n/**\n * Sort an array without modifying it and return\n * the newly sorted value. Allows for a string\n * sorting value.\n */\nexport const alphabetical = <T>(\n  array: readonly T[],\n  getter: (item: T) => string,\n  dir: 'asc' | 'desc' = 'asc'\n) => {\n  if (!array) return []\n  const asc = (a: T, b: T) => `${getter(a)}`.localeCompare(getter(b))\n  const dsc = (a: T, b: T) => `${getter(b)}`.localeCompare(getter(a))\n  return array.slice().sort(dir === 'desc' ? dsc : asc)\n}\n\nexport const counting = <T, TId extends string | number | symbol>(\n  list: readonly T[],\n  identity: (item: T) => TId\n): Record<TId, number> => {\n  if (!list) return {} as Record<TId, number>\n  return list.reduce((acc, item) => {\n    const id = identity(item)\n    acc[id] = (acc[id] ?? 0) + 1\n    return acc\n  }, {} as Record<TId, number>)\n}\n\n/**\n * Replace an element in an array with a new\n * item without modifying the array and return\n * the new value\n */\nexport const replace = <T>(\n  list: readonly T[],\n  newItem: T,\n  match: (item: T, idx: number) => boolean\n): T[] => {\n  if (!list) return []\n  if (newItem === undefined) return [...list]\n  for (let idx = 0; idx < list.length; idx++) {\n    const item = list[idx]\n    if (match(item, idx)) {\n      return [\n        ...list.slice(0, idx),\n        newItem,\n        ...list.slice(idx + 1, list.length)\n      ]\n    }\n  }\n  return [...list]\n}\n\n/**\n * Convert an array to a dictionary by mapping each item\n * into a dictionary key & value\n */\nexport const objectify = <T, Key extends string | number | symbol, Value = T>(\n  array: readonly T[],\n  getKey: (item: T) => Key,\n  getValue: (item: T) => Value = item => item as unknown as Value\n): Record<Key, Value> => {\n  return array.reduce((acc, item) => {\n    acc[getKey(item)] = getValue(item)\n    return acc\n  }, {} as Record<Key, Value>)\n}\n\n/**\n * Select performs a filter and a mapper inside of a reduce,\n * only iterating the list one time.\n *\n * @example\n * select([1, 2, 3, 4], x => x*x, x > 2) == [9, 16]\n */\nexport const select = <T, K>(\n  array: readonly T[],\n  mapper: (item: T, index: number) => K,\n  condition: (item: T, index: number) => boolean\n) => {\n  if (!array) return []\n  return array.reduce((acc, item, index) => {\n    if (!condition(item, index)) return acc\n    acc.push(mapper(item, index))\n    return acc\n  }, [] as K[])\n}\n\n/**\n * Max gets the greatest value from a list\n *\n * @example\n * max([ 2, 3, 5]) == 5\n * max([{ num: 1 }, { num: 2 }], x => x.num) == { num: 2 }\n */\nexport function max(array: readonly [number, ...number[]]): number\nexport function max(array: readonly number[]): number | null\nexport function max<T>(\n  array: readonly T[],\n  getter: (item: T) => number\n): T | null\nexport function max<T>(\n  array: readonly T[],\n  getter?: (item: T) => number\n): T | null {\n  const get = getter ?? ((v: any) => v)\n  return boil(array, (a, b) => (get(a) > get(b) ? a : b))\n}\n\n/**\n * Min gets the smallest value from a list\n *\n * @example\n * min([1, 2, 3, 4]) == 1\n * min([{ num: 1 }, { num: 2 }], x => x.num) == { num: 1 }\n */\nexport function min(array: readonly [number, ...number[]]): number\nexport function min(array: readonly number[]): number | null\nexport function min<T>(\n  array: readonly T[],\n  getter: (item: T) => number\n): T | null\nexport function min<T>(\n  array: readonly T[],\n  getter?: (item: T) => number\n): T | null {\n  const get = getter ?? ((v: any) => v)\n  return boil(array, (a, b) => (get(a) < get(b) ? a : b))\n}\n\n/**\n * Splits a single list into many lists of the desired size. If\n * given a list of 10 items and a size of 2, it will return 5\n * lists with 2 items each\n */\nexport const cluster = <T>(list: readonly T[], size: number = 2): T[][] => {\n  const clusterCount = Math.ceil(list.length / size)\n  return new Array(clusterCount).fill(null).map((_c: null, i: number) => {\n    return list.slice(i * size, i * size + size)\n  })\n}\n\n/**\n * Given a list of items returns a new list with only\n * unique items. Accepts an optional identity function\n * to convert each item in the list to a comparable identity\n * value\n */\nexport const unique = <T, K extends string | number | symbol>(\n  array: readonly T[],\n  toKey?: (item: T) => K\n): T[] => {\n  const valueMap = array.reduce((acc, item) => {\n    const key = toKey ? toKey(item) : (item as any as string | number | symbol)\n    if (acc[key]) return acc\n    acc[key] = item\n    return acc\n  }, {} as Record<string | number | symbol, T>)\n  return Object.values(valueMap)\n}\n\n/**\n * Creates a generator that will produce an iteration through\n * the range of number as requested.\n *\n * @example\n * range(3)                  // yields 0, 1, 2, 3\n * range(0, 3)               // yields 0, 1, 2, 3\n * range(0, 3, 'y')          // yields y, y, y, y\n * range(0, 3, () => 'y')    // yields y, y, y, y\n * range(0, 3, i => i)       // yields 0, 1, 2, 3\n * range(0, 3, i => `y${i}`) // yields y0, y1, y2, y3\n * range(0, 3, obj)          // yields obj, obj, obj, obj\n * range(0, 6, i => i, 2)    // yields 0, 2, 4, 6\n */\nexport function* range<T = number>(\n  startOrLength: number,\n  end?: number,\n  valueOrMapper: T | ((i: number) => T) = i => i as T,\n  step: number = 1\n): Generator<T> {\n  const mapper = isFunction(valueOrMapper) ? valueOrMapper : () => valueOrMapper\n  const start = end ? startOrLength : 0\n  const final = end ?? startOrLength\n  for (let i = start; i <= final; i += step) {\n    yield mapper(i)\n    if (i + step > final) break\n  }\n}\n\n/**\n * Creates a list of given start, end, value, and\n * step parameters.\n *\n * @example\n * list(3)                  // 0, 1, 2, 3\n * list(0, 3)               // 0, 1, 2, 3\n * list(0, 3, 'y')          // y, y, y, y\n * list(0, 3, () => 'y')    // y, y, y, y\n * list(0, 3, i => i)       // 0, 1, 2, 3\n * list(0, 3, i => `y${i}`) // y0, y1, y2, y3\n * list(0, 3, obj)          // obj, obj, obj, obj\n * list(0, 6, i => i, 2)    // 0, 2, 4, 6\n */\nexport const list = <T = number>(\n  startOrLength: number,\n  end?: number,\n  valueOrMapper?: T | ((i: number) => T),\n  step?: number\n): T[] => {\n  return Array.from(range(startOrLength, end, valueOrMapper, step))\n}\n\n/**\n * Given an array of arrays, returns a single\n * dimentional array with all items in it.\n */\nexport const flat = <T>(lists: readonly T[][]): T[] => {\n  return lists.reduce((acc, list) => {\n    acc.push(...list)\n    return acc\n  }, [])\n}\n\n/**\n * Given two arrays, returns true if any\n * elements intersect\n */\nexport const intersects = <T, K extends string | number | symbol>(\n  listA: readonly T[],\n  listB: readonly T[],\n  identity?: (t: T) => K\n): boolean => {\n  if (!listA || !listB) return false\n  const ident = identity ?? ((x: T) => x as unknown as K)\n  const dictB = listB.reduce((acc, item) => {\n    acc[ident(item)] = true\n    return acc\n  }, {} as Record<string | number | symbol, boolean>)\n  return listA.some(value => dictB[ident(value)])\n}\n\n/**\n * Split an array into two array based on\n * a true/false condition function\n */\nexport const fork = <T>(\n  list: readonly T[],\n  condition: (item: T) => boolean\n): [T[], T[]] => {\n  if (!list) return [[], []]\n  return list.reduce(\n    (acc, item) => {\n      const [a, b] = acc\n      if (condition(item)) {\n        return [[...a, item], b]\n      } else {\n        return [a, [...b, item]]\n      }\n    },\n    [[], []] as [T[], T[]]\n  )\n}\n\n/**\n * Given two lists of the same type, iterate the first list\n * and replace items matched by the matcher func in the\n * first place.\n */\nexport const merge = <T>(\n  root: readonly T[],\n  others: readonly T[],\n  matcher: (item: T) => any\n) => {\n  if (!others && !root) return []\n  if (!others) return root\n  if (!root) return []\n  if (!matcher) return root\n  return root.reduce((acc, r) => {\n    const matched = others.find(o => matcher(r) === matcher(o))\n    if (matched) acc.push(matched)\n    else acc.push(r)\n    return acc\n  }, [] as T[])\n}\n\n/**\n * Replace an item in an array by a match function condition. If\n * no items match the function condition, appends the new item to\n * the end of the list.\n */\nexport const replaceOrAppend = <T>(\n  list: readonly T[],\n  newItem: T,\n  match: (a: T, idx: number) => boolean\n) => {\n  if (!list && !newItem) return []\n  if (!newItem) return [...list]\n  if (!list) return [newItem]\n  for (let idx = 0; idx < list.length; idx++) {\n    const item = list[idx]\n    if (match(item, idx)) {\n      return [\n        ...list.slice(0, idx),\n        newItem,\n        ...list.slice(idx + 1, list.length)\n      ]\n    }\n  }\n  return [...list, newItem]\n}\n\n/**\n * If the item matching the condition already exists\n * in the list it will be removed. If it does not it\n * will be added.\n */\nexport const toggle = <T>(\n  list: readonly T[],\n  item: T,\n  /**\n   * Converts an item of type T item into a value that\n   * can be checked for equality\n   */\n  toKey?: null | ((item: T, idx: number) => number | string | symbol),\n  options?: {\n    strategy?: 'prepend' | 'append'\n  }\n) => {\n  if (!list && !item) return []\n  if (!list) return [item]\n  if (!item) return [...list]\n  const matcher = toKey\n    ? (x: T, idx: number) => toKey(x, idx) === toKey(item, idx)\n    : (x: T) => x === item\n  const existing = list.find(matcher)\n  if (existing) return list.filter((x, idx) => !matcher(x, idx))\n  const strategy = options?.strategy ?? 'append'\n  if (strategy === 'append') return [...list, item]\n  return [item, ...list]\n}\n\ntype Falsy = null | undefined | false | '' | 0 | 0n\n\n/**\n * Given a list returns a new list with\n * only truthy values\n */\nexport const sift = <T>(list: readonly (T | Falsy)[]): T[] => {\n  return (list?.filter(x => !!x) as T[]) ?? []\n}\n\n/**\n * Like a reduce but does not require an array.\n * Only need a number and will iterate the function\n * as many times as specified.\n *\n * NOTE: This is NOT zero indexed. If you pass count=5\n * you will get 1, 2, 3, 4, 5 iteration in the callback\n * function\n */\nexport const iterate = <T>(\n  count: number,\n  func: (currentValue: T, iteration: number) => T,\n  initValue: T\n) => {\n  let value = initValue\n  for (let i = 1; i <= count; i++) {\n    value = func(value, i)\n  }\n  return value\n}\n\n/**\n * Returns all items from the first list that\n * do not exist in the second list.\n */\nexport const diff = <T>(\n  root: readonly T[],\n  other: readonly T[],\n  identity: (item: T) => string | number | symbol = (t: T) =>\n    t as unknown as string | number | symbol\n): T[] => {\n  if (!root?.length && !other?.length) return []\n  if (root?.length === undefined) return [...other]\n  if (!other?.length) return [...root]\n  const bKeys = other.reduce((acc, item) => {\n    acc[identity(item)] = true\n    return acc\n  }, {} as Record<string | number | symbol, boolean>)\n  return root.filter(a => !bKeys[identity(a)])\n}\n\n/**\n * Shift array items by n steps\n * If n > 0 items will shift n steps to the right\n * If n < 0 items will shift n steps to the left\n */\nexport function shift<T>(arr: Array<T>, n: number) {\n  if (arr.length === 0) return arr\n\n  const shiftNumber = n % arr.length\n\n  if (shiftNumber === 0) return arr\n\n  return [...arr.slice(-shiftNumber, arr.length), ...arr.slice(0, -shiftNumber)]\n}\n"], "names": ["isFunction", "isArray", "list"], "mappings": ";;;;AAOa,MAAA,KAAA,GAAQ,CACnB,KAAA,EACA,UAC8B,KAAA;AAC9B,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA;AACjC,IAAM,MAAA,OAAA,GAAU,WAAW,IAAI,CAAA,CAAA;AAC/B,IAAA,IAAI,CAAC,GAAI,CAAA,OAAA,CAAA;AAAU,MAAA,GAAA,CAAI,WAAW,EAAC,CAAA;AACnC,IAAI,GAAA,CAAA,OAAA,CAAA,CAAS,KAAK,IAAI,CAAA,CAAA;AACtB,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAsB,CAAA,CAAA;AAC3B,EAAA;AA4BO,SAAS,OAAU,MAAsB,EAAA;AAC9C,EAAI,IAAA,CAAC,MAAU,IAAA,CAAC,MAAO,CAAA,MAAA;AAAQ,IAAA,OAAO,EAAC,CAAA;AACvC,EAAA,OAAO,IAAI,KAAA,CAAM,IAAK,CAAA,GAAA,CAAI,GAAG,MAAA,CAAO,GAAI,CAAA,CAAC,EAAE,MAAA,EAAa,KAAA,MAAM,CAAC,CAAC,CAC7D,CAAA,IAAA,CAAK,EAAE,CACP,CAAA,GAAA,CAAI,CAAC,CAAA,EAAG,GAAQ,KAAA,MAAA,CAAO,GAAI,CAAA,CAAA,KAAA,KAAS,KAAM,CAAA,GAAA,CAAI,CAAC,CAAA,CAAA;AACpD,CAAA;AASgB,SAAA,WAAA,CACd,MACA,MACc,EAAA;AACd,EAAA,IAAI,CAAC,IAAA,IAAQ,CAAC,IAAA,CAAK,MAAQ,EAAA;AACzB,IAAA,OAAO,EAAC,CAAA;AAAA,GACV;AAEA,EAAA,MAAM,QAAW,GAAAA,gBAAA,CAAW,MAAM,CAAA,GAC9B,SACAC,aAAQ,CAAA,MAAM,CACd,GAAA,CAAC,IAAO,CAAc,KAAA,MAAA,CAAO,CAC7B,CAAA,GAAA,CAAC,IAAO,EAAe,KAAA,MAAA,CAAA;AAE3B,EAAA,OAAO,IAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,KAAK,GAAQ,KAAA;AACpC,IAAI,GAAA,CAAA,GAAA,CAAA,GAAO,QAAS,CAAA,GAAA,EAAK,GAAG,CAAA,CAAA;AAC5B,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAkB,CAAA,CAAA;AACvB,CAAA;AASa,MAAA,IAAA,GAAO,CAClB,KAAA,EACA,WACG,KAAA;AACH,EAAA,IAAI,CAAC,KAAA,IAAA,CAAU,KAAM,CAAA,MAAA,IAAU,CAAO,MAAA,CAAA;AAAG,IAAO,OAAA,IAAA,CAAA;AAChD,EAAO,OAAA,KAAA,CAAM,OAAO,WAAW,CAAA,CAAA;AACjC,EAAA;AAWgB,SAAA,GAAA,CACd,OACA,EACQ,EAAA;AACR,EAAA,OAAA,CAAQ,KAAS,IAAA,EAAI,EAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA,GAAA,IAAO,EAAK,GAAA,EAAA,CAAG,IAAI,CAAA,GAAI,OAAO,CAAC,CAAA,CAAA;AAC5E,CAAA;AAKO,MAAM,KAAQ,GAAA,CACnB,KACA,EAAA,YAAA,GAAqC,KAClC,CAAA,KAAA;AACH,EAAA,OAAO,KAAO,EAAA,MAAA,GAAS,CAAI,GAAA,KAAA,CAAM,CAAK,CAAA,GAAA,YAAA,CAAA;AACxC,EAAA;AAKO,MAAM,IAAO,GAAA,CAClB,KACA,EAAA,YAAA,GAAqC,KAClC,CAAA,KAAA;AACH,EAAA,OAAO,OAAO,MAAS,GAAA,CAAA,GAAI,KAAM,CAAA,KAAA,CAAM,SAAS,CAAK,CAAA,GAAA,YAAA,CAAA;AACvD,EAAA;AAMO,MAAM,IAAO,GAAA,CAClB,KACA,EAAA,MAAA,EACA,OAAO,KACJ,KAAA;AACH,EAAA,IAAI,CAAC,KAAA;AAAO,IAAA,OAAO,EAAC,CAAA;AACpB,EAAM,MAAA,GAAA,GAAM,CAAC,CAAM,EAAA,CAAA,KAAS,OAAO,CAAC,CAAA,GAAI,OAAO,CAAC,CAAA,CAAA;AAChD,EAAM,MAAA,GAAA,GAAM,CAAC,CAAM,EAAA,CAAA,KAAS,OAAO,CAAC,CAAA,GAAI,OAAO,CAAC,CAAA,CAAA;AAChD,EAAA,OAAO,MAAM,KAAM,EAAA,CAAE,KAAK,IAAS,KAAA,IAAA,GAAO,MAAM,GAAG,CAAA,CAAA;AACrD,EAAA;AAOO,MAAM,YAAe,GAAA,CAC1B,KACA,EAAA,MAAA,EACA,MAAsB,KACnB,KAAA;AACH,EAAA,IAAI,CAAC,KAAA;AAAO,IAAA,OAAO,EAAC,CAAA;AACpB,EAAM,MAAA,GAAA,GAAM,CAAC,CAAA,EAAM,CAAS,KAAA,CAAA,EAAG,MAAO,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,aAAA,CAAc,MAAO,CAAA,CAAC,CAAC,CAAA,CAAA;AAClE,EAAM,MAAA,GAAA,GAAM,CAAC,CAAA,EAAM,CAAS,KAAA,CAAA,EAAG,MAAO,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,aAAA,CAAc,MAAO,CAAA,CAAC,CAAC,CAAA,CAAA;AAClE,EAAA,OAAO,MAAM,KAAM,EAAA,CAAE,KAAK,GAAQ,KAAA,MAAA,GAAS,MAAM,GAAG,CAAA,CAAA;AACtD,EAAA;AAEa,MAAA,QAAA,GAAW,CACtBC,KAAAA,EACA,QACwB,KAAA;AACxB,EAAA,IAAI,CAACA,KAAAA;AAAM,IAAA,OAAO,EAAC,CAAA;AACnB,EAAA,OAAOA,KAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA;AAChC,IAAM,MAAA,EAAA,GAAK,SAAS,IAAI,CAAA,CAAA;AACxB,IAAI,GAAA,CAAA,EAAA,CAAA,GAAA,CAAO,GAAI,CAAA,EAAA,CAAA,IAAO,CAAK,IAAA,CAAA,CAAA;AAC3B,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAyB,CAAA,CAAA;AAC9B,EAAA;AAOO,MAAM,OAAU,GAAA,CACrBA,KACA,EAAA,OAAA,EACA,KACQ,KAAA;AACR,EAAA,IAAI,CAACA,KAAAA;AAAM,IAAA,OAAO,EAAC,CAAA;AACnB,EAAA,IAAI,OAAY,KAAA,KAAA,CAAA;AAAW,IAAO,OAAA,CAAC,GAAGA,KAAI,CAAA,CAAA;AAC1C,EAAA,KAAA,IAAS,GAAM,GAAA,CAAA,EAAG,GAAMA,GAAAA,KAAAA,CAAK,QAAQ,GAAO,EAAA,EAAA;AAC1C,IAAA,MAAM,OAAOA,KAAK,CAAA,GAAA,CAAA,CAAA;AAClB,IAAI,IAAA,KAAA,CAAM,IAAM,EAAA,GAAG,CAAG,EAAA;AACpB,MAAO,OAAA;AAAA,QACL,GAAGA,KAAAA,CAAK,KAAM,CAAA,CAAA,EAAG,GAAG,CAAA;AAAA,QACpB,OAAA;AAAA,QACA,GAAGA,KAAK,CAAA,KAAA,CAAM,GAAM,GAAA,CAAA,EAAGA,MAAK,MAAM,CAAA;AAAA,OACpC,CAAA;AAAA,KACF;AAAA,GACF;AACA,EAAO,OAAA,CAAC,GAAGA,KAAI,CAAA,CAAA;AACjB,EAAA;AAMO,MAAM,YAAY,CACvB,KAAA,EACA,MACA,EAAA,QAAA,GAA+B,UAAQ,IAChB,KAAA;AACvB,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA;AACjC,IAAA,GAAA,CAAI,MAAO,CAAA,IAAI,CAAK,CAAA,GAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AACjC,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAwB,CAAA,CAAA;AAC7B,EAAA;AASO,MAAM,MAAS,GAAA,CACpB,KACA,EAAA,MAAA,EACA,SACG,KAAA;AACH,EAAA,IAAI,CAAC,KAAA;AAAO,IAAA,OAAO,EAAC,CAAA;AACpB,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,MAAM,KAAU,KAAA;AACxC,IAAI,IAAA,CAAC,SAAU,CAAA,IAAA,EAAM,KAAK,CAAA;AAAG,MAAO,OAAA,GAAA,CAAA;AACpC,IAAA,GAAA,CAAI,IAAK,CAAA,MAAA,CAAO,IAAM,EAAA,KAAK,CAAC,CAAA,CAAA;AAC5B,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAS,CAAA,CAAA;AACd,EAAA;AAegB,SAAA,GAAA,CACd,OACA,MACU,EAAA;AACV,EAAM,MAAA,GAAA,GAAM,MAAW,KAAA,CAAC,CAAW,KAAA,CAAA,CAAA,CAAA;AACnC,EAAA,OAAO,IAAK,CAAA,KAAA,EAAO,CAAC,CAAA,EAAG,CAAO,KAAA,GAAA,CAAI,CAAC,CAAA,GAAI,GAAI,CAAA,CAAC,CAAI,GAAA,CAAA,GAAI,CAAE,CAAA,CAAA;AACxD,CAAA;AAegB,SAAA,GAAA,CACd,OACA,MACU,EAAA;AACV,EAAM,MAAA,GAAA,GAAM,MAAW,KAAA,CAAC,CAAW,KAAA,CAAA,CAAA,CAAA;AACnC,EAAA,OAAO,IAAK,CAAA,KAAA,EAAO,CAAC,CAAA,EAAG,CAAO,KAAA,GAAA,CAAI,CAAC,CAAA,GAAI,GAAI,CAAA,CAAC,CAAI,GAAA,CAAA,GAAI,CAAE,CAAA,CAAA;AACxD,CAAA;AAOO,MAAM,OAAU,GAAA,CAAIA,KAAoB,EAAA,IAAA,GAAe,CAAa,KAAA;AACzE,EAAA,MAAM,YAAe,GAAA,IAAA,CAAK,IAAKA,CAAAA,KAAAA,CAAK,SAAS,IAAI,CAAA,CAAA;AACjD,EAAO,OAAA,IAAI,KAAM,CAAA,YAAY,CAAE,CAAA,IAAA,CAAK,IAAI,CAAE,CAAA,GAAA,CAAI,CAAC,EAAA,EAAU,CAAc,KAAA;AACrE,IAAA,OAAOA,MAAK,KAAM,CAAA,CAAA,GAAI,IAAM,EAAA,CAAA,GAAI,OAAO,IAAI,CAAA,CAAA;AAAA,GAC5C,CAAA,CAAA;AACH,EAAA;AAQa,MAAA,MAAA,GAAS,CACpB,KAAA,EACA,KACQ,KAAA;AACR,EAAA,MAAM,QAAW,GAAA,KAAA,CAAM,MAAO,CAAA,CAAC,KAAK,IAAS,KAAA;AAC3C,IAAA,MAAM,GAAM,GAAA,KAAA,GAAQ,KAAM,CAAA,IAAI,CAAK,GAAA,IAAA,CAAA;AACnC,IAAA,IAAI,GAAI,CAAA,GAAA,CAAA;AAAM,MAAO,OAAA,GAAA,CAAA;AACrB,IAAA,GAAA,CAAI,GAAO,CAAA,GAAA,IAAA,CAAA;AACX,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAyC,CAAA,CAAA;AAC5C,EAAO,OAAA,MAAA,CAAO,OAAO,QAAQ,CAAA,CAAA;AAC/B,EAAA;AAgBO,UAAU,MACf,aACA,EAAA,GAAA,EACA,gBAAwC,CAAK,CAAA,KAAA,CAAA,EAC7C,OAAe,CACD,EAAA;AACd,EAAA,MAAM,MAAS,GAAAF,gBAAA,CAAW,aAAa,CAAA,GAAI,gBAAgB,MAAM,aAAA,CAAA;AACjE,EAAM,MAAA,KAAA,GAAQ,MAAM,aAAgB,GAAA,CAAA,CAAA;AACpC,EAAA,MAAM,QAAQ,GAAO,IAAA,aAAA,CAAA;AACrB,EAAA,KAAA,IAAS,CAAI,GAAA,KAAA,EAAO,CAAK,IAAA,KAAA,EAAO,KAAK,IAAM,EAAA;AACzC,IAAA,MAAM,OAAO,CAAC,CAAA,CAAA;AACd,IAAA,IAAI,IAAI,IAAO,GAAA,KAAA;AAAO,MAAA,MAAA;AAAA,GACxB;AACF,CAAA;AAgBO,MAAM,IAAO,GAAA,CAClB,aACA,EAAA,GAAA,EACA,eACA,IACQ,KAAA;AACR,EAAA,OAAO,MAAM,IAAK,CAAA,KAAA,CAAM,eAAe,GAAK,EAAA,aAAA,EAAe,IAAI,CAAC,CAAA,CAAA;AAClE,EAAA;AAMa,MAAA,IAAA,GAAO,CAAI,KAA+B,KAAA;AACrD,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAKE,KAAS,KAAA;AACjC,IAAI,GAAA,CAAA,IAAA,CAAK,GAAGA,KAAI,CAAA,CAAA;AAChB,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAE,CAAA,CAAA;AACP,EAAA;AAMO,MAAM,UAAa,GAAA,CACxB,KACA,EAAA,KAAA,EACA,QACY,KAAA;AACZ,EAAI,IAAA,CAAC,SAAS,CAAC,KAAA;AAAO,IAAO,OAAA,KAAA,CAAA;AAC7B,EAAM,MAAA,KAAA,GAAQ,QAAa,KAAA,CAAC,CAAS,KAAA,CAAA,CAAA,CAAA;AACrC,EAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,MAAO,CAAA,CAAC,KAAK,IAAS,KAAA;AACxC,IAAI,GAAA,CAAA,KAAA,CAAM,IAAI,CAAK,CAAA,GAAA,IAAA,CAAA;AACnB,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAA+C,CAAA,CAAA;AAClD,EAAA,OAAO,MAAM,IAAK,CAAA,CAAA,KAAA,KAAS,KAAM,CAAA,KAAA,CAAM,KAAK,CAAE,CAAA,CAAA,CAAA;AAChD,EAAA;AAMa,MAAA,IAAA,GAAO,CAClBA,KAAAA,EACA,SACe,KAAA;AACf,EAAA,IAAI,CAACA,KAAAA;AAAM,IAAA,OAAO,CAAC,EAAI,EAAA,EAAE,CAAA,CAAA;AACzB,EAAA,OAAOA,KAAK,CAAA,MAAA;AAAA,IACV,CAAC,KAAK,IAAS,KAAA;AACb,MAAM,MAAA,CAAC,CAAG,EAAA,CAAC,CAAI,GAAA,GAAA,CAAA;AACf,MAAI,IAAA,SAAA,CAAU,IAAI,CAAG,EAAA;AACnB,QAAA,OAAO,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,GAAG,CAAC,CAAA,CAAA;AAAA,OAClB,MAAA;AACL,QAAA,OAAO,CAAC,CAAG,EAAA,CAAC,GAAG,CAAA,EAAG,IAAI,CAAC,CAAA,CAAA;AAAA,OACzB;AAAA,KACF;AAAA,IACA,CAAC,EAAI,EAAA,EAAE,CAAA;AAAA,GACT,CAAA;AACF,EAAA;AAOO,MAAM,KAAQ,GAAA,CACnB,IACA,EAAA,MAAA,EACA,OACG,KAAA;AACH,EAAI,IAAA,CAAC,UAAU,CAAC,IAAA;AAAM,IAAA,OAAO,EAAC,CAAA;AAC9B,EAAA,IAAI,CAAC,MAAA;AAAQ,IAAO,OAAA,IAAA,CAAA;AACpB,EAAA,IAAI,CAAC,IAAA;AAAM,IAAA,OAAO,EAAC,CAAA;AACnB,EAAA,IAAI,CAAC,OAAA;AAAS,IAAO,OAAA,IAAA,CAAA;AACrB,EAAA,OAAO,IAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,CAAM,KAAA;AAC7B,IAAM,MAAA,OAAA,GAAU,OAAO,IAAK,CAAA,CAAA,CAAA,KAAK,QAAQ,CAAC,CAAA,KAAM,OAAQ,CAAA,CAAC,CAAC,CAAA,CAAA;AAC1D,IAAI,IAAA,OAAA;AAAS,MAAA,GAAA,CAAI,KAAK,OAAO,CAAA,CAAA;AAAA;AACxB,MAAA,GAAA,CAAI,KAAK,CAAC,CAAA,CAAA;AACf,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAS,CAAA,CAAA;AACd,EAAA;AAOO,MAAM,eAAkB,GAAA,CAC7BA,KACA,EAAA,OAAA,EACA,KACG,KAAA;AACH,EAAI,IAAA,CAACA,SAAQ,CAAC,OAAA;AAAS,IAAA,OAAO,EAAC,CAAA;AAC/B,EAAA,IAAI,CAAC,OAAA;AAAS,IAAO,OAAA,CAAC,GAAGA,KAAI,CAAA,CAAA;AAC7B,EAAA,IAAI,CAACA,KAAAA;AAAM,IAAA,OAAO,CAAC,OAAO,CAAA,CAAA;AAC1B,EAAA,KAAA,IAAS,GAAM,GAAA,CAAA,EAAG,GAAMA,GAAAA,KAAAA,CAAK,QAAQ,GAAO,EAAA,EAAA;AAC1C,IAAA,MAAM,OAAOA,KAAK,CAAA,GAAA,CAAA,CAAA;AAClB,IAAI,IAAA,KAAA,CAAM,IAAM,EAAA,GAAG,CAAG,EAAA;AACpB,MAAO,OAAA;AAAA,QACL,GAAGA,KAAAA,CAAK,KAAM,CAAA,CAAA,EAAG,GAAG,CAAA;AAAA,QACpB,OAAA;AAAA,QACA,GAAGA,KAAK,CAAA,KAAA,CAAM,GAAM,GAAA,CAAA,EAAGA,MAAK,MAAM,CAAA;AAAA,OACpC,CAAA;AAAA,KACF;AAAA,GACF;AACA,EAAO,OAAA,CAAC,GAAGA,KAAAA,EAAM,OAAO,CAAA,CAAA;AAC1B,EAAA;AAOO,MAAM,MAAS,GAAA,CACpBA,KACA,EAAA,IAAA,EAKA,OACA,OAGG,KAAA;AACH,EAAI,IAAA,CAACA,SAAQ,CAAC,IAAA;AAAM,IAAA,OAAO,EAAC,CAAA;AAC5B,EAAA,IAAI,CAACA,KAAAA;AAAM,IAAA,OAAO,CAAC,IAAI,CAAA,CAAA;AACvB,EAAA,IAAI,CAAC,IAAA;AAAM,IAAO,OAAA,CAAC,GAAGA,KAAI,CAAA,CAAA;AAC1B,EAAA,MAAM,OAAU,GAAA,KAAA,GACZ,CAAC,CAAA,EAAM,QAAgB,KAAM,CAAA,CAAA,EAAG,GAAG,CAAA,KAAM,MAAM,IAAM,EAAA,GAAG,CACxD,GAAA,CAAC,MAAS,CAAM,KAAA,IAAA,CAAA;AACpB,EAAM,MAAA,QAAA,GAAWA,KAAK,CAAA,IAAA,CAAK,OAAO,CAAA,CAAA;AAClC,EAAI,IAAA,QAAA;AAAU,IAAOA,OAAAA,KAAAA,CAAK,OAAO,CAAC,CAAA,EAAG,QAAQ,CAAC,OAAA,CAAQ,CAAG,EAAA,GAAG,CAAC,CAAA,CAAA;AAC7D,EAAM,MAAA,QAAA,GAAW,SAAS,QAAY,IAAA,QAAA,CAAA;AACtC,EAAA,IAAI,QAAa,KAAA,QAAA;AAAU,IAAO,OAAA,CAAC,GAAGA,KAAAA,EAAM,IAAI,CAAA,CAAA;AAChD,EAAO,OAAA,CAAC,IAAM,EAAA,GAAGA,KAAI,CAAA,CAAA;AACvB,EAAA;AAQa,MAAA,IAAA,GAAO,CAAIA,KAAsC,KAAA;AAC5D,EAAA,OAAQA,OAAM,MAAO,CAAA,CAAA,CAAA,KAAK,CAAC,CAAC,CAAC,KAAa,EAAC,CAAA;AAC7C,EAAA;AAWO,MAAM,OAAU,GAAA,CACrB,KACA,EAAA,IAAA,EACA,SACG,KAAA;AACH,EAAA,IAAI,KAAQ,GAAA,SAAA,CAAA;AACZ,EAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAK,IAAA,KAAA,EAAO,CAAK,EAAA,EAAA;AAC/B,IAAQ,KAAA,GAAA,IAAA,CAAK,OAAO,CAAC,CAAA,CAAA;AAAA,GACvB;AACA,EAAO,OAAA,KAAA,CAAA;AACT,EAAA;AAMO,MAAM,OAAO,CAClB,IAAA,EACA,OACA,QAAkD,GAAA,CAAC,MACjD,CACM,KAAA;AACR,EAAA,IAAI,CAAC,IAAA,EAAM,MAAU,IAAA,CAAC,KAAO,EAAA,MAAA;AAAQ,IAAA,OAAO,EAAC,CAAA;AAC7C,EAAA,IAAI,MAAM,MAAW,KAAA,KAAA,CAAA;AAAW,IAAO,OAAA,CAAC,GAAG,KAAK,CAAA,CAAA;AAChD,EAAA,IAAI,CAAC,KAAO,EAAA,MAAA;AAAQ,IAAO,OAAA,CAAC,GAAG,IAAI,CAAA,CAAA;AACnC,EAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,MAAO,CAAA,CAAC,KAAK,IAAS,KAAA;AACxC,IAAI,GAAA,CAAA,QAAA,CAAS,IAAI,CAAK,CAAA,GAAA,IAAA,CAAA;AACtB,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAA+C,CAAA,CAAA;AAClD,EAAA,OAAO,KAAK,MAAO,CAAA,CAAA,CAAA,KAAK,CAAC,KAAM,CAAA,QAAA,CAAS,CAAC,CAAE,CAAA,CAAA,CAAA;AAC7C,EAAA;AAOgB,SAAA,KAAA,CAAS,KAAe,CAAW,EAAA;AACjD,EAAA,IAAI,IAAI,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,GAAA,CAAA;AAE7B,EAAM,MAAA,WAAA,GAAc,IAAI,GAAI,CAAA,MAAA,CAAA;AAE5B,EAAA,IAAI,WAAgB,KAAA,CAAA;AAAG,IAAO,OAAA,GAAA,CAAA;AAE9B,EAAA,OAAO,CAAC,GAAG,GAAI,CAAA,KAAA,CAAM,CAAC,WAAa,EAAA,GAAA,CAAI,MAAM,CAAA,EAAG,GAAG,GAAI,CAAA,KAAA,CAAM,CAAG,EAAA,CAAC,WAAW,CAAC,CAAA,CAAA;AAC/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}