{"version": 3, "file": "number.cjs", "sources": ["../../src/number.ts"], "sourcesContent": ["/**\n * Checks if the given number is between zero (0) and the ending number. 0 is inclusive.\n *\n * * Numbers can be negative or positive.\n * * Ending number is exclusive.\n *\n * @param {number} number The number to check.\n * @param {number} end The end of the range. Exclusive.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n */\nexport function inRange(number: number, end: number): boolean\n\n/**\n * Checks if the given number is between two numbers.\n *\n * * Numbers can be negative or positive.\n * * Starting number is inclusive.\n * * Ending number is exclusive.\n * * The start and the end of the range can be ascending OR descending order.\n *\n * @param {number} number The number to check.\n * @param {number} start The start of the range. Inclusive.\n * @param {number} end The end of the range. Exclusive.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n */\nexport function inRange(number: number, start: number, end: number): boolean\nexport function inRange(number: number, start: number, end?: number): boolean {\n  const isTypeSafe =\n    typeof number === 'number' &&\n    typeof start === 'number' &&\n    (typeof end === 'undefined' || typeof end === 'number')\n\n  if (!isTypeSafe) {\n    return false\n  }\n\n  if (typeof end === 'undefined') {\n    end = start\n    start = 0\n  }\n\n  return number >= Math.min(start, end) && number < Math.max(start, end)\n}\n\nexport const toFloat = <T extends number | null = number>(\n  value: any,\n  defaultValue?: T\n): number | T => {\n  const def = defaultValue === undefined ? 0.0 : defaultValue\n  if (value === null || value === undefined) {\n    return def\n  }\n  const result = parseFloat(value)\n  return isNaN(result) ? def : result\n}\n\nexport const toInt = <T extends number | null = number>(\n  value: any,\n  defaultValue?: T\n): number | T => {\n  const def = defaultValue === undefined ? 0 : defaultValue\n  if (value === null || value === undefined) {\n    return def\n  }\n  const result = parseInt(value)\n  return isNaN(result) ? def : result\n}\n"], "names": [], "mappings": ";;AA0BgB,SAAA,OAAA,CAAQ,MAAgB,EAAA,KAAA,EAAe,GAAuB,EAAA;AAC5E,EAAM,MAAA,UAAA,GACJ,OAAO,MAAA,KAAW,QAClB,IAAA,OAAO,KAAU,KAAA,QAAA,KAChB,OAAO,GAAA,KAAQ,WAAe,IAAA,OAAO,GAAQ,KAAA,QAAA,CAAA,CAAA;AAEhD,EAAA,IAAI,CAAC,UAAY,EAAA;AACf,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AAEA,EAAI,IAAA,OAAO,QAAQ,WAAa,EAAA;AAC9B,IAAM,GAAA,GAAA,KAAA,CAAA;AACN,IAAQ,KAAA,GAAA,CAAA,CAAA;AAAA,GACV;AAEA,EAAO,OAAA,MAAA,IAAU,IAAK,CAAA,GAAA,CAAI,KAAO,EAAA,GAAG,KAAK,MAAS,GAAA,IAAA,CAAK,GAAI,CAAA,KAAA,EAAO,GAAG,CAAA,CAAA;AACvE,CAAA;AAEa,MAAA,OAAA,GAAU,CACrB,KAAA,EACA,YACe,KAAA;AACf,EAAM,MAAA,GAAA,GAAM,YAAiB,KAAA,KAAA,CAAA,GAAY,CAAM,GAAA,YAAA,CAAA;AAC/C,EAAI,IAAA,KAAA,KAAU,IAAQ,IAAA,KAAA,KAAU,KAAW,CAAA,EAAA;AACzC,IAAO,OAAA,GAAA,CAAA;AAAA,GACT;AACA,EAAM,MAAA,MAAA,GAAS,WAAW,KAAK,CAAA,CAAA;AAC/B,EAAO,OAAA,KAAA,CAAM,MAAM,CAAA,GAAI,GAAM,GAAA,MAAA,CAAA;AAC/B,EAAA;AAEa,MAAA,KAAA,GAAQ,CACnB,KAAA,EACA,YACe,KAAA;AACf,EAAM,MAAA,GAAA,GAAM,YAAiB,KAAA,KAAA,CAAA,GAAY,CAAI,GAAA,YAAA,CAAA;AAC7C,EAAI,IAAA,KAAA,KAAU,IAAQ,IAAA,KAAA,KAAU,KAAW,CAAA,EAAA;AACzC,IAAO,OAAA,GAAA,CAAA;AAAA,GACT;AACA,EAAM,MAAA,MAAA,GAAS,SAAS,KAAK,CAAA,CAAA;AAC7B,EAAO,OAAA,KAAA,CAAM,MAAM,CAAA,GAAI,GAAM,GAAA,MAAA,CAAA;AAC/B;;;;;;"}