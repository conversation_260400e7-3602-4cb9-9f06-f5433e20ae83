{"version": 3, "file": "typed.cjs", "sources": ["../../src/typed.ts"], "sourcesContent": ["export const isSymbol = (value: any): value is symbol => {\n  return !!value && value.constructor === Symbol\n}\n\nexport const isArray = Array.isArray\n\nexport const isObject = (value: any): value is object => {\n  return !!value && value.constructor === Object\n}\n\n/**\n * Checks if the given value is primitive.\n *\n * Primitive Types: number , string , boolean , symbol, bigint, undefined, null\n *\n * @param {*} value value to check\n * @returns {boolean} result\n */\nexport const isPrimitive = (value: any): boolean => {\n  return (\n    value === undefined ||\n    value === null ||\n    (typeof value !== 'object' && typeof value !== 'function')\n  )\n}\n\nexport const isFunction = (value: any): value is Function => {\n  return !!(value && value.constructor && value.call && value.apply)\n}\n\nexport const isString = (value: any): value is string => {\n  return typeof value === 'string' || value instanceof String\n}\n\nexport const isInt = (value: any): value is number => {\n  return isNumber(value) && value % 1 === 0\n}\n\nexport const isFloat = (value: any): value is number => {\n  return isNumber(value) && value % 1 !== 0\n}\n\nexport const isNumber = (value: any): value is number => {\n  try {\n    return Number(value) === value\n  } catch {\n    return false\n  }\n}\n\nexport const isDate = (value: any): value is Date => {\n  return Object.prototype.toString.call(value) === '[object Date]'\n}\n\n/**\n * This is really a _best guess_ promise checking. You\n * should probably use Promise.resolve(value) to be 100%\n * sure you're handling it correctly.\n */\nexport const isPromise = (value: any): value is Promise<any> => {\n  if (!value) return false\n  if (!value.then) return false\n  if (!isFunction(value.then)) return false\n  return true\n}\n\nexport const isEmpty = (value: any) => {\n  if (value === true || value === false) return true\n  if (value === null || value === undefined) return true\n  if (isNumber(value)) return value === 0\n  if (isDate(value)) return isNaN(value.getTime())\n  if (isFunction(value)) return false\n  if (isSymbol(value)) return false\n  const length = (value as any).length\n  if (isNumber(length)) return length === 0\n  const size = (value as any).size\n  if (isNumber(size)) return size === 0\n  const keys = Object.keys(value).length\n  return keys === 0\n}\n\nexport const isEqual = <TType>(x: TType, y: TType): boolean => {\n  if (Object.is(x, y)) return true\n  if (x instanceof Date && y instanceof Date) {\n    return x.getTime() === y.getTime()\n  }\n  if (x instanceof RegExp && y instanceof RegExp) {\n    return x.toString() === y.toString()\n  }\n  if (\n    typeof x !== 'object' ||\n    x === null ||\n    typeof y !== 'object' ||\n    y === null\n  ) {\n    return false\n  }\n  const keysX = Reflect.ownKeys(x as unknown as object) as (keyof typeof x)[]\n  const keysY = Reflect.ownKeys(y as unknown as object)\n  if (keysX.length !== keysY.length) return false\n  for (let i = 0; i < keysX.length; i++) {\n    if (!Reflect.has(y as unknown as object, keysX[i])) return false\n    if (!isEqual(x[keysX[i]], y[keysX[i]])) return false\n  }\n  return true\n}\n"], "names": [], "mappings": ";;AAAa,MAAA,QAAA,GAAW,CAAC,KAAgC,KAAA;AACvD,EAAA,OAAO,CAAC,CAAC,KAAS,IAAA,KAAA,CAAM,WAAgB,KAAA,MAAA,CAAA;AAC1C,EAAA;AAEO,MAAM,UAAU,KAAM,CAAA,QAAA;AAEhB,MAAA,QAAA,GAAW,CAAC,KAAgC,KAAA;AACvD,EAAA,OAAO,CAAC,CAAC,KAAS,IAAA,KAAA,CAAM,WAAgB,KAAA,MAAA,CAAA;AAC1C,EAAA;AAUa,MAAA,WAAA,GAAc,CAAC,KAAwB,KAAA;AAClD,EACE,OAAA,KAAA,KAAU,UACV,KAAU,KAAA,IAAA,IACT,OAAO,KAAU,KAAA,QAAA,IAAY,OAAO,KAAU,KAAA,UAAA,CAAA;AAEnD,EAAA;AAEa,MAAA,UAAA,GAAa,CAAC,KAAkC,KAAA;AAC3D,EAAA,OAAO,CAAC,EAAE,KAAA,IAAS,MAAM,WAAe,IAAA,KAAA,CAAM,QAAQ,KAAM,CAAA,KAAA,CAAA,CAAA;AAC9D,EAAA;AAEa,MAAA,QAAA,GAAW,CAAC,KAAgC,KAAA;AACvD,EAAO,OAAA,OAAO,KAAU,KAAA,QAAA,IAAY,KAAiB,YAAA,MAAA,CAAA;AACvD,EAAA;AAEa,MAAA,KAAA,GAAQ,CAAC,KAAgC,KAAA;AACpD,EAAA,OAAO,QAAS,CAAA,KAAK,CAAK,IAAA,KAAA,GAAQ,CAAM,KAAA,CAAA,CAAA;AAC1C,EAAA;AAEa,MAAA,OAAA,GAAU,CAAC,KAAgC,KAAA;AACtD,EAAA,OAAO,QAAS,CAAA,KAAK,CAAK,IAAA,KAAA,GAAQ,CAAM,KAAA,CAAA,CAAA;AAC1C,EAAA;AAEa,MAAA,QAAA,GAAW,CAAC,KAAgC,KAAA;AACvD,EAAI,IAAA;AACF,IAAO,OAAA,MAAA,CAAO,KAAK,CAAM,KAAA,KAAA,CAAA;AAAA,GACzB,CAAA,MAAA;AACA,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACF,EAAA;AAEa,MAAA,MAAA,GAAS,CAAC,KAA8B,KAAA;AACnD,EAAA,OAAO,MAAO,CAAA,SAAA,CAAU,QAAS,CAAA,IAAA,CAAK,KAAK,CAAM,KAAA,eAAA,CAAA;AACnD,EAAA;AAOa,MAAA,SAAA,GAAY,CAAC,KAAsC,KAAA;AAC9D,EAAA,IAAI,CAAC,KAAA;AAAO,IAAO,OAAA,KAAA,CAAA;AACnB,EAAA,IAAI,CAAC,KAAM,CAAA,IAAA;AAAM,IAAO,OAAA,KAAA,CAAA;AACxB,EAAI,IAAA,CAAC,UAAW,CAAA,KAAA,CAAM,IAAI,CAAA;AAAG,IAAO,OAAA,KAAA,CAAA;AACpC,EAAO,OAAA,IAAA,CAAA;AACT,EAAA;AAEa,MAAA,OAAA,GAAU,CAAC,KAAe,KAAA;AACrC,EAAI,IAAA,KAAA,KAAU,QAAQ,KAAU,KAAA,KAAA;AAAO,IAAO,OAAA,IAAA,CAAA;AAC9C,EAAI,IAAA,KAAA,KAAU,QAAQ,KAAU,KAAA,KAAA,CAAA;AAAW,IAAO,OAAA,IAAA,CAAA;AAClD,EAAA,IAAI,SAAS,KAAK,CAAA;AAAG,IAAA,OAAO,KAAU,KAAA,CAAA,CAAA;AACtC,EAAA,IAAI,OAAO,KAAK,CAAA;AAAG,IAAO,OAAA,KAAA,CAAM,KAAM,CAAA,OAAA,EAAS,CAAA,CAAA;AAC/C,EAAA,IAAI,WAAW,KAAK,CAAA;AAAG,IAAO,OAAA,KAAA,CAAA;AAC9B,EAAA,IAAI,SAAS,KAAK,CAAA;AAAG,IAAO,OAAA,KAAA,CAAA;AAC5B,EAAA,MAAM,SAAU,KAAc,CAAA,MAAA,CAAA;AAC9B,EAAA,IAAI,SAAS,MAAM,CAAA;AAAG,IAAA,OAAO,MAAW,KAAA,CAAA,CAAA;AACxC,EAAA,MAAM,OAAQ,KAAc,CAAA,IAAA,CAAA;AAC5B,EAAA,IAAI,SAAS,IAAI,CAAA;AAAG,IAAA,OAAO,IAAS,KAAA,CAAA,CAAA;AACpC,EAAA,MAAM,IAAO,GAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,MAAA,CAAA;AAChC,EAAA,OAAO,IAAS,KAAA,CAAA,CAAA;AAClB,EAAA;AAEa,MAAA,OAAA,GAAU,CAAQ,CAAA,EAAU,CAAsB,KAAA;AAC7D,EAAI,IAAA,MAAA,CAAO,EAAG,CAAA,CAAA,EAAG,CAAC,CAAA;AAAG,IAAO,OAAA,IAAA,CAAA;AAC5B,EAAI,IAAA,CAAA,YAAa,IAAQ,IAAA,CAAA,YAAa,IAAM,EAAA;AAC1C,IAAA,OAAO,CAAE,CAAA,OAAA,EAAc,KAAA,CAAA,CAAE,OAAQ,EAAA,CAAA;AAAA,GACnC;AACA,EAAI,IAAA,CAAA,YAAa,MAAU,IAAA,CAAA,YAAa,MAAQ,EAAA;AAC9C,IAAA,OAAO,CAAE,CAAA,QAAA,EAAe,KAAA,CAAA,CAAE,QAAS,EAAA,CAAA;AAAA,GACrC;AACA,EACE,IAAA,OAAO,MAAM,QACb,IAAA,CAAA,KAAM,QACN,OAAO,CAAA,KAAM,QACb,IAAA,CAAA,KAAM,IACN,EAAA;AACA,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACA,EAAM,MAAA,KAAA,GAAQ,OAAQ,CAAA,OAAA,CAAQ,CAAsB,CAAA,CAAA;AACpD,EAAM,MAAA,KAAA,GAAQ,OAAQ,CAAA,OAAA,CAAQ,CAAsB,CAAA,CAAA;AACpD,EAAI,IAAA,KAAA,CAAM,WAAW,KAAM,CAAA,MAAA;AAAQ,IAAO,OAAA,KAAA,CAAA;AAC1C,EAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,KAAA,CAAM,QAAQ,CAAK,EAAA,EAAA;AACrC,IAAA,IAAI,CAAC,OAAA,CAAQ,GAAI,CAAA,CAAA,EAAwB,MAAM,CAAE,CAAA,CAAA;AAAG,MAAO,OAAA,KAAA,CAAA;AAC3D,IAAA,IAAI,CAAC,OAAQ,CAAA,CAAA,CAAE,MAAM,CAAK,CAAA,CAAA,EAAA,CAAA,CAAE,MAAM,CAAG,CAAA,CAAA,CAAA;AAAG,MAAO,OAAA,KAAA,CAAA;AAAA,GACjD;AACA,EAAO,OAAA,IAAA,CAAA;AACT;;;;;;;;;;;;;;;;"}