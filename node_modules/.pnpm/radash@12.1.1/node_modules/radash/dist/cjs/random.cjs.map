{"version": 3, "file": "random.cjs", "sources": ["../../src/random.ts"], "sourcesContent": ["import { iterate } from './array'\n\n/**\n * Generates a random number between min and max\n */\nexport const random = (min: number, max: number) => {\n  return Math.floor(Math.random() * (max - min + 1) + min)\n}\n\n/**\n * Draw a random item from a list. Returns\n * null if the list is empty\n */\nexport const draw = <T>(array: readonly T[]): T | null => {\n  const max = array.length\n  if (max === 0) {\n    return null\n  }\n  const index = random(0, max - 1)\n  return array[index]\n}\n\nexport const shuffle = <T>(array: readonly T[]): T[] => {\n  return array\n    .map(a => ({ rand: Math.random(), value: a }))\n    .sort((a, b) => a.rand - b.rand)\n    .map(a => a.value)\n}\n\nexport const uid = (length: number, specials: string = '') => {\n  const characters =\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789' + specials\n  return iterate(\n    length,\n    acc => {\n      return acc + characters.charAt(random(0, characters.length - 1))\n    },\n    ''\n  )\n}\n"], "names": ["iterate"], "mappings": ";;;;AAKa,MAAA,MAAA,GAAS,CAAC,GAAA,EAAa,GAAgB,KAAA;AAClD,EAAO,OAAA,IAAA,CAAK,MAAM,IAAK,CAAA,MAAA,MAAY,GAAM,GAAA,GAAA,GAAM,KAAK,GAAG,CAAA,CAAA;AACzD,EAAA;AAMa,MAAA,IAAA,GAAO,CAAI,KAAkC,KAAA;AACxD,EAAA,MAAM,MAAM,KAAM,CAAA,MAAA,CAAA;AAClB,EAAA,IAAI,QAAQ,CAAG,EAAA;AACb,IAAO,OAAA,IAAA,CAAA;AAAA,GACT;AACA,EAAA,MAAM,KAAQ,GAAA,MAAA,CAAO,CAAG,EAAA,GAAA,GAAM,CAAC,CAAA,CAAA;AAC/B,EAAA,OAAO,KAAM,CAAA,KAAA,CAAA,CAAA;AACf,EAAA;AAEa,MAAA,OAAA,GAAU,CAAI,KAA6B,KAAA;AACtD,EAAO,OAAA,KAAA,CACJ,GAAI,CAAA,CAAA,CAAA,MAAM,EAAE,IAAA,EAAM,KAAK,MAAO,EAAA,EAAG,KAAO,EAAA,CAAA,EAAI,CAAA,CAAA,CAC5C,KAAK,CAAC,CAAA,EAAG,CAAM,KAAA,CAAA,CAAE,IAAO,GAAA,CAAA,CAAE,IAAI,CAC9B,CAAA,GAAA,CAAI,CAAK,CAAA,KAAA,CAAA,CAAE,KAAK,CAAA,CAAA;AACrB,EAAA;AAEO,MAAM,GAAM,GAAA,CAAC,MAAgB,EAAA,QAAA,GAAmB,EAAO,KAAA;AAC5D,EAAA,MAAM,aACJ,gEAAmE,GAAA,QAAA,CAAA;AACrE,EAAO,OAAAA,aAAA;AAAA,IACL,MAAA;AAAA,IACA,CAAO,GAAA,KAAA;AACL,MAAO,OAAA,GAAA,GAAM,WAAW,MAAO,CAAA,MAAA,CAAO,GAAG,UAAW,CAAA,MAAA,GAAS,CAAC,CAAC,CAAA,CAAA;AAAA,KACjE;AAAA,IACA,EAAA;AAAA,GACF,CAAA;AACF;;;;;;;"}