{"version": 3, "file": "async.cjs", "sources": ["../../src/async.ts"], "sourcesContent": ["import { fork, list, range, sort } from './array'\nimport { isArray, isPromise } from './typed'\n\n/**\n * An async reduce function. Works like the\n * built-in Array.reduce function but handles\n * an async reducer function\n */\nexport const reduce = async <T, K>(\n  array: readonly T[],\n  asyncReducer: (acc: K, item: T, index: number) => Promise<K>,\n  initValue?: K\n): Promise<K> => {\n  const initProvided = initValue !== undefined\n  if (!initProvided && array?.length < 1) {\n    throw new Error('Cannot reduce empty array with no init value')\n  }\n  const iter = initProvided ? array : array.slice(1)\n  let value: any = initProvided ? initValue : array[0]\n  for (const [i, item] of iter.entries()) {\n    value = await asyncReducer(value, item, i)\n  }\n  return value\n}\n\n/**\n * An async map function. Works like the\n * built-in Array.map function but handles\n * an async mapper function\n */\nexport const map = async <T, K>(\n  array: readonly T[],\n  asyncMapFunc: (item: T, index: number) => Promise<K>\n): Promise<K[]> => {\n  if (!array) return []\n  let result = []\n  let index = 0\n  for (const value of array) {\n    const newValue = await asyncMapFunc(value, index++)\n    result.push(newValue)\n  }\n  return result\n}\n\n/**\n * Useful when for script like things where cleanup\n * should be done on fail or sucess no matter.\n *\n * You can call defer many times to register many\n * defered functions that will all be called when\n * the function exits in any state.\n */\nexport const defer = async <TResponse>(\n  func: (\n    register: (\n      fn: (error?: any) => any,\n      options?: { rethrow?: boolean }\n    ) => void\n  ) => Promise<TResponse>\n): Promise<TResponse> => {\n  const callbacks: {\n    fn: (error?: any) => any\n    rethrow: boolean\n  }[] = []\n  const register = (\n    fn: (error?: any) => any,\n    options?: { rethrow?: boolean }\n  ) =>\n    callbacks.push({\n      fn,\n      rethrow: options?.rethrow ?? false\n    })\n  const [err, response] = await tryit(func)(register)\n  for (const { fn, rethrow } of callbacks) {\n    const [rethrown] = await tryit(fn)(err)\n    if (rethrown && rethrow) throw rethrown\n  }\n  if (err) throw err\n  return response\n}\n\ntype WorkItemResult<K> = {\n  index: number\n  result: K\n  error: any\n}\n\n/**\n * Support for the built-in AggregateError\n * is still new. Node < 15 doesn't have it\n * so patching here.\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError#browser_compatibility\n */\nexport class AggregateError extends Error {\n  errors: Error[]\n  constructor(errors: Error[] = []) {\n    super()\n    const name = errors.find(e => e.name)?.name ?? ''\n    this.name = `AggregateError(${name}...)`\n    this.message = `AggregateError with ${errors.length} errors`\n    this.stack = errors.find(e => e.stack)?.stack ?? this.stack\n    this.errors = errors\n  }\n}\n\n/**\n * Executes many async functions in parallel. Returns the\n * results from all functions as an array. After all functions\n * have resolved, if any errors were thrown, they are rethrown\n * in an instance of AggregateError\n */\nexport const parallel = async <T, K>(\n  limit: number,\n  array: readonly T[],\n  func: (item: T) => Promise<K>\n): Promise<K[]> => {\n  const work = array.map((item, index) => ({\n    index,\n    item\n  }))\n  // Process array items\n  const processor = async (res: (value: WorkItemResult<K>[]) => void) => {\n    const results: WorkItemResult<K>[] = []\n    while (true) {\n      const next = work.pop()\n      if (!next) return res(results)\n      const [error, result] = await tryit(func)(next.item)\n      results.push({\n        error,\n        result: result as K,\n        index: next.index\n      })\n    }\n  }\n  // Create queues\n  const queues = list(1, limit).map(() => new Promise(processor))\n  // Wait for all queues to complete\n  const itemResults = (await Promise.all(queues)) as WorkItemResult<K>[][]\n  const [errors, results] = fork(\n    sort(itemResults.flat(), r => r.index),\n    x => !!x.error\n  )\n  if (errors.length > 0) {\n    throw new AggregateError(errors.map(error => error.error))\n  }\n  return results.map(r => r.result)\n}\n\ntype PromiseValues<T extends Promise<any>[]> = {\n  [K in keyof T]: T[K] extends Promise<infer U> ? U : never\n}\n\n/**\n * Functionally similar to Promise.all or Promise.allSettled. If any\n * errors are thrown, all errors are gathered and thrown in an\n * AggregateError.\n *\n * @example\n * const [user] = await all([\n *   api.users.create(...),\n *   s3.buckets.create(...),\n *   slack.customerSuccessChannel.sendMessage(...)\n * ])\n */\nexport async function all<T extends [Promise<any>, ...Promise<any>[]]>(\n  promises: T\n): Promise<PromiseValues<T>>\nexport async function all<T extends Promise<any>[]>(\n  promises: T\n): Promise<PromiseValues<T>>\n/**\n * Functionally similar to Promise.all or Promise.allSettled. If any\n * errors are thrown, all errors are gathered and thrown in an\n * AggregateError.\n *\n * @example\n * const { user } = await all({\n *   user: api.users.create(...),\n *   bucket: s3.buckets.create(...),\n *   message: slack.customerSuccessChannel.sendMessage(...)\n * })\n */\nexport async function all<T extends Record<string, Promise<any>>>(\n  promises: T\n): Promise<{ [K in keyof T]: Awaited<T[K]> }>\nexport async function all<\n  T extends Record<string, Promise<any>> | Promise<any>[]\n>(promises: T) {\n  const entries = isArray(promises)\n    ? promises.map(p => [null, p] as [null, Promise<any>])\n    : Object.entries(promises)\n\n  const results = await Promise.all(\n    entries.map(([key, value]) =>\n      value\n        .then(result => ({ result, exc: null, key }))\n        .catch(exc => ({ result: null, exc, key }))\n    )\n  )\n\n  const exceptions = results.filter(r => r.exc)\n  if (exceptions.length > 0) {\n    throw new AggregateError(exceptions.map(e => e.exc))\n  }\n\n  if (isArray(promises)) {\n    return results.map(r => r.result) as T extends Promise<any>[]\n      ? PromiseValues<T>\n      : unknown\n  }\n\n  return results.reduce(\n    (acc, item) => ({\n      ...acc,\n      [item.key!]: item.result\n    }),\n    {} as { [K in keyof T]: Awaited<T[K]> }\n  )\n}\n\n/**\n * Retries the given function the specified number\n * of times.\n */\nexport const retry = async <TResponse>(\n  options: {\n    times?: number\n    delay?: number | null\n    backoff?: (count: number) => number\n  },\n  func: (exit: (err: any) => void) => Promise<TResponse>\n): Promise<TResponse> => {\n  const times = options?.times ?? 3\n  const delay = options?.delay\n  const backoff = options?.backoff ?? null\n  for (const i of range(1, times)) {\n    const [err, result] = (await tryit(func)((err: any) => {\n      throw { _exited: err }\n    })) as [any, TResponse]\n    if (!err) return result\n    if (err._exited) throw err._exited\n    if (i === times) throw err\n    if (delay) await sleep(delay)\n    if (backoff) await sleep(backoff(i))\n  }\n  // Logically, we should never reach this\n  // code path. It makes the function meet\n  // strict mode requirements.\n  /* istanbul ignore next */\n  return undefined as unknown as TResponse\n}\n\n/**\n * Async wait\n */\nexport const sleep = (milliseconds: number) => {\n  return new Promise(res => setTimeout(res, milliseconds))\n}\n\n/**\n * A helper to try an async function without forking\n * the control flow. Returns an error first callback _like_\n * array response as [Error, result]\n */\nexport const tryit = <Args extends any[], Return>(\n  func: (...args: Args) => Return\n) => {\n  return (\n    ...args: Args\n  ): Return extends Promise<any>\n    ? Promise<[Error, undefined] | [undefined, Awaited<Return>]>\n    : [Error, undefined] | [undefined, Return] => {\n    try {\n      const result = func(...args)\n      if (isPromise(result)) {\n        return result\n          .then(value => [undefined, value])\n          .catch(err => [err, undefined]) as Return extends Promise<any>\n          ? Promise<[Error, undefined] | [undefined, Awaited<Return>]>\n          : [Error, undefined] | [undefined, Return]\n      }\n      return [undefined, result] as Return extends Promise<any>\n        ? Promise<[Error, undefined] | [undefined, Awaited<Return>]>\n        : [Error, undefined] | [undefined, Return]\n    } catch (err) {\n      return [err as any, undefined] as Return extends Promise<any>\n        ? Promise<[Error, undefined] | [undefined, Awaited<Return>]>\n        : [Error, undefined] | [undefined, Return]\n    }\n  }\n}\n\n/**\n * A helper to try an async function that returns undefined\n * if it fails.\n *\n * e.g. const result = await guard(fetchUsers)() ?? [];\n */\nexport const guard = <TFunction extends () => any>(\n  func: TFunction,\n  shouldGuard?: (err: any) => boolean\n): ReturnType<TFunction> extends Promise<any>\n  ? Promise<Awaited<ReturnType<TFunction>> | undefined>\n  : ReturnType<TFunction> | undefined => {\n  const _guard = (err: any) => {\n    if (shouldGuard && !shouldGuard(err)) throw err\n    return undefined as any\n  }\n  const isPromise = (result: any): result is Promise<any> =>\n    result instanceof Promise\n  try {\n    const result = func()\n    return isPromise(result) ? result.catch(_guard) : result\n  } catch (err) {\n    return _guard(err)\n  }\n}\n"], "names": ["array", "results", "list", "fork", "sort", "isArray", "range", "err", "isPromise"], "mappings": ";;;;;AAQO,MAAM,MAAS,GAAA,OACpB,KACA,EAAA,YAAA,EACA,SACe,KAAA;AACf,EAAA,MAAM,eAAe,SAAc,KAAA,KAAA,CAAA,CAAA;AACnC,EAAA,IAAI,CAAC,YAAA,IAAgB,KAAO,EAAA,MAAA,GAAS,CAAG,EAAA;AACtC,IAAM,MAAA,IAAI,MAAM,8CAA8C,CAAA,CAAA;AAAA,GAChE;AACA,EAAA,MAAM,IAAO,GAAA,YAAA,GAAe,KAAQ,GAAA,KAAA,CAAM,MAAM,CAAC,CAAA,CAAA;AACjD,EAAI,IAAA,KAAA,GAAa,YAAe,GAAA,SAAA,GAAY,KAAM,CAAA,CAAA,CAAA,CAAA;AAClD,EAAA,KAAA,MAAW,CAAC,CAAG,EAAA,IAAI,CAAK,IAAA,IAAA,CAAK,SAAW,EAAA;AACtC,IAAA,KAAA,GAAQ,MAAM,YAAA,CAAa,KAAO,EAAA,IAAA,EAAM,CAAC,CAAA,CAAA;AAAA,GAC3C;AACA,EAAO,OAAA,KAAA,CAAA;AACT,EAAA;AAOa,MAAA,GAAA,GAAM,OACjB,KAAA,EACA,YACiB,KAAA;AACjB,EAAA,IAAI,CAAC,KAAA;AAAO,IAAA,OAAO,EAAC,CAAA;AACpB,EAAA,IAAI,SAAS,EAAC,CAAA;AACd,EAAA,IAAI,KAAQ,GAAA,CAAA,CAAA;AACZ,EAAA,KAAA,MAAW,SAAS,KAAO,EAAA;AACzB,IAAA,MAAM,QAAW,GAAA,MAAM,YAAa,CAAA,KAAA,EAAO,KAAO,EAAA,CAAA,CAAA;AAClD,IAAA,MAAA,CAAO,KAAK,QAAQ,CAAA,CAAA;AAAA,GACtB;AACA,EAAO,OAAA,MAAA,CAAA;AACT,EAAA;AAUa,MAAA,KAAA,GAAQ,OACnB,IAMuB,KAAA;AACvB,EAAA,MAAM,YAGA,EAAC,CAAA;AACP,EAAA,MAAM,QAAW,GAAA,CACf,EACA,EAAA,OAAA,KAEA,UAAU,IAAK,CAAA;AAAA,IACb,EAAA;AAAA,IACA,OAAA,EAAS,SAAS,OAAW,IAAA,KAAA;AAAA,GAC9B,CAAA,CAAA;AACH,EAAM,MAAA,CAAC,KAAK,QAAQ,CAAA,GAAI,MAAM,KAAM,CAAA,IAAI,EAAE,QAAQ,CAAA,CAAA;AAClD,EAAA,KAAA,MAAW,EAAE,EAAA,EAAI,OAAQ,EAAA,IAAK,SAAW,EAAA;AACvC,IAAA,MAAM,CAAC,QAAQ,CAAA,GAAI,MAAM,KAAM,CAAA,EAAE,EAAE,GAAG,CAAA,CAAA;AACtC,IAAA,IAAI,QAAY,IAAA,OAAA;AAAS,MAAM,MAAA,QAAA,CAAA;AAAA,GACjC;AACA,EAAI,IAAA,GAAA;AAAK,IAAM,MAAA,GAAA,CAAA;AACf,EAAO,OAAA,QAAA,CAAA;AACT,EAAA;AAcO,MAAM,uBAAuB,KAAM,CAAA;AAAA,EAExC,WAAA,CAAY,MAAkB,GAAA,EAAI,EAAA;AAChC,IAAM,KAAA,EAAA,CAAA;AACN,IAAA,MAAM,OAAO,MAAO,CAAA,IAAA,CAAK,OAAK,CAAE,CAAA,IAAI,GAAG,IAAQ,IAAA,EAAA,CAAA;AAC/C,IAAA,IAAA,CAAK,OAAO,CAAkB,eAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAC9B,IAAK,IAAA,CAAA,OAAA,GAAU,uBAAuB,MAAO,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;AAC7C,IAAK,IAAA,CAAA,KAAA,GAAQ,OAAO,IAAK,CAAA,CAAA,CAAA,KAAK,EAAE,KAAK,CAAA,EAAG,SAAS,IAAK,CAAA,KAAA,CAAA;AACtD,IAAA,IAAA,CAAK,MAAS,GAAA,MAAA,CAAA;AAAA,GAChB;AACF,CAAA;AAQO,MAAM,QAAW,GAAA,OACtB,KACA,EAAAA,OAAA,EACA,IACiB,KAAA;AACjB,EAAA,MAAM,IAAO,GAAAA,OAAA,CAAM,GAAI,CAAA,CAAC,MAAM,KAAW,MAAA;AAAA,IACvC,KAAA;AAAA,IACA,IAAA;AAAA,GACA,CAAA,CAAA,CAAA;AAEF,EAAM,MAAA,SAAA,GAAY,OAAO,GAA8C,KAAA;AACrE,IAAA,MAAMC,WAA+B,EAAC,CAAA;AACtC,IAAA,OAAO,IAAM,EAAA;AACX,MAAM,MAAA,IAAA,GAAO,KAAK,GAAI,EAAA,CAAA;AACtB,MAAA,IAAI,CAAC,IAAA;AAAM,QAAA,OAAO,IAAIA,QAAO,CAAA,CAAA;AAC7B,MAAM,MAAA,CAAC,OAAO,MAAM,CAAA,GAAI,MAAM,KAAM,CAAA,IAAI,CAAE,CAAA,IAAA,CAAK,IAAI,CAAA,CAAA;AACnD,MAAAA,SAAQ,IAAK,CAAA;AAAA,QACX,KAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAO,IAAK,CAAA,KAAA;AAAA,OACb,CAAA,CAAA;AAAA,KACH;AAAA,GACF,CAAA;AAEA,EAAM,MAAA,MAAA,GAASC,UAAK,CAAA,CAAA,EAAG,KAAK,CAAA,CAAE,IAAI,MAAM,IAAI,OAAQ,CAAA,SAAS,CAAC,CAAA,CAAA;AAE9D,EAAA,MAAM,WAAe,GAAA,MAAM,OAAQ,CAAA,GAAA,CAAI,MAAM,CAAA,CAAA;AAC7C,EAAM,MAAA,CAAC,MAAQ,EAAA,OAAO,CAAI,GAAAC,UAAA;AAAA,IACxBC,WAAK,WAAY,CAAA,IAAA,EAAQ,EAAA,CAAA,CAAA,KAAK,EAAE,KAAK,CAAA;AAAA,IACrC,CAAA,CAAA,KAAK,CAAC,CAAC,CAAE,CAAA,KAAA;AAAA,GACX,CAAA;AACA,EAAI,IAAA,MAAA,CAAO,SAAS,CAAG,EAAA;AACrB,IAAA,MAAM,IAAI,cAAe,CAAA,MAAA,CAAO,IAAI,CAAS,KAAA,KAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,GAC3D;AACA,EAAA,OAAO,OAAQ,CAAA,GAAA,CAAI,CAAK,CAAA,KAAA,CAAA,CAAE,MAAM,CAAA,CAAA;AAClC,EAAA;AAuCA,eAAsB,IAEpB,QAAa,EAAA;AACb,EAAA,MAAM,OAAU,GAAAC,aAAA,CAAQ,QAAQ,CAAA,GAC5B,SAAS,GAAI,CAAA,CAAA,CAAA,KAAK,CAAC,IAAA,EAAM,CAAC,CAAyB,CACnD,GAAA,MAAA,CAAO,QAAQ,QAAQ,CAAA,CAAA;AAE3B,EAAM,MAAA,OAAA,GAAU,MAAM,OAAQ,CAAA,GAAA;AAAA,IAC5B,OAAQ,CAAA,GAAA;AAAA,MAAI,CAAC,CAAC,GAAK,EAAA,KAAK,MACtB,KACG,CAAA,IAAA,CAAK,CAAW,MAAA,MAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,MAAM,GAAI,EAAA,CAAE,EAC3C,KAAM,CAAA,CAAA,GAAA,MAAQ,EAAE,MAAQ,EAAA,IAAA,EAAM,GAAK,EAAA,GAAA,EAAM,CAAA,CAAA;AAAA,KAC9C;AAAA,GACF,CAAA;AAEA,EAAA,MAAM,UAAa,GAAA,OAAA,CAAQ,MAAO,CAAA,CAAA,CAAA,KAAK,EAAE,GAAG,CAAA,CAAA;AAC5C,EAAI,IAAA,UAAA,CAAW,SAAS,CAAG,EAAA;AACzB,IAAA,MAAM,IAAI,cAAe,CAAA,UAAA,CAAW,IAAI,CAAK,CAAA,KAAA,CAAA,CAAE,GAAG,CAAC,CAAA,CAAA;AAAA,GACrD;AAEA,EAAI,IAAAA,aAAA,CAAQ,QAAQ,CAAG,EAAA;AACrB,IAAA,OAAO,OAAQ,CAAA,GAAA,CAAI,CAAK,CAAA,KAAA,CAAA,CAAE,MAAM,CAAA,CAAA;AAAA,GAGlC;AAEA,EAAA,OAAO,OAAQ,CAAA,MAAA;AAAA,IACb,CAAC,KAAK,IAAU,MAAA;AAAA,MACd,GAAG,GAAA;AAAA,MACH,CAAC,IAAK,CAAA,GAAA,GAAO,IAAK,CAAA,MAAA;AAAA,KACpB,CAAA;AAAA,IACA,EAAC;AAAA,GACH,CAAA;AACF,CAAA;AAMa,MAAA,KAAA,GAAQ,OACnB,OAAA,EAKA,IACuB,KAAA;AACvB,EAAM,MAAA,KAAA,GAAQ,SAAS,KAAS,IAAA,CAAA,CAAA;AAChC,EAAA,MAAM,QAAQ,OAAS,EAAA,KAAA,CAAA;AACvB,EAAM,MAAA,OAAA,GAAU,SAAS,OAAW,IAAA,IAAA,CAAA;AACpC,EAAA,KAAA,MAAW,CAAK,IAAAC,WAAA,CAAM,CAAG,EAAA,KAAK,CAAG,EAAA;AAC/B,IAAM,MAAA,CAAC,KAAK,MAAM,CAAA,GAAK,MAAM,KAAM,CAAA,IAAI,CAAE,CAAA,CAACC,IAAa,KAAA;AACrD,MAAM,MAAA,EAAE,SAASA,IAAI,EAAA,CAAA;AAAA,KACtB,CAAA,CAAA;AACD,IAAA,IAAI,CAAC,GAAA;AAAK,MAAO,OAAA,MAAA,CAAA;AACjB,IAAA,IAAI,GAAI,CAAA,OAAA;AAAS,MAAA,MAAM,GAAI,CAAA,OAAA,CAAA;AAC3B,IAAA,IAAI,CAAM,KAAA,KAAA;AAAO,MAAM,MAAA,GAAA,CAAA;AACvB,IAAI,IAAA,KAAA;AAAO,MAAA,MAAM,MAAM,KAAK,CAAA,CAAA;AAC5B,IAAI,IAAA,OAAA;AAAS,MAAM,MAAA,KAAA,CAAM,OAAQ,CAAA,CAAC,CAAC,CAAA,CAAA;AAAA,GACrC;AAKA,EAAO,OAAA,KAAA,CAAA,CAAA;AACT,EAAA;AAKa,MAAA,KAAA,GAAQ,CAAC,YAAyB,KAAA;AAC7C,EAAA,OAAO,IAAI,OAAQ,CAAA,CAAA,GAAA,KAAO,UAAW,CAAA,GAAA,EAAK,YAAY,CAAC,CAAA,CAAA;AACzD,EAAA;AAOa,MAAA,KAAA,GAAQ,CACnB,IACG,KAAA;AACH,EAAA,OAAO,IACF,IAG2C,KAAA;AAC9C,IAAI,IAAA;AACF,MAAM,MAAA,MAAA,GAAS,IAAK,CAAA,GAAG,IAAI,CAAA,CAAA;AAC3B,MAAI,IAAAC,eAAA,CAAU,MAAM,CAAG,EAAA;AACrB,QAAA,OAAO,MACJ,CAAA,IAAA,CAAK,CAAS,KAAA,KAAA,CAAC,KAAW,CAAA,EAAA,KAAK,CAAC,CAAA,CAChC,KAAM,CAAA,CAAA,GAAA,KAAO,CAAC,GAAA,EAAK,MAAS,CAAC,CAAA,CAAA;AAAA,OAGlC;AACA,MAAO,OAAA,CAAC,QAAW,MAAM,CAAA,CAAA;AAAA,aAGlB,GAAP,EAAA;AACA,MAAO,OAAA,CAAC,KAAY,KAAS,CAAA,CAAA,CAAA;AAAA,KAG/B;AAAA,GACF,CAAA;AACF,EAAA;AAQa,MAAA,KAAA,GAAQ,CACnB,IAAA,EACA,WAGuC,KAAA;AACvC,EAAM,MAAA,MAAA,GAAS,CAAC,GAAa,KAAA;AAC3B,IAAI,IAAA,WAAA,IAAe,CAAC,WAAA,CAAY,GAAG,CAAA;AAAG,MAAM,MAAA,GAAA,CAAA;AAC5C,IAAO,OAAA,KAAA,CAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAMA,MAAAA,UAAAA,GAAY,CAAC,MAAA,KACjB,MAAkB,YAAA,OAAA,CAAA;AACpB,EAAI,IAAA;AACF,IAAA,MAAM,SAAS,IAAK,EAAA,CAAA;AACpB,IAAA,OAAOA,WAAU,MAAM,CAAA,GAAI,MAAO,CAAA,KAAA,CAAM,MAAM,CAAI,GAAA,MAAA,CAAA;AAAA,WAC3C,GAAP,EAAA;AACA,IAAA,OAAO,OAAO,GAAG,CAAA,CAAA;AAAA,GACnB;AACF;;;;;;;;;;;;;"}