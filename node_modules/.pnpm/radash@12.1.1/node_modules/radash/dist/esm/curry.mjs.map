{"version": 3, "file": "curry.mjs", "sources": ["../../src/curry.ts"], "sourcesContent": ["export function chain<T1 extends any[], T2, T3>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3\n): (...arg: T1) => T3\nexport function chain<T1 extends any[], T2, T3, T4>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4\n): (...arg: T1) => T4\nexport function chain<T1 extends any[], T2, T3, T4, T5>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5\n): (...arg: T1) => T5\nexport function chain<T1 extends any[], T2, T3, T4, T5, T6>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5,\n  f5: (arg: T3) => T6\n): (...arg: T1) => T6\nexport function chain<T1 extends any[], T2, T3, T4, T5, T6, T7>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5,\n  f5: (arg: T3) => T6,\n  f6: (arg: T3) => T7\n): (...arg: T1) => T7\nexport function chain<T1 extends any[], T2, T3, T4, T5, T6, T7, T8>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5,\n  f5: (arg: T3) => T6,\n  f6: (arg: T3) => T7,\n  f7: (arg: T3) => T8\n): (...arg: T1) => T8\nexport function chain<T1 extends any[], T2, T3, T4, T5, T6, T7, T8, T9>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5,\n  f5: (arg: T3) => T6,\n  f6: (arg: T3) => T7,\n  f7: (arg: T3) => T8,\n  f8: (arg: T3) => T9\n): (...arg: T1) => T9\nexport function chain<T1 extends any[], T2, T3, T4, T5, T6, T7, T8, T9, T10>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5,\n  f5: (arg: T3) => T6,\n  f6: (arg: T3) => T7,\n  f7: (arg: T3) => T8,\n  f8: (arg: T3) => T9,\n  f9: (arg: T3) => T10\n): (...arg: T1) => T10\nexport function chain<\n  T1 extends any[],\n  T2,\n  T3,\n  T4,\n  T5,\n  T6,\n  T7,\n  T8,\n  T9,\n  T10,\n  T11\n>(\n  f1: (...arg: T1) => T2,\n  f2: (arg: T2) => T3,\n  f3: (arg: T3) => T4,\n  f4: (arg: T3) => T5,\n  f5: (arg: T3) => T6,\n  f6: (arg: T3) => T7,\n  f7: (arg: T3) => T8,\n  f8: (arg: T3) => T9,\n  f9: (arg: T3) => T10,\n  f10: (arg: T3) => T11\n): (...arg: T1) => T11\nexport function chain(...funcs: ((...args: any[]) => any)[]) {\n  return (...args: any[]) => {\n    return funcs.slice(1).reduce((acc, fn) => fn(acc), funcs[0](...args))\n  }\n}\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => LastResult\n  ) => (...args: F1Args) => F1Result,\n  last: (...args: F1NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2Result,\n  F2NextArgs extends any[],\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => LastResult\n  ) => (...args: F1NextArgs) => F2Result,\n  last: (...args: F2NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => LastResult\n  ) => (...args: F2NextArgs) => F3Result,\n  last: (...args: F3NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  F4NextArgs extends any[],\n  F4Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => F4Result\n  ) => (...args: F2NextArgs) => F3Result,\n  f4: (\n    next: (...args: F4NextArgs) => LastResult\n  ) => (...args: F3NextArgs) => F4Result,\n  last: (...args: F4NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  F4NextArgs extends any[],\n  F4Result,\n  F5NextArgs extends any[],\n  F5Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => F4Result\n  ) => (...args: F2NextArgs) => F3Result,\n  f4: (\n    next: (...args: F4NextArgs) => F5Result\n  ) => (...args: F3NextArgs) => F4Result,\n  f5: (\n    next: (...args: F5NextArgs) => LastResult\n  ) => (...args: F4NextArgs) => F5Result,\n  last: (...args: F5NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  F4NextArgs extends any[],\n  F4Result,\n  F5NextArgs extends any[],\n  F5Result,\n  F6NextArgs extends any[],\n  F6Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => F4Result\n  ) => (...args: F2NextArgs) => F3Result,\n  f4: (\n    next: (...args: F4NextArgs) => F5Result\n  ) => (...args: F3NextArgs) => F4Result,\n  f5: (\n    next: (...args: F5NextArgs) => F6Result\n  ) => (...args: F4NextArgs) => F5Result,\n  f6: (\n    next: (...args: F6NextArgs) => LastResult\n  ) => (...args: F5NextArgs) => F6Result,\n  last: (...args: F6NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  F4NextArgs extends any[],\n  F4Result,\n  F5NextArgs extends any[],\n  F5Result,\n  F6NextArgs extends any[],\n  F6Result,\n  F7NextArgs extends any[],\n  F7Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => F4Result\n  ) => (...args: F2NextArgs) => F3Result,\n  f4: (\n    next: (...args: F4NextArgs) => F5Result\n  ) => (...args: F3NextArgs) => F4Result,\n  f5: (\n    next: (...args: F5NextArgs) => F6Result\n  ) => (...args: F4NextArgs) => F5Result,\n  f6: (\n    next: (...args: F6NextArgs) => F7Result\n  ) => (...args: F5NextArgs) => F6Result,\n  f7: (\n    next: (...args: F7NextArgs) => LastResult\n  ) => (...args: F6NextArgs) => F7Result,\n  last: (...args: F7NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  F4NextArgs extends any[],\n  F4Result,\n  F5NextArgs extends any[],\n  F5Result,\n  F6NextArgs extends any[],\n  F6Result,\n  F7NextArgs extends any[],\n  F7Result,\n  F8NextArgs extends any[],\n  F8Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => F4Result\n  ) => (...args: F2NextArgs) => F3Result,\n  f4: (\n    next: (...args: F4NextArgs) => F5Result\n  ) => (...args: F3NextArgs) => F4Result,\n  f5: (\n    next: (...args: F5NextArgs) => F6Result\n  ) => (...args: F4NextArgs) => F5Result,\n  f6: (\n    next: (...args: F6NextArgs) => F7Result\n  ) => (...args: F5NextArgs) => F6Result,\n  f7: (\n    next: (...args: F7NextArgs) => LastResult\n  ) => (...args: F6NextArgs) => F7Result,\n  f8: (\n    next: (...args: F8NextArgs) => LastResult\n  ) => (...args: F7NextArgs) => F8Result,\n  last: (...args: F8NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose<\n  F1Result,\n  F1Args extends any[],\n  F1NextArgs extends any[],\n  F2NextArgs extends any[],\n  F2Result,\n  F3NextArgs extends any[],\n  F3Result,\n  F4NextArgs extends any[],\n  F4Result,\n  F5NextArgs extends any[],\n  F5Result,\n  F6NextArgs extends any[],\n  F6Result,\n  F7NextArgs extends any[],\n  F7Result,\n  F8NextArgs extends any[],\n  F8Result,\n  F9NextArgs extends any[],\n  F9Result,\n  LastResult\n>(\n  f1: (\n    next: (...args: F1NextArgs) => F2Result\n  ) => (...args: F1Args) => F1Result,\n  f2: (\n    next: (...args: F2NextArgs) => F3Result\n  ) => (...args: F1NextArgs) => F2Result,\n  f3: (\n    next: (...args: F3NextArgs) => F4Result\n  ) => (...args: F2NextArgs) => F3Result,\n  f4: (\n    next: (...args: F4NextArgs) => F5Result\n  ) => (...args: F3NextArgs) => F4Result,\n  f5: (\n    next: (...args: F5NextArgs) => F6Result\n  ) => (...args: F4NextArgs) => F5Result,\n  f6: (\n    next: (...args: F6NextArgs) => F7Result\n  ) => (...args: F5NextArgs) => F6Result,\n  f7: (\n    next: (...args: F7NextArgs) => LastResult\n  ) => (...args: F6NextArgs) => F7Result,\n  f8: (\n    next: (...args: F8NextArgs) => LastResult\n  ) => (...args: F7NextArgs) => F8Result,\n  f9: (\n    next: (...args: F9NextArgs) => LastResult\n  ) => (...args: F8NextArgs) => F9Result,\n  last: (...args: F9NextArgs) => LastResult\n): (...args: F1Args) => F1Result\n\nexport function compose(...funcs: ((...args: any[]) => any)[]) {\n  return funcs.reverse().reduce((acc, fn) => fn(acc))\n}\n\n/**\n * This type produces the type array of TItems with all the type items\n * in TItemsToRemove removed from the start of the array type.\n *\n * @example\n * ```\n * RemoveItemsInFront<[number, number], [number]> = [number]\n * RemoveItemsInFront<[File, number, string], [File, number]> = [string]\n * ```\n */\ntype RemoveItemsInFront<\n  TItems extends any[],\n  TItemsToRemove extends any[]\n> = TItems extends [...TItemsToRemove, ...infer TRest] ? TRest : TItems\n\nexport const partial = <T extends any[], TA extends Partial<T>, R>(\n  fn: (...args: T) => R,\n  ...args: TA\n) => {\n  return (...rest: RemoveItemsInFront<T, TA>) =>\n    fn(...([...args, ...rest] as T))\n}\n/**\n * Like partial but for unary functions that accept\n * a single object argument\n */\nexport const partob = <T, K, PartialArgs extends Partial<T>>(\n  fn: (args: T) => K,\n  argobj: PartialArgs\n) => {\n  return (restobj: Omit<T, keyof PartialArgs>): K =>\n    fn({\n      ...(argobj as Partial<T>),\n      ...(restobj as Partial<T>)\n    } as T)\n}\n\n/**\n * Creates a Proxy object that will dynamically\n * call the handler argument when attributes are\n * accessed\n */\nexport const proxied = <T, K>(\n  handler: (propertyName: T) => K\n): Record<string, K> => {\n  return new Proxy(\n    {},\n    {\n      get: (target, propertyName: any) => handler(propertyName)\n    }\n  )\n}\n\ntype Cache<T> = Record<string, { exp: number | null; value: T }>\n\nconst memoize = <TArgs extends any[], TResult>(\n  cache: Cache<TResult>,\n  func: (...args: TArgs) => TResult,\n  keyFunc: ((...args: TArgs) => string) | null,\n  ttl: number | null\n) => {\n  return function callWithMemo(...args: any): TResult {\n    const key = keyFunc ? keyFunc(...args) : JSON.stringify({ args })\n    const existing = cache[key]\n    if (existing !== undefined) {\n      if (!existing.exp) return existing.value\n      if (existing.exp > new Date().getTime()) {\n        return existing.value\n      }\n    }\n    const result = func(...args)\n    cache[key] = {\n      exp: ttl ? new Date().getTime() + ttl : null,\n      value: result\n    }\n    return result\n  }\n}\n\n/**\n * Creates a memoized function. The returned function\n * will only execute the source function when no value\n * has previously been computed. If a ttl (milliseconds)\n * is given previously computed values will be checked\n * for expiration before being returned.\n */\nexport const memo = <TArgs extends any[], TResult>(\n  func: (...args: TArgs) => TResult,\n  options: {\n    key?: (...args: TArgs) => string\n    ttl?: number\n  } = {}\n) => {\n  return memoize({}, func, options.key ?? null, options.ttl ?? null) as (\n    ...args: TArgs\n  ) => TResult\n}\n\nexport type DebounceFunction<TArgs extends any[]> = {\n  (...args: TArgs): void\n  /**\n   * Cancels the debounced function\n   */\n  cancel(): void\n  /**\n   * Checks if there is any invocation debounced\n   */\n  isPending(): boolean\n  /**\n   * Runs the debounced function immediately\n   */\n  flush(...args: TArgs): void\n}\n\nexport type ThrottledFunction<TArgs extends any[]> = {\n  (...args: TArgs): void\n  /**\n   * Checks if there is any invocation throttled\n   */\n  isThrottled(): boolean\n}\n\n/**\n * Given a delay and a function returns a new function\n * that will only call the source function after delay\n * milliseconds have passed without any invocations.\n *\n * The debounce function comes with a `cancel` method\n * to cancel delayed `func` invocations and a `flush`\n * method to invoke them immediately\n */\nexport const debounce = <TArgs extends any[]>(\n  { delay }: { delay: number },\n  func: (...args: TArgs) => any\n) => {\n  let timer: NodeJS.Timeout | undefined = undefined\n  let active = true\n\n  const debounced: DebounceFunction<TArgs> = (...args: TArgs) => {\n    if (active) {\n      clearTimeout(timer)\n      timer = setTimeout(() => {\n        active && func(...args)\n        timer = undefined\n      }, delay)\n    } else {\n      func(...args)\n    }\n  }\n  debounced.isPending = () => {\n    return timer !== undefined\n  }\n  debounced.cancel = () => {\n    active = false\n  }\n  debounced.flush = (...args: TArgs) => func(...args)\n\n  return debounced\n}\n\n/**\n * Given an interval and a function returns a new function\n * that will only call the source function if interval milliseconds\n * have passed since the last invocation\n */\nexport const throttle = <TArgs extends any[]>(\n  { interval }: { interval: number },\n  func: (...args: TArgs) => any\n) => {\n  let ready = true\n  let timer: NodeJS.Timeout | undefined = undefined\n\n  const throttled: ThrottledFunction<TArgs> = (...args: TArgs) => {\n    if (!ready) return\n    func(...args)\n    ready = false\n    timer = setTimeout(() => {\n      ready = true\n      timer = undefined\n    }, interval)\n  }\n  throttled.isThrottled = () => {\n    return timer !== undefined\n  }\n  return throttled\n}\n\n/**\n * Make an object callable. Given an object and a function\n * the returned object will be a function with all the\n * objects properties.\n *\n * @example\n * ```typescript\n * const car = callable({\n *   wheels: 2\n * }, self => () => {\n *   return 'driving'\n * })\n *\n * car.wheels // => 2\n * car() // => 'driving'\n * ```\n */\nexport const callable = <\n  TValue,\n  TObj extends Record<string | number | symbol, TValue>,\n  TFunc extends (...args: any) => any\n>(\n  obj: TObj,\n  fn: (self: TObj) => TFunc\n): TObj & TFunc => {\n  /* istanbul ignore next */\n  const FUNC = () => {}\n  return new Proxy(Object.assign(FUNC, obj), {\n    get: (target, key: string) => target[key],\n    set: (target, key: string, value: any) => {\n      ;(target as any)[key] = value\n      return true\n    },\n    apply: (target, self, args) => fn(Object.assign({}, target))(...args)\n  }) as unknown as TObj & TFunc\n}\n"], "names": [], "mappings": "AAoFO,SAAS,SAAS,KAAoC,EAAA;AAC3D,EAAA,OAAO,IAAI,IAAgB,KAAA;AACzB,IAAA,OAAO,KAAM,CAAA,KAAA,CAAM,CAAC,CAAA,CAAE,OAAO,CAAC,GAAA,EAAK,EAAO,KAAA,EAAA,CAAG,GAAG,CAAG,EAAA,KAAA,CAAM,CAAG,CAAA,CAAA,GAAG,IAAI,CAAC,CAAA,CAAA;AAAA,GACtE,CAAA;AACF,CAAA;AAkSO,SAAS,WAAW,KAAoC,EAAA;AAC7D,EAAO,OAAA,KAAA,CAAM,SAAU,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,EAAA,KAAO,EAAG,CAAA,GAAG,CAAC,CAAA,CAAA;AACpD,CAAA;AAiBa,MAAA,OAAA,GAAU,CACrB,EAAA,EAAA,GACG,IACA,KAAA;AACH,EAAO,OAAA,CAAA,GAAI,SACT,EAAG,CAAA,GAAI,CAAC,GAAG,IAAA,EAAM,GAAG,IAAI,CAAO,CAAA,CAAA;AACnC,EAAA;AAKa,MAAA,MAAA,GAAS,CACpB,EAAA,EACA,MACG,KAAA;AACH,EAAO,OAAA,CAAC,YACN,EAAG,CAAA;AAAA,IACD,GAAI,MAAA;AAAA,IACJ,GAAI,OAAA;AAAA,GACA,CAAA,CAAA;AACV,EAAA;AAOa,MAAA,OAAA,GAAU,CACrB,OACsB,KAAA;AACtB,EAAA,OAAO,IAAI,KAAA;AAAA,IACT,EAAC;AAAA,IACD;AAAA,MACE,GAAK,EAAA,CAAC,MAAQ,EAAA,YAAA,KAAsB,QAAQ,YAAY,CAAA;AAAA,KAC1D;AAAA,GACF,CAAA;AACF,EAAA;AAIA,MAAM,OAAU,GAAA,CACd,KACA,EAAA,IAAA,EACA,SACA,GACG,KAAA;AACH,EAAO,OAAA,SAAS,gBAAgB,IAAoB,EAAA;AAClD,IAAM,MAAA,GAAA,GAAM,OAAU,GAAA,OAAA,CAAQ,GAAG,IAAI,IAAI,IAAK,CAAA,SAAA,CAAU,EAAE,IAAA,EAAM,CAAA,CAAA;AAChE,IAAA,MAAM,WAAW,KAAM,CAAA,GAAA,CAAA,CAAA;AACvB,IAAA,IAAI,aAAa,KAAW,CAAA,EAAA;AAC1B,MAAA,IAAI,CAAC,QAAS,CAAA,GAAA;AAAK,QAAA,OAAO,QAAS,CAAA,KAAA,CAAA;AACnC,MAAA,IAAI,SAAS,GAAM,GAAA,IAAI,IAAK,EAAA,CAAE,SAAW,EAAA;AACvC,QAAA,OAAO,QAAS,CAAA,KAAA,CAAA;AAAA,OAClB;AAAA,KACF;AACA,IAAM,MAAA,MAAA,GAAS,IAAK,CAAA,GAAG,IAAI,CAAA,CAAA;AAC3B,IAAA,KAAA,CAAM,GAAO,CAAA,GAAA;AAAA,MACX,KAAK,GAAM,GAAA,IAAI,MAAO,CAAA,OAAA,KAAY,GAAM,GAAA,IAAA;AAAA,MACxC,KAAO,EAAA,MAAA;AAAA,KACT,CAAA;AACA,IAAO,OAAA,MAAA,CAAA;AAAA,GACT,CAAA;AACF,CAAA,CAAA;AASO,MAAM,IAAO,GAAA,CAClB,IACA,EAAA,OAAA,GAGI,EACD,KAAA;AACH,EAAO,OAAA,OAAA,CAAQ,EAAI,EAAA,IAAA,EAAM,QAAQ,GAAO,IAAA,IAAA,EAAM,OAAQ,CAAA,GAAA,IAAO,IAAI,CAAA,CAAA;AAGnE,EAAA;AAmCO,MAAM,QAAW,GAAA,CACtB,EAAE,KAAA,IACF,IACG,KAAA;AACH,EAAA,IAAI,KAAoC,GAAA,KAAA,CAAA,CAAA;AACxC,EAAA,IAAI,MAAS,GAAA,IAAA,CAAA;AAEb,EAAM,MAAA,SAAA,GAAqC,IAAI,IAAgB,KAAA;AAC7D,IAAA,IAAI,MAAQ,EAAA;AACV,MAAA,YAAA,CAAa,KAAK,CAAA,CAAA;AAClB,MAAA,KAAA,GAAQ,WAAW,MAAM;AACvB,QAAU,MAAA,IAAA,IAAA,CAAK,GAAG,IAAI,CAAA,CAAA;AACtB,QAAQ,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,SACP,KAAK,CAAA,CAAA;AAAA,KACH,MAAA;AACL,MAAA,IAAA,CAAK,GAAG,IAAI,CAAA,CAAA;AAAA,KACd;AAAA,GACF,CAAA;AACA,EAAA,SAAA,CAAU,YAAY,MAAM;AAC1B,IAAA,OAAO,KAAU,KAAA,KAAA,CAAA,CAAA;AAAA,GACnB,CAAA;AACA,EAAA,SAAA,CAAU,SAAS,MAAM;AACvB,IAAS,MAAA,GAAA,KAAA,CAAA;AAAA,GACX,CAAA;AACA,EAAA,SAAA,CAAU,KAAQ,GAAA,CAAA,GAAI,IAAgB,KAAA,IAAA,CAAK,GAAG,IAAI,CAAA,CAAA;AAElD,EAAO,OAAA,SAAA,CAAA;AACT,EAAA;AAOO,MAAM,QAAW,GAAA,CACtB,EAAE,QAAA,IACF,IACG,KAAA;AACH,EAAA,IAAI,KAAQ,GAAA,IAAA,CAAA;AACZ,EAAA,IAAI,KAAoC,GAAA,KAAA,CAAA,CAAA;AAExC,EAAM,MAAA,SAAA,GAAsC,IAAI,IAAgB,KAAA;AAC9D,IAAA,IAAI,CAAC,KAAA;AAAO,MAAA,OAAA;AACZ,IAAA,IAAA,CAAK,GAAG,IAAI,CAAA,CAAA;AACZ,IAAQ,KAAA,GAAA,KAAA,CAAA;AACR,IAAA,KAAA,GAAQ,WAAW,MAAM;AACvB,MAAQ,KAAA,GAAA,IAAA,CAAA;AACR,MAAQ,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,OACP,QAAQ,CAAA,CAAA;AAAA,GACb,CAAA;AACA,EAAA,SAAA,CAAU,cAAc,MAAM;AAC5B,IAAA,OAAO,KAAU,KAAA,KAAA,CAAA,CAAA;AAAA,GACnB,CAAA;AACA,EAAO,OAAA,SAAA,CAAA;AACT,EAAA;AAmBa,MAAA,QAAA,GAAW,CAKtB,GAAA,EACA,EACiB,KAAA;AAEjB,EAAA,MAAM,OAAO,MAAM;AAAA,GAAC,CAAA;AACpB,EAAA,OAAO,IAAI,KAAM,CAAA,MAAA,CAAO,MAAO,CAAA,IAAA,EAAM,GAAG,CAAG,EAAA;AAAA,IACzC,GAAK,EAAA,CAAC,MAAQ,EAAA,GAAA,KAAgB,MAAO,CAAA,GAAA,CAAA;AAAA,IACrC,GAAK,EAAA,CAAC,MAAQ,EAAA,GAAA,EAAa,KAAe,KAAA;AACvC,MAAC,OAAe,GAAO,CAAA,GAAA,KAAA,CAAA;AACxB,MAAO,OAAA,IAAA,CAAA;AAAA,KACT;AAAA,IACA,KAAO,EAAA,CAAC,MAAQ,EAAA,IAAA,EAAM,SAAS,EAAG,CAAA,MAAA,CAAO,MAAO,CAAA,EAAI,EAAA,MAAM,CAAC,CAAA,CAAE,GAAG,IAAI,CAAA;AAAA,GACrE,CAAA,CAAA;AACH;;;;"}