{"version": 3, "file": "string.mjs", "sources": ["../../src/string.ts"], "sourcesContent": ["/**\n * Capitalize the first word of the string\n *\n * capitalize('hello')   -> 'Hello'\n * capitalize('va va voom') -> 'Va va voom'\n */\nexport const capitalize = (str: string): string => {\n  if (!str || str.length === 0) return ''\n  const lower = str.toLowerCase()\n  return lower.substring(0, 1).toUpperCase() + lower.substring(1, lower.length)\n}\n\n/**\n * Formats the given string in camel case fashion\n *\n * camel('hello world')   -> 'helloWorld'\n * camel('va va-VOOM') -> 'vaVaVoom'\n * camel('helloWorld') -> 'helloWorld'\n */\nexport const camel = (str: string): string => {\n  const parts =\n    str\n      ?.replace(/([A-Z])+/g, capitalize)\n      ?.split(/(?=[A-Z])|[\\.\\-\\s_]/)\n      .map(x => x.toLowerCase()) ?? []\n  if (parts.length === 0) return ''\n  if (parts.length === 1) return parts[0]\n  return parts.reduce((acc, part) => {\n    return `${acc}${part.charAt(0).toUpperCase()}${part.slice(1)}`\n  })\n}\n\n/**\n * Formats the given string in snake case fashion\n *\n * snake('hello world')   -> 'hello_world'\n * snake('va va-VOOM') -> 'va_va_voom'\n * snake('helloWord') -> 'hello_world'\n */\nexport const snake = (\n  str: string,\n  options?: {\n    splitOnNumber?: boolean\n  }\n): string => {\n  const parts =\n    str\n      ?.replace(/([A-Z])+/g, capitalize)\n      .split(/(?=[A-Z])|[\\.\\-\\s_]/)\n      .map(x => x.toLowerCase()) ?? []\n  if (parts.length === 0) return ''\n  if (parts.length === 1) return parts[0]\n  const result = parts.reduce((acc, part) => {\n    return `${acc}_${part.toLowerCase()}`\n  })\n  return options?.splitOnNumber === false\n    ? result\n    : result.replace(/([A-Za-z]{1}[0-9]{1})/, val => `${val[0]!}_${val[1]!}`)\n}\n\n/**\n * Formats the given string in dash case fashion\n *\n * dash('hello world')   -> 'hello-world'\n * dash('va va_VOOM') -> 'va-va-voom'\n * dash('helloWord') -> 'hello-word'\n */\nexport const dash = (str: string): string => {\n  const parts =\n    str\n      ?.replace(/([A-Z])+/g, capitalize)\n      ?.split(/(?=[A-Z])|[\\.\\-\\s_]/)\n      .map(x => x.toLowerCase()) ?? []\n  if (parts.length === 0) return ''\n  if (parts.length === 1) return parts[0]\n  return parts.reduce((acc, part) => {\n    return `${acc}-${part.toLowerCase()}`\n  })\n}\n\n/**\n * Formats the given string in pascal case fashion\n *\n * pascal('hello world') -> 'HelloWorld'\n * pascal('va va boom') -> 'VaVaBoom'\n */\nexport const pascal = (str: string): string => {\n  const parts = str?.split(/[\\.\\-\\s_]/).map(x => x.toLowerCase()) ?? []\n  if (parts.length === 0) return ''\n  return parts.map(str => str.charAt(0).toUpperCase() + str.slice(1)).join('')\n}\n\n/**\n * Formats the given string in title case fashion\n *\n * title('hello world') -> 'Hello World'\n * title('va_va_boom') -> 'Va Va Boom'\n * title('root-hook') -> 'Root Hook'\n * title('queryItems') -> 'Query Items'\n */\nexport const title = (str: string | null | undefined): string => {\n  if (!str) return ''\n  return str\n    .split(/(?=[A-Z])|[\\.\\-\\s_]/)\n    .map(s => s.trim())\n    .filter(s => !!s)\n    .map(s => capitalize(s.toLowerCase()))\n    .join(' ')\n}\n\n/**\n * template is used to replace data by name in template strings.\n * The default expression looks for {{name}} to identify names.\n *\n * Ex. template('Hello, {{name}}', { name: 'ray' })\n * Ex. template('Hello, <name>', { name: 'ray' }, /<(.+?)>/g)\n */\nexport const template = (\n  str: string,\n  data: Record<string, any>,\n  regex = /\\{\\{(.+?)\\}\\}/g\n) => {\n  return Array.from(str.matchAll(regex)).reduce((acc, match) => {\n    return acc.replace(match[0], data[match[1]])\n  }, str)\n}\n\n/**\n * Trims all prefix and suffix characters from the given\n * string. Like the builtin trim function but accepts\n * other characters you would like to trim and trims\n * multiple characters.\n *\n * ```typescript\n * trim('  hello ') // => 'hello'\n * trim('__hello__', '_') // => 'hello'\n * trim('/repos/:owner/:repo/', '/') // => 'repos/:owner/:repo'\n * trim('222222__hello__1111111', '12_') // => 'hello'\n * ```\n */\nexport const trim = (\n  str: string | null | undefined,\n  charsToTrim: string = ' '\n) => {\n  if (!str) return ''\n  const toTrim = charsToTrim.replace(/[\\W]{1}/g, '\\\\$&')\n  const regex = new RegExp(`^[${toTrim}]+|[${toTrim}]+$`, 'g')\n  return str.replace(regex, '')\n}\n"], "names": ["str"], "mappings": "AAMa,MAAA,UAAA,GAAa,CAAC,GAAwB,KAAA;AACjD,EAAI,IAAA,CAAC,GAAO,IAAA,GAAA,CAAI,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,EAAA,CAAA;AACrC,EAAM,MAAA,KAAA,GAAQ,IAAI,WAAY,EAAA,CAAA;AAC9B,EAAO,OAAA,KAAA,CAAM,SAAU,CAAA,CAAA,EAAG,CAAC,CAAA,CAAE,WAAY,EAAA,GAAI,KAAM,CAAA,SAAA,CAAU,CAAG,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAC9E,EAAA;AASa,MAAA,KAAA,GAAQ,CAAC,GAAwB,KAAA;AAC5C,EAAA,MAAM,KACJ,GAAA,GAAA,EACI,OAAQ,CAAA,WAAA,EAAa,UAAU,CAC/B,EAAA,KAAA,CAAM,qBAAqB,CAAA,CAC5B,IAAI,CAAK,CAAA,KAAA,CAAA,CAAE,WAAY,EAAC,KAAK,EAAC,CAAA;AACnC,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,EAAA,CAAA;AAC/B,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAA,OAAO,KAAM,CAAA,CAAA,CAAA,CAAA;AACrC,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA;AACjC,IAAO,OAAA,CAAA,EAAG,GAAM,CAAA,EAAA,IAAA,CAAK,MAAO,CAAA,CAAC,EAAE,WAAY,EAAA,CAAA,EAAI,IAAK,CAAA,KAAA,CAAM,CAAC,CAAA,CAAA,CAAA,CAAA;AAAA,GAC5D,CAAA,CAAA;AACH,EAAA;AASa,MAAA,KAAA,GAAQ,CACnB,GAAA,EACA,OAGW,KAAA;AACX,EAAA,MAAM,KACJ,GAAA,GAAA,EACI,OAAQ,CAAA,WAAA,EAAa,UAAU,CAChC,CAAA,KAAA,CAAM,qBAAqB,CAAA,CAC3B,IAAI,CAAK,CAAA,KAAA,CAAA,CAAE,WAAY,EAAC,KAAK,EAAC,CAAA;AACnC,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,EAAA,CAAA;AAC/B,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAA,OAAO,KAAM,CAAA,CAAA,CAAA,CAAA;AACrC,EAAA,MAAM,MAAS,GAAA,KAAA,CAAM,MAAO,CAAA,CAAC,KAAK,IAAS,KAAA;AACzC,IAAO,OAAA,CAAA,EAAG,GAAO,CAAA,CAAA,EAAA,IAAA,CAAK,WAAY,EAAA,CAAA,CAAA,CAAA;AAAA,GACnC,CAAA,CAAA;AACD,EAAA,OAAO,OAAS,EAAA,aAAA,KAAkB,KAC9B,GAAA,MAAA,GACA,MAAO,CAAA,OAAA,CAAQ,uBAAyB,EAAA,CAAA,GAAA,KAAO,CAAG,EAAA,GAAA,CAAI,CAAO,CAAA,CAAA,CAAA,EAAA,GAAA,CAAI,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAC5E,EAAA;AASa,MAAA,IAAA,GAAO,CAAC,GAAwB,KAAA;AAC3C,EAAA,MAAM,KACJ,GAAA,GAAA,EACI,OAAQ,CAAA,WAAA,EAAa,UAAU,CAC/B,EAAA,KAAA,CAAM,qBAAqB,CAAA,CAC5B,IAAI,CAAK,CAAA,KAAA,CAAA,CAAE,WAAY,EAAC,KAAK,EAAC,CAAA;AACnC,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,EAAA,CAAA;AAC/B,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAA,OAAO,KAAM,CAAA,CAAA,CAAA,CAAA;AACrC,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAS,KAAA;AACjC,IAAO,OAAA,CAAA,EAAG,GAAO,CAAA,CAAA,EAAA,IAAA,CAAK,WAAY,EAAA,CAAA,CAAA,CAAA;AAAA,GACnC,CAAA,CAAA;AACH,EAAA;AAQa,MAAA,MAAA,GAAS,CAAC,GAAwB,KAAA;AAC7C,EAAM,MAAA,KAAA,GAAQ,GAAK,EAAA,KAAA,CAAM,WAAW,CAAA,CAAE,GAAI,CAAA,CAAA,CAAA,KAAK,CAAE,CAAA,WAAA,EAAa,CAAA,IAAK,EAAC,CAAA;AACpE,EAAA,IAAI,MAAM,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,EAAA,CAAA;AAC/B,EAAA,OAAO,MAAM,GAAI,CAAA,CAAAA,IAAOA,KAAAA,IAAAA,CAAI,OAAO,CAAC,CAAA,CAAE,WAAY,EAAA,GAAIA,KAAI,KAAM,CAAA,CAAC,CAAC,CAAA,CAAE,KAAK,EAAE,CAAA,CAAA;AAC7E,EAAA;AAUa,MAAA,KAAA,GAAQ,CAAC,GAA2C,KAAA;AAC/D,EAAA,IAAI,CAAC,GAAA;AAAK,IAAO,OAAA,EAAA,CAAA;AACjB,EAAO,OAAA,GAAA,CACJ,KAAM,CAAA,qBAAqB,CAC3B,CAAA,GAAA,CAAI,OAAK,CAAE,CAAA,IAAA,EAAM,CAAA,CACjB,MAAO,CAAA,CAAA,CAAA,KAAK,CAAC,CAAC,CAAC,CACf,CAAA,GAAA,CAAI,CAAK,CAAA,KAAA,UAAA,CAAW,CAAE,CAAA,WAAA,EAAa,CAAC,CACpC,CAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AACb,EAAA;AASO,MAAM,QAAW,GAAA,CACtB,GACA,EAAA,IAAA,EACA,QAAQ,gBACL,KAAA;AACH,EAAO,OAAA,KAAA,CAAM,IAAK,CAAA,GAAA,CAAI,QAAS,CAAA,KAAK,CAAC,CAAE,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,KAAU,KAAA;AAC5D,IAAA,OAAO,IAAI,OAAQ,CAAA,KAAA,CAAM,CAAI,CAAA,EAAA,IAAA,CAAK,MAAM,CAAG,CAAA,CAAA,CAAA,CAAA;AAAA,KAC1C,GAAG,CAAA,CAAA;AACR,EAAA;AAeO,MAAM,IAAO,GAAA,CAClB,GACA,EAAA,WAAA,GAAsB,GACnB,KAAA;AACH,EAAA,IAAI,CAAC,GAAA;AAAK,IAAO,OAAA,EAAA,CAAA;AACjB,EAAA,MAAM,MAAS,GAAA,WAAA,CAAY,OAAQ,CAAA,UAAA,EAAY,MAAM,CAAA,CAAA;AACrD,EAAA,MAAM,QAAQ,IAAI,MAAA,CAAO,CAAK,EAAA,EAAA,MAAA,CAAA,IAAA,EAAa,aAAa,GAAG,CAAA,CAAA;AAC3D,EAAO,OAAA,GAAA,CAAI,OAAQ,CAAA,KAAA,EAAO,EAAE,CAAA,CAAA;AAC9B;;;;"}