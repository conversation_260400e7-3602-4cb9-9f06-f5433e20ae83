{"version": 3, "file": "object.mjs", "sources": ["../../src/object.ts"], "sourcesContent": ["import { objectify } from './array'\nimport { isArray, isObject, isPrimitive } from './typed'\n\ntype LowercasedKeys<T extends Record<string, any>> = {\n  [P in keyof T & string as Lowercase<P>]: T[P]\n}\n\ntype UppercasedKeys<T extends Record<string, any>> = {\n  [P in keyof T & string as Uppercase<P>]: T[P]\n}\n\n/**\n * Removes (shakes out) undefined entries from an\n * object. Optional second argument shakes out values\n * by custom evaluation.\n */\nexport const shake = <RemovedKeys extends string, T>(\n  obj: T,\n  filter: (value: any) => boolean = x => x === undefined\n): Omit<T, RemovedKeys> => {\n  if (!obj) return {} as T\n  const keys = Object.keys(obj) as (keyof T)[]\n  return keys.reduce((acc, key) => {\n    if (filter(obj[key])) {\n      return acc\n    } else {\n      acc[key] = obj[key]\n      return acc\n    }\n  }, {} as T)\n}\n\n/**\n * Map over all the keys of an object to return\n * a new object\n */\nexport const mapKeys = <\n  TValue,\n  <PERSON><PERSON><PERSON> extends string | number | symbol,\n  <PERSON><PERSON><PERSON><PERSON><PERSON> extends string | number | symbol\n>(\n  obj: Record<TKey, TValue>,\n  mapFunc: (key: TKey, value: TValue) => TNewKey\n): Record<TNewKey, TValue> => {\n  const keys = Object.keys(obj) as TKey[]\n  return keys.reduce((acc, key) => {\n    acc[mapFunc(key as TKey, obj[key])] = obj[key]\n    return acc\n  }, {} as Record<TNewKey, TValue>)\n}\n\n/**\n * Map over all the keys to create a new object\n */\nexport const mapValues = <\n  TValue,\n  TKey extends string | number | symbol,\n  TNewValue\n>(\n  obj: Record<TKey, TValue>,\n  mapFunc: (value: TValue, key: TKey) => TNewValue\n): Record<TKey, TNewValue> => {\n  const keys = Object.keys(obj) as TKey[]\n  return keys.reduce((acc, key) => {\n    acc[key] = mapFunc(obj[key], key)\n    return acc\n  }, {} as Record<TKey, TNewValue>)\n}\n\n/**\n * Map over all the keys to create a new object\n */\nexport const mapEntries = <\n  TKey extends string | number | symbol,\n  TValue,\n  TNewKey extends string | number | symbol,\n  TNewValue\n>(\n  obj: Record<TKey, TValue>,\n  toEntry: (key: TKey, value: TValue) => [TNewKey, TNewValue]\n): Record<TNewKey, TNewValue> => {\n  if (!obj) return {} as Record<TNewKey, TNewValue>\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    const [newKey, newValue] = toEntry(key as TKey, value as TValue)\n    acc[newKey] = newValue\n    return acc\n  }, {} as Record<TNewKey, TNewValue>)\n}\n\n/**\n * Returns an object with { [keys]: value }\n * inverted as { [value]: key }\n */\nexport const invert = <\n  TKey extends string | number | symbol,\n  TValue extends string | number | symbol\n>(\n  obj: Record<TKey, TValue>\n): Record<TValue, TKey> => {\n  if (!obj) return {} as Record<TValue, TKey>\n  const keys = Object.keys(obj) as TKey[]\n  return keys.reduce((acc, key) => {\n    acc[obj[key]] = key\n    return acc\n  }, {} as Record<TValue, TKey>)\n}\n\n/**\n * Convert all keys in an object to lower case\n */\nexport const lowerize = <T extends Record<string, any>>(obj: T) =>\n  mapKeys(obj, k => k.toLowerCase()) as LowercasedKeys<T>\n\n/**\n * Convert all keys in an object to upper case\n */\nexport const upperize = <T extends Record<string, any>>(obj: T) =>\n  mapKeys(obj, k => k.toUpperCase()) as UppercasedKeys<T>\n\n/**\n * Creates a shallow copy of the given obejct/value.\n * @param {*} obj value to clone\n * @returns {*} shallow clone of the given value\n */\nexport const clone = <T>(obj: T): T => {\n  // Primitive values do not need cloning.\n  if (isPrimitive(obj)) {\n    return obj\n  }\n\n  // Binding a function to an empty object creates a\n  // copy function.\n  if (typeof obj === 'function') {\n    return obj.bind({})\n  }\n\n  // Access the constructor and create a new object.\n  // This method can create an array as well.\n  const newObj = new ((obj as object).constructor as { new (): T })()\n\n  // Assign the props.\n  Object.getOwnPropertyNames(obj).forEach(prop => {\n    // Bypass type checking since the primitive cases\n    // are already checked in the beginning\n    ;(newObj as any)[prop] = (obj as any)[prop]\n  })\n\n  return newObj\n}\n\n/**\n * Convert an object to a list, mapping each entry\n * into a list item\n */\nexport const listify = <TValue, TKey extends string | number | symbol, KResult>(\n  obj: Record<TKey, TValue>,\n  toItem: (key: TKey, value: TValue) => KResult\n) => {\n  if (!obj) return []\n  const entries = Object.entries(obj)\n  if (entries.length === 0) return []\n  return entries.reduce((acc, entry) => {\n    acc.push(toItem(entry[0] as TKey, entry[1] as TValue))\n    return acc\n  }, [] as KResult[])\n}\n\n/**\n * Pick a list of properties from an object\n * into a new object\n */\nexport const pick = <T extends object, TKeys extends keyof T>(\n  obj: T,\n  keys: TKeys[]\n): Pick<T, TKeys> => {\n  if (!obj) return {} as Pick<T, TKeys>\n  return keys.reduce((acc, key) => {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) acc[key] = obj[key]\n    return acc\n  }, {} as Pick<T, TKeys>)\n}\n\n/**\n * Omit a list of properties from an object\n * returning a new object with the properties\n * that remain\n */\nexport const omit = <T, TKeys extends keyof T>(\n  obj: T,\n  keys: TKeys[]\n): Omit<T, TKeys> => {\n  if (!obj) return {} as Omit<T, TKeys>\n  if (!keys || keys.length === 0) return obj as Omit<T, TKeys>\n  return keys.reduce(\n    (acc, key) => {\n      // Gross, I know, it's mutating the object, but we\n      // are allowing it in this very limited scope due\n      // to the performance implications of an omit func.\n      // Not a pattern or practice to use elsewhere.\n      delete acc[key]\n      return acc\n    },\n    { ...obj }\n  )\n}\n\n/**\n * Dynamically get a nested value from an array or\n * object with a string.\n *\n * @example get(person, 'friends[0].name')\n */\nexport const get = <TDefault = unknown>(\n  value: any,\n  path: string,\n  defaultValue?: TDefault\n): TDefault => {\n  const segments = path.split(/[\\.\\[\\]]/g)\n  let current: any = value\n  for (const key of segments) {\n    if (current === null) return defaultValue as TDefault\n    if (current === undefined) return defaultValue as TDefault\n    const dequoted = key.replace(/['\"]/g, '')\n    if (dequoted.trim() === '') continue\n    current = current[dequoted]\n  }\n  if (current === undefined) return defaultValue as TDefault\n  return current\n}\n\n/**\n * Opposite of get, dynamically set a nested value into\n * an object using a key path. Does not modify the given\n * initial object.\n *\n * @example\n * set({}, 'name', 'ra') // => { name: 'ra' }\n * set({}, 'cards[0].value', 2) // => { cards: [{ value: 2 }] }\n */\nexport const set = <T extends object, K>(\n  initial: T,\n  path: string,\n  value: K\n): T => {\n  if (!initial) return {} as T\n  if (!path || value === undefined) return initial\n  const segments = path.split(/[\\.\\[\\]]/g).filter(x => !!x.trim())\n  const _set = (node: any) => {\n    if (segments.length > 1) {\n      const key = segments.shift() as string\n      const nextIsNum = /^\\d+$/.test(segments[0])\n      node[key] = node[key] === undefined ? (nextIsNum ? [] : {}) : node[key]\n      _set(node[key])\n    } else {\n      node[segments[0]] = value\n    }\n  }\n  // NOTE: One day, when structuredClone has more\n  // compatability use it to clone the value\n  // https://developer.mozilla.org/en-US/docs/Web/API/structuredClone\n  const cloned = clone(initial)\n  _set(cloned)\n  return cloned\n}\n\n/**\n * Merges two objects together recursivly into a new\n * object applying values from right to left.\n * Recursion only applies to child object properties.\n */\nexport const assign = <X extends Record<string | symbol | number, any>>(\n  initial: X,\n  override: X\n): X => {\n  if (!initial || !override) return initial ?? override ?? {}\n\n  return Object.entries({ ...initial, ...override }).reduce(\n    (acc, [key, value]) => {\n      return {\n        ...acc,\n        [key]: (() => {\n          if (isObject(initial[key])) return assign(initial[key], value)\n          // if (isArray(value)) return value.map(x => assign)\n          return value\n        })()\n      }\n    },\n    {} as X\n  )\n}\n\n/**\n * Get a string list of all key names that exist in\n * an object (deep).\n *\n * @example\n * keys({ name: 'ra' }) // ['name']\n * keys({ name: 'ra', children: [{ name: 'hathor' }] }) // ['name', 'children.0.name']\n */\nexport const keys = <TValue extends object>(value: TValue): string[] => {\n  if (!value) return []\n  const getKeys = (nested: any, paths: string[]): string[] => {\n    if (isObject(nested)) {\n      return Object.entries(nested).flatMap(([k, v]) =>\n        getKeys(v, [...paths, k])\n      )\n    }\n    if (isArray(nested)) {\n      return nested.flatMap((item, i) => getKeys(item, [...paths, `${i}`]))\n    }\n    return [paths.join('.')]\n  }\n  return getKeys(value, [])\n}\n\n/**\n * Flattens a deep object to a single demension, converting\n * the keys to dot notation.\n *\n * @example\n * crush({ name: 'ra', children: [{ name: 'hathor' }] })\n * // { name: 'ra', 'children.0.name': 'hathor' }\n */\nexport const crush = <TValue extends object>(value: TValue): object => {\n  if (!value) return {}\n  return objectify(\n    keys(value),\n    k => k,\n    k => get(value, k)\n  )\n}\n\n/**\n * The opposite of crush, given an object that was\n * crushed into key paths and values will return\n * the original object reconstructed.\n *\n * @example\n * construct({ name: 'ra', 'children.0.name': 'hathor' })\n * // { name: 'ra', children: [{ name: 'hathor' }] }\n */\nexport const construct = <TObject extends object>(obj: TObject): object => {\n  if (!obj) return {}\n  return Object.keys(obj).reduce((acc, path) => {\n    return set(acc, path, (obj as any)[path])\n  }, {})\n}\n"], "names": ["keys"], "mappings": ";;;AAgBO,MAAM,QAAQ,CACnB,GAAA,EACA,MAAkC,GAAA,CAAA,CAAA,KAAK,MAAM,KACpB,CAAA,KAAA;AACzB,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAMA,MAAAA,KAAAA,GAAO,MAAO,CAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AAC5B,EAAA,OAAOA,KAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,GAAQ,KAAA;AAC/B,IAAI,IAAA,MAAA,CAAO,GAAI,CAAA,GAAA,CAAI,CAAG,EAAA;AACpB,MAAO,OAAA,GAAA,CAAA;AAAA,KACF,MAAA;AACL,MAAA,GAAA,CAAI,OAAO,GAAI,CAAA,GAAA,CAAA,CAAA;AACf,MAAO,OAAA,GAAA,CAAA;AAAA,KACT;AAAA,GACF,EAAG,EAAO,CAAA,CAAA;AACZ,EAAA;AAMa,MAAA,OAAA,GAAU,CAKrB,GAAA,EACA,OAC4B,KAAA;AAC5B,EAAMA,MAAAA,KAAAA,GAAO,MAAO,CAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AAC5B,EAAA,OAAOA,KAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,GAAQ,KAAA;AAC/B,IAAA,GAAA,CAAI,OAAQ,CAAA,GAAA,EAAa,GAAI,CAAA,GAAA,CAAI,KAAK,GAAI,CAAA,GAAA,CAAA,CAAA;AAC1C,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAA6B,CAAA,CAAA;AAClC,EAAA;AAKa,MAAA,SAAA,GAAY,CAKvB,GAAA,EACA,OAC4B,KAAA;AAC5B,EAAMA,MAAAA,KAAAA,GAAO,MAAO,CAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AAC5B,EAAA,OAAOA,KAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,GAAQ,KAAA;AAC/B,IAAA,GAAA,CAAI,GAAO,CAAA,GAAA,OAAA,CAAQ,GAAI,CAAA,GAAA,CAAA,EAAM,GAAG,CAAA,CAAA;AAChC,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAA6B,CAAA,CAAA;AAClC,EAAA;AAKa,MAAA,UAAA,GAAa,CAMxB,GAAA,EACA,OAC+B,KAAA;AAC/B,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAO,OAAA,MAAA,CAAO,OAAQ,CAAA,GAAG,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,CAAC,GAAK,EAAA,KAAK,CAAM,KAAA;AACvD,IAAA,MAAM,CAAC,MAAQ,EAAA,QAAQ,CAAI,GAAA,OAAA,CAAQ,KAAa,KAAe,CAAA,CAAA;AAC/D,IAAA,GAAA,CAAI,MAAU,CAAA,GAAA,QAAA,CAAA;AACd,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAgC,CAAA,CAAA;AACrC,EAAA;AAMa,MAAA,MAAA,GAAS,CAIpB,GACyB,KAAA;AACzB,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAMA,MAAAA,KAAAA,GAAO,MAAO,CAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AAC5B,EAAA,OAAOA,KAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,GAAQ,KAAA;AAC/B,IAAA,GAAA,CAAI,IAAI,GAAQ,CAAA,CAAA,GAAA,GAAA,CAAA;AAChB,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAA0B,CAAA,CAAA;AAC/B,EAAA;AAKa,MAAA,QAAA,GAAW,CAAgC,GACtD,KAAA,OAAA,CAAQ,KAAK,CAAK,CAAA,KAAA,CAAA,CAAE,aAAa,EAAA;AAKtB,MAAA,QAAA,GAAW,CAAgC,GACtD,KAAA,OAAA,CAAQ,KAAK,CAAK,CAAA,KAAA,CAAA,CAAE,aAAa,EAAA;AAOtB,MAAA,KAAA,GAAQ,CAAI,GAAc,KAAA;AAErC,EAAI,IAAA,WAAA,CAAY,GAAG,CAAG,EAAA;AACpB,IAAO,OAAA,GAAA,CAAA;AAAA,GACT;AAIA,EAAI,IAAA,OAAO,QAAQ,UAAY,EAAA;AAC7B,IAAO,OAAA,GAAA,CAAI,IAAK,CAAA,EAAE,CAAA,CAAA;AAAA,GACpB;AAIA,EAAM,MAAA,MAAA,GAAS,IAAM,GAAA,CAAe,WAA8B,EAAA,CAAA;AAGlE,EAAA,MAAA,CAAO,mBAAoB,CAAA,GAAG,CAAE,CAAA,OAAA,CAAQ,CAAQ,IAAA,KAAA;AAG7C,IAAC,MAAA,CAAe,QAAS,GAAY,CAAA,IAAA,CAAA,CAAA;AAAA,GACvC,CAAA,CAAA;AAED,EAAO,OAAA,MAAA,CAAA;AACT,EAAA;AAMa,MAAA,OAAA,GAAU,CACrB,GAAA,EACA,MACG,KAAA;AACH,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAM,MAAA,OAAA,GAAU,MAAO,CAAA,OAAA,CAAQ,GAAG,CAAA,CAAA;AAClC,EAAA,IAAI,QAAQ,MAAW,KAAA,CAAA;AAAG,IAAA,OAAO,EAAC,CAAA;AAClC,EAAA,OAAO,OAAQ,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,KAAU,KAAA;AACpC,IAAA,GAAA,CAAI,KAAK,MAAO,CAAA,KAAA,CAAM,CAAY,CAAA,EAAA,KAAA,CAAM,EAAY,CAAC,CAAA,CAAA;AACrD,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAe,CAAA,CAAA;AACpB,EAAA;AAMa,MAAA,IAAA,GAAO,CAClB,GAAA,EACAA,KACmB,KAAA;AACnB,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAA,OAAOA,KAAK,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,GAAQ,KAAA;AAC/B,IAAA,IAAI,MAAO,CAAA,SAAA,CAAU,cAAe,CAAA,IAAA,CAAK,KAAK,GAAG,CAAA;AAAG,MAAA,GAAA,CAAI,OAAO,GAAI,CAAA,GAAA,CAAA,CAAA;AACnE,IAAO,OAAA,GAAA,CAAA;AAAA,GACT,EAAG,EAAoB,CAAA,CAAA;AACzB,EAAA;AAOa,MAAA,IAAA,GAAO,CAClB,GAAA,EACAA,KACmB,KAAA;AACnB,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAI,IAAA,CAACA,KAAQA,IAAAA,KAAAA,CAAK,MAAW,KAAA,CAAA;AAAG,IAAO,OAAA,GAAA,CAAA;AACvC,EAAA,OAAOA,KAAK,CAAA,MAAA;AAAA,IACV,CAAC,KAAK,GAAQ,KAAA;AAKZ,MAAA,OAAO,GAAI,CAAA,GAAA,CAAA,CAAA;AACX,MAAO,OAAA,GAAA,CAAA;AAAA,KACT;AAAA,IACA,EAAE,GAAG,GAAI,EAAA;AAAA,GACX,CAAA;AACF,EAAA;AAQO,MAAM,GAAM,GAAA,CACjB,KACA,EAAA,IAAA,EACA,YACa,KAAA;AACb,EAAM,MAAA,QAAA,GAAW,IAAK,CAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AACvC,EAAA,IAAI,OAAe,GAAA,KAAA,CAAA;AACnB,EAAA,KAAA,MAAW,OAAO,QAAU,EAAA;AAC1B,IAAA,IAAI,OAAY,KAAA,IAAA;AAAM,MAAO,OAAA,YAAA,CAAA;AAC7B,IAAA,IAAI,OAAY,KAAA,KAAA,CAAA;AAAW,MAAO,OAAA,YAAA,CAAA;AAClC,IAAA,MAAM,QAAW,GAAA,GAAA,CAAI,OAAQ,CAAA,OAAA,EAAS,EAAE,CAAA,CAAA;AACxC,IAAI,IAAA,QAAA,CAAS,MAAW,KAAA,EAAA;AAAI,MAAA,SAAA;AAC5B,IAAA,OAAA,GAAU,OAAQ,CAAA,QAAA,CAAA,CAAA;AAAA,GACpB;AACA,EAAA,IAAI,OAAY,KAAA,KAAA,CAAA;AAAW,IAAO,OAAA,YAAA,CAAA;AAClC,EAAO,OAAA,OAAA,CAAA;AACT,EAAA;AAWO,MAAM,GAAM,GAAA,CACjB,OACA,EAAA,IAAA,EACA,KACM,KAAA;AACN,EAAA,IAAI,CAAC,OAAA;AAAS,IAAA,OAAO,EAAC,CAAA;AACtB,EAAI,IAAA,CAAC,QAAQ,KAAU,KAAA,KAAA,CAAA;AAAW,IAAO,OAAA,OAAA,CAAA;AACzC,EAAM,MAAA,QAAA,GAAW,IAAK,CAAA,KAAA,CAAM,WAAW,CAAA,CAAE,MAAO,CAAA,CAAA,CAAA,KAAK,CAAC,CAAC,CAAE,CAAA,IAAA,EAAM,CAAA,CAAA;AAC/D,EAAM,MAAA,IAAA,GAAO,CAAC,IAAc,KAAA;AAC1B,IAAI,IAAA,QAAA,CAAS,SAAS,CAAG,EAAA;AACvB,MAAM,MAAA,GAAA,GAAM,SAAS,KAAM,EAAA,CAAA;AAC3B,MAAA,MAAM,SAAY,GAAA,OAAA,CAAQ,IAAK,CAAA,QAAA,CAAS,CAAE,CAAA,CAAA,CAAA;AAC1C,MAAK,IAAA,CAAA,GAAA,CAAA,GAAO,KAAK,GAAS,CAAA,KAAA,KAAA,CAAA,GAAa,YAAY,EAAC,GAAI,EAAC,GAAK,IAAK,CAAA,GAAA,CAAA,CAAA;AACnE,MAAA,IAAA,CAAK,KAAK,GAAI,CAAA,CAAA,CAAA;AAAA,KACT,MAAA;AACL,MAAA,IAAA,CAAK,SAAS,CAAM,CAAA,CAAA,GAAA,KAAA,CAAA;AAAA,KACtB;AAAA,GACF,CAAA;AAIA,EAAM,MAAA,MAAA,GAAS,MAAM,OAAO,CAAA,CAAA;AAC5B,EAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AACX,EAAO,OAAA,MAAA,CAAA;AACT,EAAA;AAOa,MAAA,MAAA,GAAS,CACpB,OAAA,EACA,QACM,KAAA;AACN,EAAI,IAAA,CAAC,WAAW,CAAC,QAAA;AAAU,IAAO,OAAA,OAAA,IAAW,YAAY,EAAC,CAAA;AAE1D,EAAO,OAAA,MAAA,CAAO,QAAQ,EAAE,GAAG,SAAS,GAAG,QAAA,EAAU,CAAE,CAAA,MAAA;AAAA,IACjD,CAAC,GAAA,EAAK,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AACrB,MAAO,OAAA;AAAA,QACL,GAAG,GAAA;AAAA,QACH,CAAC,OAAO,MAAM;AACZ,UAAI,IAAA,QAAA,CAAS,QAAQ,GAAI,CAAA,CAAA;AAAG,YAAO,OAAA,MAAA,CAAO,OAAQ,CAAA,GAAA,CAAA,EAAM,KAAK,CAAA,CAAA;AAE7D,UAAO,OAAA,KAAA,CAAA;AAAA,SACN,GAAA;AAAA,OACL,CAAA;AAAA,KACF;AAAA,IACA,EAAC;AAAA,GACH,CAAA;AACF,EAAA;AAUa,MAAA,IAAA,GAAO,CAAwB,KAA4B,KAAA;AACtE,EAAA,IAAI,CAAC,KAAA;AAAO,IAAA,OAAO,EAAC,CAAA;AACpB,EAAM,MAAA,OAAA,GAAU,CAAC,MAAA,EAAa,KAA8B,KAAA;AAC1D,IAAI,IAAA,QAAA,CAAS,MAAM,CAAG,EAAA;AACpB,MAAO,OAAA,MAAA,CAAO,OAAQ,CAAA,MAAM,CAAE,CAAA,OAAA;AAAA,QAAQ,CAAC,CAAC,CAAA,EAAG,CAAC,CAAA,KAC1C,OAAQ,CAAA,CAAA,EAAG,CAAC,GAAG,KAAO,EAAA,CAAC,CAAC,CAAA;AAAA,OAC1B,CAAA;AAAA,KACF;AACA,IAAI,IAAA,OAAA,CAAQ,MAAM,CAAG,EAAA;AACnB,MAAA,OAAO,MAAO,CAAA,OAAA,CAAQ,CAAC,IAAA,EAAM,CAAM,KAAA,OAAA,CAAQ,IAAM,EAAA,CAAC,GAAG,KAAA,EAAO,CAAG,EAAA,CAAA,CAAA,CAAG,CAAC,CAAC,CAAA,CAAA;AAAA,KACtE;AACA,IAAA,OAAO,CAAC,KAAA,CAAM,IAAK,CAAA,GAAG,CAAC,CAAA,CAAA;AAAA,GACzB,CAAA;AACA,EAAO,OAAA,OAAA,CAAQ,KAAO,EAAA,EAAE,CAAA,CAAA;AAC1B,EAAA;AAUa,MAAA,KAAA,GAAQ,CAAwB,KAA0B,KAAA;AACrE,EAAA,IAAI,CAAC,KAAA;AAAO,IAAA,OAAO,EAAC,CAAA;AACpB,EAAO,OAAA,SAAA;AAAA,IACL,KAAK,KAAK,CAAA;AAAA,IACV,CAAK,CAAA,KAAA,CAAA;AAAA,IACL,CAAA,CAAA,KAAK,GAAI,CAAA,KAAA,EAAO,CAAC,CAAA;AAAA,GACnB,CAAA;AACF,EAAA;AAWa,MAAA,SAAA,GAAY,CAAyB,GAAyB,KAAA;AACzE,EAAA,IAAI,CAAC,GAAA;AAAK,IAAA,OAAO,EAAC,CAAA;AAClB,EAAA,OAAO,OAAO,IAAK,CAAA,GAAG,EAAE,MAAO,CAAA,CAAC,KAAK,IAAS,KAAA;AAC5C,IAAA,OAAO,GAAI,CAAA,GAAA,EAAK,IAAO,EAAA,GAAA,CAAY,IAAK,CAAA,CAAA,CAAA;AAAA,GAC1C,EAAG,EAAE,CAAA,CAAA;AACP;;;;"}