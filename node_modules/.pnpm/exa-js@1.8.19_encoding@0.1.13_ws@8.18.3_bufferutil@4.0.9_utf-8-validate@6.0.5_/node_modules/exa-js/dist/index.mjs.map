{"version": 3, "sources": ["../src/index.ts", "../src/errors.ts", "../src/websets/base.ts", "../src/websets/enrichments.ts", "../src/websets/events.ts", "../src/websets/openapi.ts", "../src/websets/imports.ts", "../src/websets/items.ts", "../src/websets/monitors.ts", "../src/websets/searches.ts", "../src/websets/webhooks.ts", "../src/websets/client.ts", "../src/zod-utils.ts", "../src/research/base.ts", "../src/research/client.ts"], "sourcesContent": ["import fetch, { Head<PERSON> } from \"cross-fetch\";\nimport { <PERSON>a<PERSON><PERSON><PERSON>, HttpStatusCode } from \"./errors\";\nimport { WebsetsClient } from \"./websets/client\";\nimport { ResearchClient } from \"./research/client\";\nimport { ZodSchema } from \"zod\";\nimport { isZodSchema, zodToJsonSchema } from \"./zod-utils\";\n\n// Use native fetch in Node.js environments\nconst fetchImpl =\n  typeof global !== \"undefined\" && global.fetch ? global.fetch : fetch;\nconst HeadersImpl =\n  typeof global !== \"undefined\" && global.Headers ? global.Headers : Headers;\n\nconst isBeta = false;\n\n/**\n * Search options for performing a search query.\n * @typedef {Object} SearchOptions\n * @property {number} [numResults] - Number of search results to return. Default 10. Max 10 for basic plans.\n * @property {string[]} [includeDomains] - List of domains to include in the search.\n * @property {string[]} [excludeDomains] - List of domains to exclude in the search.\n * @property {string} [startCrawlDate] - Start date for results based on crawl date.\n * @property {string} [endCrawlDate] - End date for results based on crawl date.\n * @property {string} [startPublishedDate] - Start date for results based on published date.\n * @property {string} [endPublishedDate] - End date for results based on published date.\n * @property {string} [category] - A data category to focus on, with higher comprehensivity and data cleanliness. Currently, the only category is company.\n * @property {string[]} [includeText] - List of strings that must be present in webpage text of results. Currently only supports 1 string of up to 5 words.\n * @property {string[]} [excludeText] - List of strings that must not be present in webpage text of results. Currently only supports 1 string of up to 5 words.\n * @property {string[]} [flags] - Experimental flags\n */\nexport type BaseSearchOptions = {\n  numResults?: number;\n  includeDomains?: string[];\n  excludeDomains?: string[];\n  startCrawlDate?: string;\n  endCrawlDate?: string;\n  startPublishedDate?: string;\n  endPublishedDate?: string;\n  category?:\n    | \"company\"\n    | \"research paper\"\n    | \"news\"\n    | \"pdf\"\n    | \"github\"\n    | \"tweet\"\n    | \"personal site\"\n    | \"linkedin profile\"\n    | \"financial report\";\n  includeText?: string[];\n  excludeText?: string[];\n  flags?: string[];\n};\n\n/**\n * Search options for performing a search query.\n * @typedef {Object} RegularSearchOptions\n */\nexport type RegularSearchOptions = BaseSearchOptions & {\n  /**\n   * If true, the search results are moderated for safety.\n   */\n  moderation?: boolean;\n\n  useAutoprompt?: boolean;\n  type?: \"keyword\" | \"neural\" | \"auto\" | \"hybrid\";\n};\n\n/**\n * Options for finding similar links.\n * @typedef {Object} FindSimilarOptions\n * @property {boolean} [excludeSourceDomain] - If true, excludes links from the base domain of the input.\n */\nexport type FindSimilarOptions = BaseSearchOptions & {\n  excludeSourceDomain?: boolean;\n};\n\nexport type ExtrasOptions = { links?: number; imageLinks?: number };\n\n/**\n * Search options for performing a search query.\n * @typedef {Object} ContentsOptions\n * @property {TextContentsOptions | boolean} [text] - Options for retrieving text contents.\n * @property {HighlightsContentsOptions | boolean} [highlights] - Options for retrieving highlights.\n * @property {SummaryContentsOptions | boolean} [summary] - Options for retrieving summary.\n * @property {LivecrawlOptions} [livecrawl] - Options for livecrawling contents. Default is \"never\" for neural/auto search, \"fallback\" for keyword search.\n * @property {number} [livecrawlTimeout] - The timeout for livecrawling. Max and default is 10000ms.\n * @property {boolean} [filterEmptyResults] - If true, filters out results with no contents. Default is true.\n * @property {number} [subpages] - The number of subpages to return for each result, where each subpage is derived from an internal link for the result.\n * @property {string | string[]} [subpageTarget] - Text used to match/rank subpages in the returned subpage list. You could use \"about\" to get *about* page for websites. Note that this is a fuzzy matcher.\n * @property {ExtrasOptions} [extras] - Miscelleneous data derived from results\n */\nexport type ContentsOptions = {\n  text?: TextContentsOptions | true;\n  highlights?: HighlightsContentsOptions | true;\n  summary?: SummaryContentsOptions | true;\n  livecrawl?: LivecrawlOptions;\n  context?: ContextOptions | true;\n  livecrawlTimeout?: number;\n  filterEmptyResults?: boolean;\n  subpages?: number;\n  subpageTarget?: string | string[];\n  extras?: ExtrasOptions;\n} & (typeof isBeta extends true ? {} : {});\n\n/**\n * Options for livecrawling contents\n * @typedef {string} LivecrawlOptions\n */\nexport type LivecrawlOptions =\n  | \"never\"\n  | \"fallback\"\n  | \"always\"\n  | \"auto\"\n  | \"preferred\";\n\n/**\n * Options for retrieving text from page.\n * @typedef {Object} TextContentsOptions\n * @property {number} [maxCharacters] - The maximum number of characters to return.\n * @property {boolean} [includeHtmlTags] - If true, includes HTML tags in the returned text. Default: false\n */\nexport type TextContentsOptions = {\n  maxCharacters?: number;\n  includeHtmlTags?: boolean;\n};\n\n/**\n * Options for retrieving highlights from page.\n * @typedef {Object} HighlightsContentsOptions\n * @property {string} [query] - The query string to use for highlights search.\n * @property {number} [numSentences] - The number of sentences to return for each highlight.\n * @property {number} [highlightsPerUrl] - The number of highlights to return for each URL.\n */\nexport type HighlightsContentsOptions = {\n  query?: string;\n  numSentences?: number;\n  highlightsPerUrl?: number;\n};\n\n/**\n * Options for retrieving summary from page.\n * @typedef {Object} SummaryContentsOptions\n * @property {string} [query] - The query string to use for summary generation.\n * @property {JSONSchema} [schema] - JSON schema for structured output from summary.\n */\nexport type SummaryContentsOptions = {\n  query?: string;\n  schema?: Record<string, unknown> | ZodSchema;\n};\n\n/**\n * @deprecated Use Record<string, unknown> instead.\n */\nexport type JSONSchema = Record<string, unknown>;\n\n/**\n * Options for retrieving the context from a list of search results. The context is a string\n * representation of all the search results.\n * @typedef {Object} ContextOptions\n * @property {number} [maxCharacters] - The maximum number of characters.\n */\nexport type ContextOptions = {\n  maxCharacters?: number;\n};\n\n/**\n * @typedef {Object} TextResponse\n * @property {string} text - Text from page\n */\nexport type TextResponse = { text: string };\n\n/**\n * @typedef {Object} HighlightsResponse\n * @property {string[]} highlights - The highlights as an array of strings.\n * @property {number[]} highlightScores - The corresponding scores as an array of floats, 0 to 1\n */\nexport type HighlightsResponse = {\n  highlights: string[];\n  highlightScores: number[];\n};\n\n/**\n * @typedef {Object} SummaryResponse\n * @property {string} summary - The generated summary of the page content.\n */\nexport type SummaryResponse = { summary: string };\n\n/**\n * @typedef {Object} ExtrasResponse\n * @property {string[]} links - The links on the page of a result\n * @property {string[]} imageLinks - The image links on the page of a result\n */\nexport type ExtrasResponse = {\n  extras: { links?: string[]; imageLinks?: string[] };\n};\n\n/**\n * @typedef {Object} SubpagesResponse\n * @property {ContentsResultComponent<T extends ContentsOptions>} subpages - The subpages for a result\n */\nexport type SubpagesResponse<T extends ContentsOptions> = {\n  subpages: ContentsResultComponent<T>[];\n};\n\nexport type Default<T extends {}, U> = [keyof T] extends [never] ? U : T;\n\n/**\n * @typedef {Object} ContentsResultComponent\n * Depending on 'ContentsOptions', this yields a combination of 'TextResponse', 'HighlightsResponse', 'SummaryResponse', or an empty object.\n *\n * @template T - A type extending from 'ContentsOptions'.\n */\nexport type ContentsResultComponent<T extends ContentsOptions> = Default<\n  (T[\"text\"] extends object | true ? TextResponse : {}) &\n    (T[\"highlights\"] extends object | true ? HighlightsResponse : {}) &\n    (T[\"summary\"] extends object | true ? SummaryResponse : {}) &\n    (T[\"subpages\"] extends number ? SubpagesResponse<T> : {}) &\n    (T[\"extras\"] extends object ? ExtrasResponse : {}),\n  TextResponse\n>;\n\n/**\n * Represents the cost breakdown related to contents retrieval. Fields are optional because\n * only non-zero costs are included.\n * @typedef {Object} CostDollarsContents\n * @property {number} [text] - The cost in dollars for retrieving text.\n * @property {number} [highlights] - The cost in dollars for retrieving highlights.\n * @property {number} [summary] - The cost in dollars for retrieving summary.\n */\nexport type CostDollarsContents = {\n  text?: number;\n  highlights?: number;\n  summary?: number;\n};\n\n/**\n * Represents the cost breakdown related to search. Fields are optional because\n * only non-zero costs are included.\n * @typedef {Object} CostDollarsSeearch\n * @property {number} [neural] - The cost in dollars for neural search.\n * @property {number} [keyword] - The cost in dollars for keyword search.\n */\nexport type CostDollarsSeearch = {\n  neural?: number;\n  keyword?: number;\n};\n\n/**\n * Represents the total cost breakdown. Only non-zero costs are included.\n * @typedef {Object} CostDollars\n * @property {number} total - The total cost in dollars.\n * @property {CostDollarsSeearch} [search] - The cost breakdown for search.\n * @property {CostDollarsContents} [contents] - The cost breakdown for contents.\n */\nexport type CostDollars = {\n  total: number;\n  search?: CostDollarsSeearch;\n  contents?: CostDollarsContents;\n};\n\n/**\n * Represents a search result object.\n * @typedef {Object} SearchResult\n * @property {string} title - The title of the search result.\n * @property {string} url - The URL of the search result.\n * @property {string} [publishedDate] - The estimated creation date of the content.\n * @property {string} [author] - The author of the content, if available.\n * @property {number} [score] - Similarity score between the query/url and the result.\n * @property {string} id - The temporary ID for the document.\n * @property {string} [image] - A representative image for the content, if any.\n * @property {string} [favicon] - A favicon for the site, if any.\n */\nexport type SearchResult<T extends ContentsOptions> = {\n  title: string | null;\n  url: string;\n  publishedDate?: string;\n  author?: string;\n  score?: number;\n  id: string;\n  image?: string;\n  favicon?: string;\n} & ContentsResultComponent<T>;\n\n/**\n * Represents a search response object.\n * @typedef {Object} SearchResponse\n * @property {Result[]} results - The list of search results.\n * @property {string} [context] - The context for the search.\n * @property {string} [autopromptString] - The autoprompt string, if applicable.\n * @property {string} [autoDate] - The autoprompt date, if applicable.\n * @property {string} requestId - The request ID for the search.\n * @property {CostDollars} [costDollars] - The cost breakdown for this request.\n */\nexport type SearchResponse<T extends ContentsOptions> = {\n  results: SearchResult<T>[];\n  context?: string;\n  autopromptString?: string;\n  autoDate?: string;\n  requestId: string;\n  statuses?: Array<Status>;\n  costDollars?: CostDollars;\n};\n\nexport type Status = {\n  id: string;\n  status: string;\n  source: string;\n};\n\n/**\n * Options for the answer endpoint\n * @typedef {Object} AnswerOptions\n * @property {boolean} [stream] - Whether to stream the response. Default false.\n * @property {boolean} [text] - Whether to include text in the source results. Default false.\n * @property {\"exa\"} [model] - The model to use for generating the answer. Default \"exa\".\n * @property {string} [systemPrompt] - A system prompt to guide the LLM's behavior when generating the answer.\n * @property {Object} [outputSchema] - A JSON Schema specification for the structure you expect the output to take\n */\nexport type AnswerOptions = {\n  stream?: boolean;\n  text?: boolean;\n  model?: \"exa\";\n  systemPrompt?: string;\n  outputSchema?: Record<string, unknown>;\n};\n\n/**\n * Represents an answer response object from the /answer endpoint.\n * @typedef {Object} AnswerResponse\n * @property {string | Object} answer - The generated answer text (or an object matching `outputSchema`, if provided)\n * @property {SearchResult<{}>[]} citations - The sources used to generate the answer.\n * @property {CostDollars} [costDollars] - The cost breakdown for this request.\n * @property {string} [requestId] - Optional request ID for the answer.\n */\nexport type AnswerResponse = {\n  answer: string | Record<string, unknown>;\n  citations: SearchResult<{}>[];\n  requestId?: string;\n  costDollars?: CostDollars;\n};\n\nexport type AnswerStreamChunk = {\n  /**\n   * The partial text content of the answer (if present in this chunk).\n   */\n  content?: string;\n  /**\n   * Citations associated with the current chunk of text (if present).\n   */\n  citations?: Array<{\n    id: string;\n    url: string;\n    title?: string;\n    publishedDate?: string;\n    author?: string;\n    text?: string;\n  }>;\n};\n\n/**\n * Represents a streaming answer response chunk from the /answer endpoint.\n * @typedef {Object} AnswerStreamResponse\n * @property {string} [answer] - A chunk of the generated answer text.\n * @property {SearchResult<{}>[]]} [citations] - The sources used to generate the answer.\n */\nexport type AnswerStreamResponse = {\n  answer?: string;\n  citations?: SearchResult<{}>[];\n};\n\n// ==========================================\n// Zod-Enhanced Types\n// ==========================================\n\n/**\n * Enhanced answer options that accepts either JSON schema or Zod schema\n */\nexport type AnswerOptionsTyped<T> = Omit<AnswerOptions, \"outputSchema\"> & {\n  outputSchema: T;\n};\n\n/**\n * Enhanced answer response with strongly typed answer when using Zod\n */\nexport type AnswerResponseTyped<T> = Omit<AnswerResponse, \"answer\"> & {\n  answer: T;\n};\n\n/**\n * Enhanced summary contents options that accepts either JSON schema or Zod schema\n */\nexport type SummaryContentsOptionsTyped<T> = Omit<\n  SummaryContentsOptions,\n  \"schema\"\n> & {\n  schema: T;\n};\n\n/**\n * Enhanced research task output options that accepts either JSON schema or Zod schema\n */\nexport type ResearchTaskOutputTyped<T> = {\n  inferSchema?: boolean;\n  schema: T;\n};\n\n/**\n * Enhanced research task creation params with zod schema support\n */\nexport type ResearchCreateTaskParamsTyped<T> = {\n  instructions: string;\n  model?: \"exa-research\" | \"exa-research-pro\";\n  output?: ResearchTaskOutputTyped<T>;\n};\n\n/**\n * The Exa class encapsulates the API's endpoints.\n */\nexport class Exa {\n  private baseURL: string;\n  private headers: Headers;\n\n  /**\n   * Websets API client\n   */\n  websets: WebsetsClient;\n\n  /**\n   * Research API client\n   */\n  research: ResearchClient;\n\n  /**\n   * Helper method to separate out the contents-specific options from the rest.\n   */\n  private extractContentsOptions<T extends ContentsOptions>(\n    options: T\n  ): {\n    contentsOptions: ContentsOptions;\n    restOptions: Omit<T, keyof ContentsOptions>;\n  } {\n    const {\n      text,\n      highlights,\n      summary,\n      subpages,\n      subpageTarget,\n      extras,\n      livecrawl,\n      livecrawlTimeout,\n      context,\n      ...rest\n    } = options;\n\n    const contentsOptions: ContentsOptions = {};\n\n    // Default: if none of text, summary, or highlights is provided, we retrieve text\n    if (\n      text === undefined &&\n      summary === undefined &&\n      highlights === undefined &&\n      extras === undefined\n    ) {\n      contentsOptions.text = true;\n    }\n\n    if (text !== undefined) contentsOptions.text = text;\n    if (summary !== undefined) {\n      // Handle zod schema conversion for summary\n      if (\n        typeof summary === \"object\" &&\n        summary !== null &&\n        \"schema\" in summary &&\n        summary.schema &&\n        isZodSchema(summary.schema)\n      ) {\n        contentsOptions.summary = {\n          ...summary,\n          schema: zodToJsonSchema(summary.schema),\n        };\n      } else {\n        contentsOptions.summary = summary;\n      }\n    }\n    if (highlights !== undefined) contentsOptions.highlights = highlights;\n    if (subpages !== undefined) contentsOptions.subpages = subpages;\n    if (subpageTarget !== undefined)\n      contentsOptions.subpageTarget = subpageTarget;\n    if (extras !== undefined) contentsOptions.extras = extras;\n    if (livecrawl !== undefined) contentsOptions.livecrawl = livecrawl;\n    if (livecrawlTimeout !== undefined)\n      contentsOptions.livecrawlTimeout = livecrawlTimeout;\n    if (context !== undefined) contentsOptions.context = context;\n\n    return {\n      contentsOptions,\n      restOptions: rest as Omit<T, keyof ContentsOptions>,\n    };\n  }\n\n  /**\n   * Constructs the Exa API client.\n   * @param {string} apiKey - The API key for authentication.\n   * @param {string} [baseURL] - The base URL of the Exa API.\n   */\n  constructor(apiKey?: string, baseURL: string = \"https://api.exa.ai\") {\n    this.baseURL = baseURL;\n    if (!apiKey) {\n      apiKey = process.env.EXASEARCH_API_KEY;\n      if (!apiKey) {\n        throw new ExaError(\n          \"API key must be provided as an argument or as an environment variable (EXASEARCH_API_KEY)\",\n          HttpStatusCode.Unauthorized\n        );\n      }\n    }\n    this.headers = new HeadersImpl({\n      \"x-api-key\": apiKey,\n      \"Content-Type\": \"application/json\",\n      \"User-Agent\": \"exa-node 1.4.0\",\n    });\n\n    // Initialize the Websets client\n    this.websets = new WebsetsClient(this);\n    // Initialize the Research client\n    this.research = new ResearchClient(this);\n  }\n\n  /**\n   * Makes a request to the Exa API.\n   * @param {string} endpoint - The API endpoint to call.\n   * @param {string} method - The HTTP method to use.\n   * @param {any} [body] - The request body for POST requests.\n   * @param {Record<string, any>} [params] - The query parameters.\n   * @returns {Promise<any>} The response from the API.\n   * @throws {ExaError} When any API request fails with structured error information\n   */\n  async request<T = unknown>(\n    endpoint: string,\n    method: string,\n    body?: any,\n    params?: Record<string, any>\n  ): Promise<T> {\n    // Build URL with query parameters if provided\n    let url = this.baseURL + endpoint;\n    if (params && Object.keys(params).length > 0) {\n      const searchParams = new URLSearchParams();\n      for (const [key, value] of Object.entries(params)) {\n        if (Array.isArray(value)) {\n          for (const item of value) {\n            searchParams.append(key, item);\n          }\n        } else if (value !== undefined) {\n          searchParams.append(key, String(value));\n        }\n      }\n      url += `?${searchParams.toString()}`;\n    }\n\n    const response = await fetchImpl(url, {\n      method,\n      headers: this.headers,\n      body: body ? JSON.stringify(body) : undefined,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n\n      if (!errorData.statusCode) {\n        errorData.statusCode = response.status;\n      }\n      if (!errorData.timestamp) {\n        errorData.timestamp = new Date().toISOString();\n      }\n      if (!errorData.path) {\n        errorData.path = endpoint;\n      }\n\n      // For other APIs, throw a simple ExaError with just message and status\n      let message = errorData.error || \"Unknown error\";\n      if (errorData.message) {\n        message += (message.length > 0 ? \". \" : \"\") + errorData.message;\n      }\n      throw new ExaError(\n        message,\n        response.status,\n        errorData.timestamp,\n        errorData.path\n      );\n    }\n\n    // If the server responded with an SSE stream, parse it and return the final payload.\n    const contentType = response.headers.get(\"content-type\") || \"\";\n    if (contentType.includes(\"text/event-stream\")) {\n      return (await this.parseSSEStream<T>(response)) as T;\n    }\n\n    return (await response.json()) as T;\n  }\n\n  async rawRequest(\n    endpoint: string,\n    method: string = \"POST\",\n    body?: Record<string, unknown>,\n    queryParams?: Record<\n      string,\n      string | number | boolean | string[] | undefined\n    >\n  ): Promise<Response> {\n    let url = this.baseURL + endpoint;\n\n    if (queryParams) {\n      const searchParams = new URLSearchParams();\n      for (const [key, value] of Object.entries(queryParams)) {\n        if (Array.isArray(value)) {\n          for (const item of value) {\n            searchParams.append(key, String(item));\n          }\n        } else if (value !== undefined) {\n          searchParams.append(key, String(value));\n        }\n      }\n      url += `?${searchParams.toString()}`;\n    }\n\n    const response = await fetchImpl(url, {\n      method,\n      headers: this.headers,\n      body: body ? JSON.stringify(body) : undefined,\n    });\n\n    return response;\n  }\n\n  /**\n   * Performs a search with an Exa prompt-engineered query.\n   *\n   * @param {string} query - The query string.\n   * @param {RegularSearchOptions} [options] - Additional search options\n   * @returns {Promise<SearchResponse<{}>>} A list of relevant search results.\n   */\n  async search(\n    query: string,\n    options?: RegularSearchOptions\n  ): Promise<SearchResponse<{}>> {\n    return await this.request(\"/search\", \"POST\", { query, ...options });\n  }\n\n  /**\n   * Performs a search with an Exa prompt-engineered query and returns the contents of the documents.\n   *\n   * @param {string} query - The query string.\n   * @param {RegularSearchOptions & T} [options] - Additional search + contents options\n   * @returns {Promise<SearchResponse<T>>} A list of relevant search results with requested contents.\n   */\n  async searchAndContents<T extends ContentsOptions>(\n    query: string,\n    options?: RegularSearchOptions & T\n  ): Promise<SearchResponse<T>> {\n    const { contentsOptions, restOptions } =\n      options === undefined\n        ? { contentsOptions: { text: true }, restOptions: {} }\n        : this.extractContentsOptions(options);\n\n    return await this.request(\"/search\", \"POST\", {\n      query,\n      contents: contentsOptions,\n      ...restOptions,\n    });\n  }\n\n  /**\n   * Finds similar links to the provided URL.\n   * @param {string} url - The URL for which to find similar links.\n   * @param {FindSimilarOptions} [options] - Additional options for finding similar links.\n   * @returns {Promise<SearchResponse<{}>>} A list of similar search results.\n   */\n  async findSimilar(\n    url: string,\n    options?: FindSimilarOptions\n  ): Promise<SearchResponse<{}>> {\n    return await this.request(\"/findSimilar\", \"POST\", { url, ...options });\n  }\n\n  /**\n   * Finds similar links to the provided URL and returns the contents of the documents.\n   * @param {string} url - The URL for which to find similar links.\n   * @param {FindSimilarOptions & T} [options] - Additional options for finding similar links + contents.\n   * @returns {Promise<SearchResponse<T>>} A list of similar search results, including requested contents.\n   */\n  async findSimilarAndContents<T extends ContentsOptions>(\n    url: string,\n    options?: FindSimilarOptions & T\n  ): Promise<SearchResponse<T>> {\n    const { contentsOptions, restOptions } =\n      options === undefined\n        ? { contentsOptions: { text: true }, restOptions: {} }\n        : this.extractContentsOptions(options);\n\n    return await this.request(\"/findSimilar\", \"POST\", {\n      url,\n      contents: contentsOptions,\n      ...restOptions,\n    });\n  }\n\n  /**\n   * Retrieves contents of documents based on URLs.\n   * @param {string | string[] | SearchResult[]} urls - A URL or array of URLs, or an array of SearchResult objects.\n   * @param {ContentsOptions} [options] - Additional options for retrieving document contents.\n   * @returns {Promise<SearchResponse<T>>} A list of document contents for the requested URLs.\n   */\n  async getContents<T extends ContentsOptions>(\n    urls: string | string[] | SearchResult<T>[],\n    options?: T\n  ): Promise<SearchResponse<T>> {\n    if (!urls || (Array.isArray(urls) && urls.length === 0)) {\n      throw new ExaError(\n        \"Must provide at least one URL\",\n        HttpStatusCode.BadRequest\n      );\n    }\n\n    let requestUrls: string[];\n\n    if (typeof urls === \"string\") {\n      requestUrls = [urls];\n    } else if (typeof urls[0] === \"string\") {\n      requestUrls = urls as string[];\n    } else {\n      requestUrls = (urls as SearchResult<T>[]).map((result) => result.url);\n    }\n\n    const payload = {\n      urls: requestUrls,\n      ...options,\n    };\n\n    return await this.request(\"/contents\", \"POST\", payload);\n  }\n\n  /**\n   * Generate an answer with Zod schema for strongly typed output\n   */\n  async answer<T>(\n    query: string,\n    options: AnswerOptionsTyped<ZodSchema<T>>\n  ): Promise<AnswerResponseTyped<T>>;\n\n  /**\n   * Generate an answer to a query.\n   * @param {string} query - The question or query to answer.\n   * @param {AnswerOptions} [options] - Additional options for answer generation.\n   * @returns {Promise<AnswerResponse>} The generated answer and source references.\n   *\n   * Example with systemPrompt:\n   * ```ts\n   * const answer = await exa.answer(\"What is quantum computing?\", {\n   *   text: true,\n   *   model: \"exa\",\n   *   systemPrompt: \"Answer in a technical manner suitable for experts.\"\n   * });\n   * ```\n   *\n   * Note: For streaming responses, use the `streamAnswer` method:\n   * ```ts\n   * for await (const chunk of exa.streamAnswer(query)) {\n   *   // Handle chunks\n   * }\n   * ```\n   */\n  async answer(query: string, options?: AnswerOptions): Promise<AnswerResponse>;\n\n  async answer<T>(\n    query: string,\n    options?: AnswerOptions | AnswerOptionsTyped<ZodSchema<T>>\n  ): Promise<AnswerResponse | AnswerResponseTyped<T>> {\n    if (options?.stream) {\n      throw new ExaError(\n        \"For streaming responses, please use streamAnswer() instead:\\n\\n\" +\n          \"for await (const chunk of exa.streamAnswer(query)) {\\n\" +\n          \"  // Handle chunks\\n\" +\n          \"}\",\n        HttpStatusCode.BadRequest\n      );\n    }\n\n    // For non-streaming requests, make a regular API call\n    let outputSchema = options?.outputSchema;\n\n    // Convert Zod schema to JSON schema if needed\n    if (outputSchema && isZodSchema(outputSchema)) {\n      outputSchema = zodToJsonSchema(outputSchema);\n    }\n\n    const requestBody = {\n      query,\n      stream: false,\n      text: options?.text ?? false,\n      model: options?.model ?? \"exa\",\n      systemPrompt: options?.systemPrompt,\n      outputSchema,\n    };\n\n    return await this.request(\"/answer\", \"POST\", requestBody);\n  }\n\n  /**\n   * Stream an answer with Zod schema for structured output (non-streaming content)\n   * Note: Structured output works only with non-streaming content, not with streaming chunks\n   */\n  streamAnswer<T>(\n    query: string,\n    options: {\n      text?: boolean;\n      model?: \"exa\" | \"exa-pro\";\n      systemPrompt?: string;\n      outputSchema: ZodSchema<T>;\n    }\n  ): AsyncGenerator<AnswerStreamChunk>;\n\n  /**\n   * Stream an answer as an async generator\n   *\n   * Each iteration yields a chunk with partial text (`content`) or new citations.\n   * Use this if you'd like to read the answer incrementally, e.g. in a chat UI.\n   *\n   * Example usage:\n   * ```ts\n   * for await (const chunk of exa.streamAnswer(\"What is quantum computing?\", {\n   *   text: false,\n   *   systemPrompt: \"Answer in a concise manner suitable for beginners.\"\n   * })) {\n   *   if (chunk.content) process.stdout.write(chunk.content);\n   *   if (chunk.citations) {\n   *     console.log(\"\\nCitations: \", chunk.citations);\n   *   }\n   * }\n   * ```\n   */\n  streamAnswer(\n    query: string,\n    options?: {\n      text?: boolean;\n      model?: \"exa\" | \"exa-pro\";\n      systemPrompt?: string;\n      outputSchema?: Record<string, unknown>;\n    }\n  ): AsyncGenerator<AnswerStreamChunk>;\n\n  async *streamAnswer<T>(\n    query: string,\n    options?: {\n      text?: boolean;\n      model?: \"exa\" | \"exa-pro\";\n      systemPrompt?: string;\n      outputSchema?: Record<string, unknown> | ZodSchema<T>;\n    }\n  ): AsyncGenerator<AnswerStreamChunk> {\n    // Convert Zod schema to JSON schema if needed\n    let outputSchema = options?.outputSchema;\n    if (outputSchema && isZodSchema(outputSchema)) {\n      outputSchema = zodToJsonSchema(outputSchema);\n    }\n\n    // Build the POST body and fetch the streaming response.\n    const body = {\n      query,\n      text: options?.text ?? false,\n      stream: true,\n      model: options?.model ?? \"exa\",\n      systemPrompt: options?.systemPrompt,\n      outputSchema,\n    };\n\n    const response = await fetchImpl(this.baseURL + \"/answer\", {\n      method: \"POST\",\n      headers: this.headers,\n      body: JSON.stringify(body),\n    });\n\n    if (!response.ok) {\n      const message = await response.text();\n      throw new ExaError(message, response.status, new Date().toISOString());\n    }\n\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new ExaError(\n        \"No response body available for streaming.\",\n        500,\n        new Date().toISOString()\n      );\n    }\n\n    const decoder = new TextDecoder();\n    let buffer = \"\";\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split(\"\\n\");\n        buffer = lines.pop() || \"\";\n\n        for (const line of lines) {\n          if (!line.startsWith(\"data: \")) continue;\n\n          const jsonStr = line.replace(/^data:\\s*/, \"\").trim();\n          if (!jsonStr || jsonStr === \"[DONE]\") {\n            continue;\n          }\n\n          let chunkData: any;\n          try {\n            chunkData = JSON.parse(jsonStr);\n          } catch (err) {\n            continue;\n          }\n\n          const chunk = this.processChunk(chunkData);\n          if (chunk.content || chunk.citations) {\n            yield chunk;\n          }\n        }\n      }\n\n      if (buffer.startsWith(\"data: \")) {\n        const leftover = buffer.replace(/^data:\\s*/, \"\").trim();\n        if (leftover && leftover !== \"[DONE]\") {\n          try {\n            const chunkData = JSON.parse(leftover);\n            const chunk = this.processChunk(chunkData);\n            if (chunk.content || chunk.citations) {\n              yield chunk;\n            }\n          } catch (e) {}\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  }\n\n  private processChunk(chunkData: any): AnswerStreamChunk {\n    let content: string | undefined;\n    let citations:\n      | Array<{\n          id: string;\n          url: string;\n          title?: string;\n          publishedDate?: string;\n          author?: string;\n          text?: string;\n        }>\n      | undefined;\n\n    if (\n      chunkData.choices &&\n      chunkData.choices[0] &&\n      chunkData.choices[0].delta\n    ) {\n      content = chunkData.choices[0].delta.content;\n    }\n\n    if (chunkData.citations && chunkData.citations !== \"null\") {\n      citations = chunkData.citations.map((c: any) => ({\n        id: c.id,\n        url: c.url,\n        title: c.title,\n        publishedDate: c.publishedDate,\n        author: c.author,\n        text: c.text,\n      }));\n    }\n\n    return { content, citations };\n  }\n\n  private async parseSSEStream<T>(response: Response): Promise<T> {\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new ExaError(\n        \"No response body available for streaming.\",\n        500,\n        new Date().toISOString()\n      );\n    }\n\n    const decoder = new TextDecoder();\n    let buffer = \"\";\n\n    return new Promise<T>(async (resolve, reject) => {\n      try {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n\n          buffer += decoder.decode(value, { stream: true });\n          const lines = buffer.split(\"\\n\");\n          buffer = lines.pop() || \"\";\n\n          for (const line of lines) {\n            if (!line.startsWith(\"data: \")) continue;\n\n            const jsonStr = line.replace(/^data:\\s*/, \"\").trim();\n            if (!jsonStr || jsonStr === \"[DONE]\") {\n              continue;\n            }\n\n            let chunk: any;\n            try {\n              chunk = JSON.parse(jsonStr);\n            } catch {\n              continue; // Ignore malformed JSON lines\n            }\n\n            switch (chunk.tag) {\n              case \"complete\":\n                reader.releaseLock();\n                resolve(chunk.data as T);\n                return;\n              case \"error\": {\n                const message = chunk.error?.message || \"Unknown error\";\n                reader.releaseLock();\n                reject(\n                  new ExaError(\n                    message,\n                    HttpStatusCode.InternalServerError,\n                    new Date().toISOString()\n                  )\n                );\n                return;\n              }\n              // 'progress' and any other tags are ignored for the blocking variant\n              default:\n                break;\n            }\n          }\n        }\n\n        // If we exit the loop without receiving a completion event\n        reject(\n          new ExaError(\n            \"Stream ended without a completion event.\",\n            HttpStatusCode.InternalServerError,\n            new Date().toISOString()\n          )\n        );\n      } catch (err) {\n        reject(err as Error);\n      } finally {\n        try {\n          reader.releaseLock();\n        } catch {\n          /* ignore */\n        }\n      }\n    });\n  }\n}\n\n// Re-export Websets related types and enums\nexport * from \"./websets\";\n// Re-export Research related clients\nexport * from \"./research\";\n\n// Export the main class\nexport default Exa;\n\n// Re-export errors\nexport * from \"./errors\";\n", "/**\n * HTTP status codes\n */\nexport enum HttpStatusCode {\n  BadRequest = 400,\n  NotFound = 404,\n  Unauthorized = 401,\n  Forbidden = 403,\n  TooManyRequests = 429,\n  RequestTimeout = 408,\n  InternalServerError = 500,\n  ServiceUnavailable = 503,\n}\n\n/**\n * Base error class for all Exa API errors\n */\nexport class ExaError extends Error {\n  /**\n   * HTTP status code\n   */\n  statusCode: number;\n\n  /**\n   * ISO timestamp from API\n   */\n  timestamp?: string;\n\n  /**\n   * Path that caused the error (may be undefined for client-side errors)\n   */\n  path?: string;\n\n  /**\n   * Create a new ExaError\n   * @param message Error message\n   * @param statusCode HTTP status code\n   * @param timestamp ISO timestamp from API\n   * @param path Path that caused the error\n   */\n  constructor(\n    message: string,\n    statusCode: number,\n    timestamp?: string,\n    path?: string\n  ) {\n    super(message);\n    this.name = \"ExaError\";\n    this.statusCode = statusCode;\n    this.timestamp = timestamp ?? new Date().toISOString();\n    this.path = path;\n  }\n}\n", "/**\n * Base client for Websets API\n */\nimport { Exa } from \"../index\";\n\n/**\n * Type for API query parameters\n */\ntype QueryParams = Record<\n  string,\n  string | number | boolean | string[] | undefined\n>;\n\n/**\n * Type for API request body\n */\ninterface RequestBody {\n  [key: string]: unknown;\n}\n\n/**\n * Common pagination parameters\n */\nexport interface PaginationParams {\n  /**\n   * Cursor for pagination\n   */\n  cursor?: string;\n\n  /**\n   * Maximum number of items per page\n   */\n  limit?: number;\n}\n\n/**\n * Base client class for all Websets-related API clients\n */\nexport class WebsetsBaseClient {\n  protected client: Exa;\n\n  /**\n   * Initialize a new Websets base client\n   * @param client The Exa client instance\n   */\n  constructor(client: Exa) {\n    this.client = client;\n  }\n\n  /**\n   * Make a request to the Websets API\n   * @param endpoint The endpoint path\n   * @param method The HTTP method\n   * @param data Optional request body data\n   * @param params Optional query parameters\n   * @returns The response JSON\n   * @throws ExaError with API error details if the request fails\n   */\n  protected async request<T = unknown>(\n    endpoint: string,\n    method: string = \"POST\",\n    data?: RequestBody,\n    params?: QueryParams\n  ): Promise<T> {\n    return this.client.request<T>(`/websets${endpoint}`, method, data, params);\n  }\n\n  /**\n   * Helper to build pagination parameters\n   * @param pagination The pagination parameters\n   * @returns QueryParams object with pagination parameters\n   */\n  protected buildPaginationParams(pagination?: PaginationParams): QueryParams {\n    const params: QueryParams = {};\n    if (!pagination) return params;\n\n    if (pagination.cursor) params.cursor = pagination.cursor;\n    if (pagination.limit) params.limit = pagination.limit;\n\n    return params;\n  }\n}\n", "/**\n * Client for managing Webset Enrichments\n */\nimport { WebsetsBaseClient } from \"./base\";\nimport { CreateEnrichmentParameters, WebsetEnrichment } from \"./openapi\";\n\n/**\n * Client for managing Webset Enrichments\n */\nexport class WebsetEnrichmentsClient extends WebsetsBaseClient {\n  /**\n   * Create an Enrichment for a Webset\n   * @param websetId The ID of the Webset\n   * @param params The enrichment parameters\n   * @returns The created Webset Enrichment\n   */\n  async create(\n    websetId: string,\n    params: CreateEnrichmentParameters\n  ): Promise<WebsetEnrichment> {\n    return this.request<WebsetEnrichment>(\n      `/v0/websets/${websetId}/enrichments`,\n      \"POST\",\n      params\n    );\n  }\n\n  /**\n   * Get an Enrichment by ID\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Enrichment\n   * @returns The Webset Enrichment\n   */\n  async get(websetId: string, id: string): Promise<WebsetEnrichment> {\n    return this.request<WebsetEnrichment>(\n      `/v0/websets/${websetId}/enrichments/${id}`,\n      \"GET\"\n    );\n  }\n\n  /**\n   * Delete an Enrichment\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Enrichment\n   * @returns The deleted Webset Enrichment\n   */\n  async delete(websetId: string, id: string): Promise<WebsetEnrichment> {\n    return this.request<WebsetEnrichment>(\n      `/v0/websets/${websetId}/enrichments/${id}`,\n      \"DELETE\"\n    );\n  }\n\n  /**\n   * Cancel a running Enrichment\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Enrichment\n   * @returns The canceled Webset Enrichment\n   */\n  async cancel(websetId: string, id: string): Promise<WebsetEnrichment> {\n    return this.request<WebsetEnrichment>(\n      `/v0/websets/${websetId}/enrichments/${id}/cancel`,\n      \"POST\"\n    );\n  }\n}\n", "import { Exa } from \"../index\";\nimport { WebsetsBaseClient } from \"./base\";\nimport { Event, EventType, ListEventsResponse } from \"./openapi\";\n\n/**\n * Options for listing Events\n */\nexport interface ListEventsOptions {\n  /**\n   * The cursor to paginate through the results\n   */\n  cursor?: string;\n  /**\n   * The number of results to return\n   */\n  limit?: number;\n  /**\n   * The types of events to filter by\n   */\n  types?: EventType[];\n}\n\n/**\n * Client for managing Events\n */\nexport class EventsClient extends WebsetsBaseClient {\n  /**\n   * Initialize a new Events client\n   * @param client The Exa client instance\n   */\n  constructor(client: Exa) {\n    super(client);\n  }\n\n  /**\n   * List all Events\n   * @param options Optional filtering and pagination options\n   * @returns The list of Events\n   */\n  async list(options?: ListEventsOptions): Promise<ListEventsResponse> {\n    const params = {\n      cursor: options?.cursor,\n      limit: options?.limit,\n      types: options?.types,\n    };\n\n    return this.request<ListEventsResponse>(\n      \"/v0/events\",\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Get an Event by ID\n   * @param id The ID of the Event\n   * @returns The Event\n   */\n  async get(id: string): Promise<Event> {\n    return this.request<Event>(`/v0/events/${id}`, \"GET\");\n  }\n}\n", "/**\n * This file was auto-generated by openapi-typescript.\n * Do not make direct changes to the file.\n */\n\nexport interface paths {\n  \"/v0/events\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List all Events\n     * @description List all events that have occurred in the system.\n     *\n     *     You can paginate through the results using the `cursor` parameter.\n     */\n    get: operations[\"events-list\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/events/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * Get an Event\n     * @description Get a single Event by id.\n     *\n     *     You can subscribe to Events by creating a Webhook.\n     */\n    get: operations[\"events-get\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/imports\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List Imports\n     * @description Lists all imports for the Webset.\n     */\n    get: operations[\"imports-list\"];\n    put?: never;\n    /**\n     * Create an Import\n     * @description Creates a new import to upload your data into Websets. Imports can be used to:\n     *\n     *     - **Enrich**: Enhance your data with additional information using our AI-powered enrichment engine\n     *     - **Search**: Query your data using Websets' agentic search with natural language filters\n     *     - **Exclude**: Prevent duplicate or already known results from appearing in your searches\n     *\n     *     Once the import is created, you can upload your data to the returned `uploadUrl` until `uploadValidUntil` (by default 1 hour).\n     */\n    post: operations[\"imports-create\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/imports/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * Get Import\n     * @description Gets a specific import.\n     */\n    get: operations[\"imports-get\"];\n    put?: never;\n    post?: never;\n    /**\n     * Delete Import\n     * @description Deletes a import.\n     */\n    delete: operations[\"imports-delete\"];\n    options?: never;\n    head?: never;\n    /**\n     * Update Import\n     * @description Updates a import configuration.\n     */\n    patch: operations[\"imports-update\"];\n    trace?: never;\n  };\n  \"/v0/monitors\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List Monitors\n     * @description Lists all monitors for the Webset.\n     */\n    get: operations[\"monitors-list\"];\n    put?: never;\n    /**\n     * Create a Monitor\n     * @description Creates a new `Monitor` to continuously keep your Websets updated with fresh data.\n     *\n     *     Monitors automatically run on your defined schedule to ensure your Websets stay current without manual intervention:\n     *\n     *     - **Find new content**: Execute `search` operations to discover fresh items matching your criteria\n     *     - **Update existing content**: Run `refresh` operations to update items contents and enrichments\n     *     - **Automated scheduling**: Configure `cron` expressions and `timezone` for precise scheduling control\n     */\n    post: operations[\"monitors-create\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/monitors/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * Get Monitor\n     * @description Gets a specific monitor.\n     */\n    get: operations[\"monitors-get\"];\n    put?: never;\n    post?: never;\n    /**\n     * Delete Monitor\n     * @description Deletes a monitor.\n     */\n    delete: operations[\"monitors-delete\"];\n    options?: never;\n    head?: never;\n    /**\n     * Update Monitor\n     * @description Updates a monitor configuration.\n     */\n    patch: operations[\"monitors-update\"];\n    trace?: never;\n  };\n  \"/v0/monitors/{monitor}/runs\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List Monitor Runs\n     * @description Lists all runs for the Monitor.\n     */\n    get: operations[\"monitors-runs-list\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/monitors/{monitor}/runs/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * Get Monitor Run\n     * @description Gets a specific monitor run.\n     */\n    get: operations[\"monitors-runs-get\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/webhooks\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /** List webhooks */\n    get: operations[\"webhooks-list\"];\n    put?: never;\n    /** Create a Webhook */\n    post: operations[\"webhooks-create\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/webhooks/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /** Get a Webhook */\n    get: operations[\"webhooks-get\"];\n    put?: never;\n    post?: never;\n    /** Delete a Webhook */\n    delete: operations[\"webhooks-delete\"];\n    options?: never;\n    head?: never;\n    /** Update a Webhook */\n    patch: operations[\"webhooks-update\"];\n    trace?: never;\n  };\n  \"/v0/webhooks/{id}/attempts\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List webhook attempts\n     * @description List all attempts made by a Webhook ordered in descending order.\n     */\n    get: operations[\"webhooks-attempts-list\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List all Websets\n     * @description Returns a list of Websets.\n     *\n     *     You can paginate through the results using the `cursor` parameter.\n     */\n    get: operations[\"websets-list\"];\n    put?: never;\n    /**\n     * Create a Webset\n     * @description Creates a new Webset with optional search, import, and enrichment configurations. The Webset will automatically begin processing once created.\n     *\n     *     You can specify an `externalId` to reference the Webset with your own identifiers for easier integration.\n     */\n    post: operations[\"websets-create\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /** Get a Webset */\n    get: operations[\"websets-get\"];\n    put?: never;\n    /** Update a Webset */\n    post: operations[\"websets-update\"];\n    /**\n     * Delete a Webset\n     * @description Deletes a Webset.\n     *\n     *     Once deleted, the Webset and all its Items will no longer be available.\n     */\n    delete: operations[\"websets-delete\"];\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{id}/cancel\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    get?: never;\n    put?: never;\n    /**\n     * Cancel a running Webset\n     * @description Cancels all operations being performed on a Webset.\n     *\n     *     Any enrichment or search will be stopped and the Webset will be marked as `idle`.\n     */\n    post: operations[\"websets-cancel\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/enrichments\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    get?: never;\n    put?: never;\n    /**\n     * Create an Enrichment\n     * @description Create an Enrichment for a Webset.\n     */\n    post: operations[\"websets-enrichments-create\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/enrichments/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /** Get an Enrichment */\n    get: operations[\"websets-enrichments-get\"];\n    put?: never;\n    post?: never;\n    /**\n     * Delete an Enrichment\n     * @description When deleting an Enrichment, any running enrichments will be canceled and all existing `enrichment_result` generated by this Enrichment will no longer be available.\n     */\n    delete: operations[\"websets-enrichments-delete\"];\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/enrichments/{id}/cancel\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    get?: never;\n    put?: never;\n    /**\n     * Cancel a running Enrichment\n     * @description All running enrichments will be canceled. You can not resume an Enrichment after it has been canceled.\n     */\n    post: operations[\"websets-enrichments-cancel\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/items\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * List all Items for a Webset\n     * @description Returns a list of Webset Items.\n     *\n     *     You can paginate through the Items using the `cursor` parameter.\n     */\n    get: operations[\"websets-items-list\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/items/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * Get an Item\n     * @description Returns a Webset Item.\n     */\n    get: operations[\"websets-items-get\"];\n    put?: never;\n    post?: never;\n    /**\n     * Delete an Item\n     * @description Deletes an Item from the Webset.\n     *\n     *     This will cancel any enrichment process for it.\n     */\n    delete: operations[\"websets-items-delete\"];\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/searches\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    get?: never;\n    put?: never;\n    /**\n     * Create a Search\n     * @description Creates a new Search for the Webset.\n     *\n     *     The default behavior is to reuse the previous Search results and evaluate them against the new criteria.\n     */\n    post: operations[\"websets-searches-create\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/searches/{id}\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    /**\n     * Get a Search\n     * @description Gets a Search by id\n     */\n    get: operations[\"websets-searches-get\"];\n    put?: never;\n    post?: never;\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n  \"/v0/websets/{webset}/searches/{id}/cancel\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    get?: never;\n    put?: never;\n    /**\n     * Cancel a running Search\n     * @description Cancels a currently running Search.\n     *\n     *     You can cancel all searches at once by using the `websets/:webset/cancel` endpoint.\n     */\n    post: operations[\"websets-searches-cancel\"];\n    delete?: never;\n    options?: never;\n    head?: never;\n    patch?: never;\n    trace?: never;\n  };\n}\nexport type webhooks = Record<string, never>;\nexport interface components {\n  schemas: {\n    /** Article */\n    ArticleEntity: {\n      /**\n       * @default article\n       * @constant\n       */\n      type: \"article\";\n    };\n    /** Company */\n    CompanyEntity: {\n      /**\n       * @default company\n       * @constant\n       */\n      type: \"company\";\n    };\n    CreateCriterionParameters: {\n      /** @description The description of the criterion */\n      description: string;\n    };\n    CreateEnrichmentParameters: {\n      /** @description Provide a description of the enrichment task you want to perform to each Webset Item. */\n      description: string;\n      /**\n       * @description Format of the enrichment response.\n       *\n       *     We automatically select the best format based on the description. If you want to explicitly specify the format, you can do so here.\n       * @enum {string}\n       */\n      format?: CreateEnrichmentParametersFormat;\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      };\n      /** @description When the format is options, the different options for the enrichment agent to choose from. */\n      options?: {\n        /** @description The label of the option */\n        label: string;\n      }[];\n    };\n    CreateImportParameters: {\n      /** @description The number of records to import */\n      count: number;\n      /** @description When format is `csv`, these are the specific import parameters. */\n      csv?: {\n        /** @description Column containing the key identifier for the entity (e.g. URL, Name, etc.). If not provided, we will try to infer it from the file. */\n        identifier?: number;\n      };\n      /** @description What type of entity the import contains (e.g. People, Companies, etc.), and thus should be attempted to be resolved as. */\n      entity:\n        | components[\"schemas\"][\"CompanyEntity\"]\n        | components[\"schemas\"][\"PersonEntity\"]\n        | components[\"schemas\"][\"ArticleEntity\"]\n        | components[\"schemas\"][\"ResearchPaperEntity\"]\n        | components[\"schemas\"][\"CustomEntity\"];\n      /**\n       * @description When the import is in CSV format, we expect a column containing the key identifier for the entity - for now URL. If not provided, import will fail to be processed.\n       * @enum {string}\n       */\n      format: CreateImportParametersFormat;\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      };\n      /** @description The size of the file in megabytes. Maximum size is 50 MB. */\n      size: number;\n      /** @description The title of the import */\n      title?: string;\n    };\n    /** @description The response to a successful import. Includes the upload URL and the upload valid until date. */\n    CreateImportResponse: {\n      /** @description The number of entities in the import */\n      count: number;\n      /**\n       * Format: date-time\n       * @description When the import was created\n       */\n      createdAt: string;\n      /** @description The type of entity the import contains. */\n      entity: components[\"schemas\"][\"Entity\"];\n      /**\n       * Format: date-time\n       * @description When the import failed\n       */\n      failedAt: string | null;\n      /** @description A human readable message of the import failure */\n      failedMessage: string | null;\n      /**\n       * @description The reason the import failed\n       * @enum {string|null}\n       */\n      failedReason: CreateImportResponseFailedReason;\n      /**\n       * @description The format of the import.\n       * @enum {string}\n       */\n      format: CreateImportResponseFormat;\n      /** @description The unique identifier for the Import */\n      id: string;\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata: {\n        [key: string]: string;\n      };\n      /**\n       * @description The type of object\n       * @enum {string}\n       */\n      object: CreateImportResponseObject;\n      /**\n       * @description The status of the Import\n       * @enum {string}\n       */\n      status: CreateImportResponseStatus;\n      /** @description The title of the import */\n      title: string;\n      /**\n       * Format: date-time\n       * @description When the import was last updated\n       */\n      updatedAt: string;\n      /** @description The URL to upload the file to */\n      uploadUrl: string;\n      /** @description The date and time until the upload URL is valid. The upload URL will be valid for 1 hour. */\n      uploadValidUntil: string;\n    };\n    CreateMonitorParameters: {\n      /** @description Behavior to perform when monitor runs */\n      behavior: {\n        /** @description Specify the search parameters for the Monitor.\n         *\n         *     By default, the search parameters (query, entity and criteria) from the last search are used when no parameters are provided. */\n        config: {\n          /**\n           * @description The behaviour of the Search when it is added to a Webset.\n           * @default append\n           * @enum {string}\n           */\n          behavior: WebsetSearchBehavior;\n          /** @description The maximum number of results to find */\n          count: number;\n          /** @description The criteria to search for. By default, the criteria from the last search is used. */\n          criteria?: {\n            description: string;\n          }[];\n          /**\n           * Entity\n           * @description The entity to search for. By default, the entity from the last search/import is used.\n           */\n          entity?: components[\"schemas\"][\"Entity\"];\n          /** @description The query to search for. By default, the query from the last search is used. */\n          query?: string;\n        };\n        /**\n         * @default search\n         * @constant\n         */\n        type: \"search\";\n      };\n      /** @description How often the monitor will run */\n      cadence: {\n        /** @description Cron expression for monitor cadence (must be a valid Unix cron with 5 fields). The schedule must trigger at most once per day. */\n        cron: string;\n        /**\n         * @description IANA timezone (e.g., \"America/New_York\")\n         * @default Etc/UTC\n         */\n        timezone: string;\n      };\n      metadata?: {\n        [key: string]: string;\n      };\n      /** @description The id of the Webset */\n      websetId: string;\n    };\n    CreateWebhookParameters: {\n      /** @description The events to trigger the webhook */\n      events: EventType[];\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      };\n      /**\n       * Format: uri\n       * @description The URL to send the webhook to\n       */\n      url: string;\n    };\n    CreateWebsetParameters: {\n      /** @description Add enrichments to extract additional data from found items.\n       *\n       *     Enrichments automatically search for and extract specific information (like contact details, funding data, employee counts, etc.) from each item added to your Webset. */\n      enrichments?: components[\"schemas\"][\"CreateEnrichmentParameters\"][];\n      /** @description The external identifier for the webset.\n       *\n       *     You can use this to reference the Webset by your own internal identifiers. */\n      externalId?: string;\n      /** @description Import data from existing Websets and Imports into this Webset. */\n      import?: {\n        /** @description The ID of the source to search. */\n        id: string;\n        /** @enum {string} */\n        source: CreateWebsetParametersImportSource;\n      }[];\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      };\n      /** @description Create initial search for the Webset. */\n      search?: {\n        /**\n         * @description Number of Items the Webset will attempt to find.\n         *\n         *     The actual number of Items found may be less than this number depending on the search complexity.\n         * @default 10\n         */\n        count: number;\n        /** @description Criteria every item is evaluated against.\n         *\n         *     It's not required to provide your own criteria, we automatically detect the criteria from all the information provided in the query. Only use this when you need more fine control. */\n        criteria?: components[\"schemas\"][\"CreateCriterionParameters\"][];\n        /** @description Entity the Webset will return results for.\n         *\n         *     It is not required to provide it, we automatically detect the entity from all the information provided in the query. Only use this when you need more fine control. */\n        entity?: components[\"schemas\"][\"Entity\"];\n        /** @description Sources (existing imports or websets) to exclude from search results. Any results found within these sources will be omitted to prevent finding them during search. */\n        exclude?: {\n          /** @description The ID of the source to exclude. */\n          id: string;\n          /** @enum {string} */\n          source: CreateWebsetParametersSearchExcludeSource;\n        }[];\n        /** @description Natural language search query describing what you are looking for.\n         *\n         *     Be specific and descriptive about your requirements, characteristics, and any constraints that help narrow down the results.\n         *\n         *     Any URLs provided will be crawled and used as additional context for the search. */\n        query: string;\n        /** @description Whether to compute recall metrics for the search */\n        recall?: boolean;\n      };\n    };\n    CreateWebsetSearchParameters: {\n      /**\n       * @description How this search interacts with existing items in the Webset:\n       *\n       *     - **override**: Replace existing items and evaluate all items against new criteria\n       *     - **append**: Add new items to existing ones, keeping items that match the new criteria\n       * @default override\n       */\n      behavior: components[\"schemas\"][\"WebsetSearchBehavior\"];\n      /** @description Number of Items the Search will attempt to find.\n       *\n       *     The actual number of Items found may be less than this number depending on the query complexity. */\n      count: number;\n      /** @description Criteria every item is evaluated against.\n       *\n       *     It's not required to provide your own criteria, we automatically detect the criteria from all the information provided in the query. Only use this when you need more fine control. */\n      criteria?: components[\"schemas\"][\"CreateCriterionParameters\"][];\n      /** @description Entity the search will return results for.\n       *\n       *     It is not required to provide it, we automatically detect the entity from all the information provided in the query. Only use this when you need more fine control. */\n      entity?: components[\"schemas\"][\"Entity\"];\n      /** @description Sources (existing imports or websets) to exclude from search results. Any results found within these sources will be omitted to prevent finding them during search. */\n      exclude?: {\n        /** @description The ID of the source to exclude. */\n        id: string;\n        /** @enum {string} */\n        source: CreateWebsetSearchParametersExcludeSource;\n      }[];\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      };\n      /** @description Natural language search query describing what you are looking for.\n       *\n       *     Be specific and descriptive about your requirements, characteristics, and any constraints that help narrow down the results.\n       *\n       *     Any URLs provided will be crawled and used as additional context for the search. */\n      query: string;\n      /** @description Whether to compute recall metrics for the search */\n      recall?: boolean;\n    };\n    /** Custom */\n    CustomEntity: {\n      description: string;\n      /**\n       * @default custom\n       * @constant\n       */\n      type: \"custom\";\n    };\n    EnrichmentResult: {\n      /** @description The id of the Enrichment that generated the result */\n      enrichmentId: string;\n      format: components[\"schemas\"][\"WebsetEnrichmentFormat\"];\n      /**\n       * @default enrichment_result\n       * @constant\n       */\n      object: \"enrichment_result\";\n      /** @description The reasoning for the result when an Agent is used. */\n      reasoning: string | null;\n      /** @description The references used to generate the result. */\n      references: {\n        /** @description The relevant snippet of the reference content */\n        snippet: string | null;\n        /** @description The title of the reference */\n        title: string | null;\n        /** @description The URL of the reference */\n        url: string;\n      }[];\n      /** @description The result of the enrichment. */\n      result: string[] | null;\n      /**\n       * @description The status of the enrichment result.\n       * @enum {string}\n       */\n      status: EnrichmentResultStatus;\n    };\n    Entity:\n      | components[\"schemas\"][\"CompanyEntity\"]\n      | components[\"schemas\"][\"PersonEntity\"]\n      | components[\"schemas\"][\"ArticleEntity\"]\n      | components[\"schemas\"][\"ResearchPaperEntity\"]\n      | components[\"schemas\"][\"CustomEntity\"];\n    /** Event */\n    Event:\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"Webset\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.created\n           * @constant\n           */\n          type: \"webset.created\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"Webset\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.deleted\n           * @constant\n           */\n          type: \"webset.deleted\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"Webset\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.idle\n           * @constant\n           */\n          type: \"webset.idle\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"Webset\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.paused\n           * @constant\n           */\n          type: \"webset.paused\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"WebsetItem\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.item.created\n           * @constant\n           */\n          type: \"webset.item.created\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"WebsetItem\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.item.enriched\n           * @constant\n           */\n          type: \"webset.item.enriched\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"WebsetSearch\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.search.created\n           * @constant\n           */\n          type: \"webset.search.created\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"WebsetSearch\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.search.updated\n           * @constant\n           */\n          type: \"webset.search.updated\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"WebsetSearch\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.search.canceled\n           * @constant\n           */\n          type: \"webset.search.canceled\";\n        }\n      | {\n          /**\n           * Format: date-time\n           * @description The date and time the event was created\n           */\n          createdAt: string;\n          data: components[\"schemas\"][\"WebsetSearch\"];\n          /** @description The unique identifier for the event */\n          id: string;\n          /**\n           * @default event\n           * @constant\n           */\n          object: \"event\";\n          /**\n           * @default webset.search.completed\n           * @constant\n           */\n          type: \"webset.search.completed\";\n        };\n    /** @enum {string} */\n    EventType: EventType;\n    GetWebsetResponse: components[\"schemas\"][\"Webset\"] & {\n      /** @description When expand query parameter contains `items`, this will contain the items in the webset */\n      items?: components[\"schemas\"][\"WebsetItem\"][];\n    };\n    Import: {\n      /** @description The number of entities in the import */\n      count: number;\n      /**\n       * Format: date-time\n       * @description When the import was created\n       */\n      createdAt: string;\n      /** @description The type of entity the import contains. */\n      entity: components[\"schemas\"][\"Entity\"];\n      /**\n       * Format: date-time\n       * @description When the import failed\n       */\n      failedAt: string | null;\n      /** @description A human readable message of the import failure */\n      failedMessage: string | null;\n      /**\n       * @description The reason the import failed\n       * @enum {string|null}\n       */\n      failedReason: ImportFailedReason;\n      /**\n       * @description The format of the import.\n       * @enum {string}\n       */\n      format: ImportFormat;\n      /** @description The unique identifier for the Import */\n      id: string;\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata: {\n        [key: string]: string;\n      };\n      /**\n       * @description The type of object\n       * @enum {string}\n       */\n      object: ImportObject;\n      /**\n       * @description The status of the Import\n       * @enum {string}\n       */\n      status: ImportStatus;\n      /** @description The title of the import */\n      title: string;\n      /**\n       * Format: date-time\n       * @description When the import was last updated\n       */\n      updatedAt: string;\n    };\n    ListEventsResponse: {\n      /** @description The list of events */\n      data: components[\"schemas\"][\"Event\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    ListImportsResponse: {\n      /** @description The list of imports */\n      data: components[\"schemas\"][\"Import\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    ListMonitorRunsResponse: {\n      /** @description The list of monitor runs */\n      data: components[\"schemas\"][\"MonitorRun\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    ListMonitorsResponse: {\n      /** @description The list of monitors */\n      data: components[\"schemas\"][\"Monitor\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    ListWebhookAttemptsResponse: {\n      /** @description The list of webhook attempts */\n      data: components[\"schemas\"][\"WebhookAttempt\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    ListWebhooksResponse: {\n      /** @description The list of webhooks */\n      data: components[\"schemas\"][\"Webhook\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    ListWebsetItemResponse: {\n      /** @description The list of webset items */\n      data: components[\"schemas\"][\"WebsetItem\"][];\n      /** @description Whether there are more Items to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of Items */\n      nextCursor: string | null;\n    };\n    ListWebsetsResponse: {\n      /** @description The list of websets */\n      data: components[\"schemas\"][\"Webset\"][];\n      /** @description Whether there are more results to paginate through */\n      hasMore: boolean;\n      /** @description The cursor to paginate through the next set of results */\n      nextCursor: string | null;\n    };\n    Monitor: {\n      /** @description Behavior to perform when monitor runs */\n      behavior: {\n        /** @description Specify the search parameters for the Monitor.\n         *\n         *     By default, the search parameters (query, entity and criteria) from the last search are used when no parameters are provided. */\n        config: {\n          /**\n           * @description The behaviour of the Search when it is added to a Webset.\n           * @default append\n           * @enum {string}\n           */\n          behavior: MonitorBehaviorConfigBehavior;\n          /** @description The maximum number of results to find */\n          count: number;\n          /** @description The criteria to search for. By default, the criteria from the last search is used. */\n          criteria?: {\n            description: string;\n          }[];\n          /**\n           * Entity\n           * @description The entity to search for. By default, the entity from the last search/import is used.\n           */\n          entity?: components[\"schemas\"][\"Entity\"];\n          /** @description The query to search for. By default, the query from the last search is used. */\n          query?: string;\n        };\n        /**\n         * @default search\n         * @constant\n         */\n        type: \"search\";\n      };\n      /** @description How often the monitor will run */\n      cadence: {\n        /** @description Cron expression for monitor cadence (must be a valid Unix cron with 5 fields). The schedule must trigger at most once per day. */\n        cron: string;\n        /**\n         * @description IANA timezone (e.g., \"America/New_York\")\n         * @default Etc/UTC\n         */\n        timezone: string;\n      };\n      /**\n       * Format: date-time\n       * @description When the monitor was created\n       */\n      createdAt: string;\n      /** @description The unique identifier for the Monitor */\n      id: string;\n      /**\n       * MonitorRun\n       * @description The last run of the monitor\n       */\n      lastRun: components[\"schemas\"][\"MonitorRun\"];\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata: {\n        [key: string]: string;\n      };\n      /**\n       * Format: date-time\n       * @description Date and time when the next run will occur in\n       */\n      nextRunAt: string | null;\n      /**\n       * @description The type of object\n       * @enum {string}\n       */\n      object: MonitorObject;\n      /**\n       * @description The status of the Monitor\n       * @enum {string}\n       */\n      status: MonitorStatus;\n      /**\n       * Format: date-time\n       * @description When the monitor was last updated\n       */\n      updatedAt: string;\n      /** @description The id of the Webset the Monitor belongs to */\n      websetId: string;\n    };\n    MonitorBehavior: {\n      /** @description Specify the search parameters for the Monitor.\n       *\n       *     By default, the search parameters (query, entity and criteria) from the last search are used when no parameters are provided. */\n      config: {\n        /**\n         * @description The behaviour of the Search when it is added to a Webset.\n         * @default append\n         * @enum {string}\n         */\n        behavior: MonitorBehaviorConfigBehavior;\n        /** @description The maximum number of results to find */\n        count: number;\n        /** @description The criteria to search for. By default, the criteria from the last search is used. */\n        criteria?: {\n          description: string;\n        }[];\n        /**\n         * Entity\n         * @description The entity to search for. By default, the entity from the last search/import is used.\n         */\n        entity?: components[\"schemas\"][\"Entity\"];\n        /** @description The query to search for. By default, the query from the last search is used. */\n        query?: string;\n      };\n      /**\n       * @default search\n       * @constant\n       */\n      type: \"search\";\n    };\n    MonitorCadence: {\n      /** @description Cron expression for monitor cadence (must be a valid Unix cron with 5 fields). The schedule must trigger at most once per day. */\n      cron: string;\n      /**\n       * @description IANA timezone (e.g., \"America/New_York\")\n       * @default Etc/UTC\n       */\n      timezone: string;\n    };\n    MonitorRun: {\n      /**\n       * Format: date-time\n       * @description When the run was canceled\n       */\n      canceledAt: string | null;\n      /**\n       * Format: date-time\n       * @description When the run completed\n       */\n      completedAt: string | null;\n      /**\n       * Format: date-time\n       * @description When the run was created\n       */\n      createdAt: string;\n      /**\n       * Format: date-time\n       * @description When the run failed\n       */\n      failedAt: string | null;\n      /** @description The unique identifier for the Monitor Run */\n      id: string;\n      /** @description The monitor that the run is associated with */\n      monitorId: string;\n      /**\n       * @description The type of object\n       * @enum {string}\n       */\n      object: MonitorRunObject;\n      /**\n       * @description The status of the Monitor Run\n       * @enum {string}\n       */\n      status: MonitorRunStatus;\n      /**\n       * @description The type of the Monitor Run\n       * @enum {string}\n       */\n      type: MonitorRunType;\n      /**\n       * Format: date-time\n       * @description When the run was last updated\n       */\n      updatedAt: string;\n    };\n    /** Person */\n    PersonEntity: {\n      /**\n       * @default person\n       * @constant\n       */\n      type: \"person\";\n    };\n    /** Research Paper */\n    ResearchPaperEntity: {\n      /**\n       * @default research_paper\n       * @constant\n       */\n      type: \"research_paper\";\n    };\n    UpdateImport: {\n      metadata?: {\n        [key: string]: string;\n      };\n      title?: string;\n    };\n    UpdateMonitor: {\n      behavior?: components[\"schemas\"][\"MonitorBehavior\"];\n      cadence?: components[\"schemas\"][\"MonitorCadence\"];\n      metadata?: {\n        [key: string]: string;\n      };\n      /**\n       * @description The status of the monitor.\n       * @enum {string}\n       */\n      status?: UpdateMonitorStatus;\n    };\n    UpdateWebhookParameters: {\n      /** @description The events to trigger the webhook */\n      events?: EventType[];\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      };\n      /**\n       * Format: uri\n       * @description The URL to send the webhook to\n       */\n      url?: string;\n    };\n    UpdateWebsetRequest: {\n      /** @description Set of key-value pairs you want to associate with this object. */\n      metadata?: {\n        [key: string]: string;\n      } | null;\n    };\n    Webhook: {\n      /**\n       * Format: date-time\n       * @description The date and time the webhook was created\n       */\n      createdAt: string;\n      /** @description The events to trigger the webhook */\n      events: EventType[];\n      /** @description The unique identifier for the webhook */\n      id: string;\n      /**\n       * @description The metadata of the webhook\n       * @default {}\n       */\n      metadata: {\n        [key: string]: string;\n      };\n      /**\n       * @default webhook\n       * @constant\n       */\n      object: \"webhook\";\n      /** @description The secret to verify the webhook signature. Only returned on Webhook creation. */\n      secret: string | null;\n      /**\n       * WebhookStatus\n       * @description The status of the webhook\n       * @enum {string}\n       */\n      status: WebhookStatus;\n      /**\n       * Format: date-time\n       * @description The date and time the webhook was last updated\n       */\n      updatedAt: string;\n      /**\n       * Format: uri\n       * @description The URL to send the webhook to\n       */\n      url: string;\n    };\n    WebhookAttempt: {\n      /** @description The attempt number of the webhook */\n      attempt: number;\n      /**\n       * Format: date-time\n       * @description The date and time the webhook attempt was made\n       */\n      attemptedAt: string;\n      /** @description The unique identifier for the event */\n      eventId: string;\n      /**\n       * @description The type of event\n       * @enum {string}\n       */\n      eventType: WebhookAttemptEventType;\n      /** @description The unique identifier for the webhook attempt */\n      id: string;\n      /**\n       * @default webhook_attempt\n       * @constant\n       */\n      object: \"webhook_attempt\";\n      /** @description The body of the response */\n      responseBody: string | null;\n      /** @description The headers of the response */\n      responseHeaders: {\n        [key: string]: string;\n      };\n      /** @description The status code of the response */\n      responseStatusCode: number;\n      /** @description Whether the attempt was successful */\n      successful: boolean;\n      /** @description The URL that was used during the attempt */\n      url: string;\n      /** @description The unique identifier for the webhook */\n      webhookId: string;\n    };\n    Webset: {\n      /**\n       * Format: date-time\n       * @description The date and time the webset was created\n       */\n      createdAt: string;\n      /** @description The Enrichments to apply to the Webset Items. */\n      enrichments: components[\"schemas\"][\"WebsetEnrichment\"][];\n      /** @description The external identifier for the webset */\n      externalId: string | null;\n      /** @description The unique identifier for the webset */\n      id: string;\n      /** @description Imports that have been performed on the webset. */\n      imports: components[\"schemas\"][\"Import\"][];\n      /**\n       * @description Set of key-value pairs you want to associate with this object.\n       * @default {}\n       */\n      metadata: {\n        [key: string]: string;\n      };\n      /** @description The Monitors for the Webset. */\n      monitors: components[\"schemas\"][\"Monitor\"][];\n      /**\n       * @default webset\n       * @constant\n       */\n      object: \"webset\";\n      /** @description The searches that have been performed on the webset. */\n      searches: components[\"schemas\"][\"WebsetSearch\"][];\n      /**\n       * WebsetStatus\n       * @description The status of the webset\n       * @enum {string}\n       */\n      status: WebsetStatus;\n      /** @description The Streams for the Webset. */\n      streams: unknown[];\n      /**\n       * Format: date-time\n       * @description The date and time the webset was updated\n       */\n      updatedAt: string;\n    };\n    WebsetEnrichment: {\n      /**\n       * Format: date-time\n       * @description The date and time the enrichment was created\n       */\n      createdAt: string;\n      /** @description The description of the enrichment task provided during the creation of the enrichment. */\n      description: string;\n      /** @description The format of the enrichment response. */\n      format: components[\"schemas\"][\"WebsetEnrichmentFormat\"];\n      /** @description The unique identifier for the enrichment */\n      id: string;\n      /** @description The instructions for the enrichment Agent.\n       *\n       *     This will be automatically generated based on the description and format. */\n      instructions: string | null;\n      /**\n       * @description The metadata of the enrichment\n       * @default {}\n       */\n      metadata: {\n        [key: string]: string;\n      };\n      /**\n       * @default webset_enrichment\n       * @constant\n       */\n      object: \"webset_enrichment\";\n      /**\n       * WebsetEnrichmentOptions\n       * @description When the format is options, the different options for the enrichment agent to choose from.\n       */\n      options:\n        | {\n            /** @description The label of the option */\n            label: string;\n          }[]\n        | null;\n      /**\n       * WebsetEnrichmentStatus\n       * @description The status of the enrichment\n       * @enum {string}\n       */\n      status: WebsetEnrichmentStatus;\n      /** @description The title of the enrichment.\n       *\n       *     This will be automatically generated based on the description and format. */\n      title: string | null;\n      /**\n       * Format: date-time\n       * @description The date and time the enrichment was updated\n       */\n      updatedAt: string;\n      /** @description The unique identifier for the Webset this enrichment belongs to. */\n      websetId: string;\n    };\n    /** @enum {string} */\n    WebsetEnrichmentFormat: WebsetEnrichmentFormat;\n    WebsetItem: {\n      /**\n       * Format: date-time\n       * @description The date and time the item was created\n       */\n      createdAt: string;\n      /** @description The enrichments results of the Webset item */\n      enrichments: components[\"schemas\"][\"EnrichmentResult\"][] | null;\n      /** @description The criteria evaluations of the item */\n      evaluations: components[\"schemas\"][\"WebsetItemEvaluation\"][];\n      /** @description The unique identifier for the Webset Item */\n      id: string;\n      /**\n       * @default webset_item\n       * @constant\n       */\n      object: \"webset_item\";\n      /** @description The properties of the Item */\n      properties:\n        | components[\"schemas\"][\"WebsetItemPersonProperties\"]\n        | components[\"schemas\"][\"WebsetItemCompanyProperties\"]\n        | components[\"schemas\"][\"WebsetItemArticleProperties\"]\n        | components[\"schemas\"][\"WebsetItemResearchPaperProperties\"]\n        | components[\"schemas\"][\"WebsetItemCustomProperties\"];\n      /**\n       * @description The source of the Item\n       * @enum {string}\n       */\n      source: WebsetItemSource;\n      /** @description The unique identifier for the source */\n      sourceId: string;\n      /**\n       * Format: date-time\n       * @description The date and time the item was last updated\n       */\n      updatedAt: string;\n      /** @description The unique identifier for the Webset this Item belongs to. */\n      websetId: string;\n    };\n    WebsetItemArticleProperties: {\n      /** WebsetItemArticlePropertiesFields */\n      article: {\n        /** @description The author(s) of the article */\n        author: string | null;\n        /** @description The date and time the article was published */\n        publishedAt: string | null;\n        /** @description The title of the article */\n        title: string | null;\n      };\n      /** @description The text content for the article */\n      content: string | null;\n      /** @description Short description of the relevance of the article */\n      description: string;\n      /**\n       * @default article\n       * @constant\n       */\n      type: \"article\";\n      /**\n       * Format: uri\n       * @description The URL of the article\n       */\n      url: string;\n    };\n    WebsetItemCompanyProperties: {\n      /** WebsetItemCompanyPropertiesFields */\n      company: {\n        /** @description A short description of the company */\n        about: string | null;\n        /** @description The number of employees of the company */\n        employees: number | null;\n        /** @description The industry of the company */\n        industry: string | null;\n        /** @description The main location of the company */\n        location: string | null;\n        /**\n         * Format: uri\n         * @description The logo URL of the company\n         */\n        logoUrl: string | null;\n        /** @description The name of the company */\n        name: string;\n      };\n      /** @description The text content of the company website */\n      content: string | null;\n      /** @description Short description of the relevance of the company */\n      description: string;\n      /**\n       * @default company\n       * @constant\n       */\n      type: \"company\";\n      /**\n       * Format: uri\n       * @description The URL of the company website\n       */\n      url: string;\n    };\n    WebsetItemCustomProperties: {\n      /** @description The text content of the Item */\n      content: string | null;\n      /** WebsetItemCustomPropertiesFields */\n      custom: {\n        /** @description The author(s) of the website */\n        author: string | null;\n        /** @description The date and time the website was published */\n        publishedAt: string | null;\n        /** @description The title of the website */\n        title: string | null;\n      };\n      /** @description Short description of the Item */\n      description: string;\n      /**\n       * @default custom\n       * @constant\n       */\n      type: \"custom\";\n      /**\n       * Format: uri\n       * @description The URL of the Item\n       */\n      url: string;\n    };\n    WebsetItemEvaluation: {\n      /** @description The description of the criterion */\n      criterion: string;\n      /** @description The reasoning for the result of the evaluation */\n      reasoning: string;\n      /**\n       * @description The references used to generate the result.\n       * @default []\n       */\n      references: {\n        /** @description The relevant snippet of the reference content */\n        snippet: string | null;\n        /** @description The title of the reference */\n        title: string | null;\n        /** @description The URL of the reference */\n        url: string;\n      }[];\n      /**\n       * @description The satisfaction of the criterion\n       * @enum {string}\n       */\n      satisfied: WebsetItemEvaluationSatisfied;\n    };\n    WebsetItemPersonProperties: {\n      /** @description Short description of the relevance of the person */\n      description: string;\n      /** WebsetItemPersonPropertiesFields */\n      person: {\n        /** WebsetItemPersonCompanyPropertiesFields */\n        company: {\n          /** @description The location the person is working at the company */\n          location: string | null;\n          /** @description The name of the company */\n          name: string;\n        } | null;\n        /** @description The location of the person */\n        location: string | null;\n        /** @description The name of the person */\n        name: string;\n        /**\n         * Format: uri\n         * @description The image URL of the person\n         */\n        pictureUrl: string | null;\n        /** @description The current work position of the person */\n        position: string | null;\n      };\n      /**\n       * @default person\n       * @constant\n       */\n      type: \"person\";\n      /**\n       * Format: uri\n       * @description The URL of the person profile\n       */\n      url: string;\n    };\n    WebsetItemResearchPaperProperties: {\n      /** @description The text content of the research paper */\n      content: string | null;\n      /** @description Short description of the relevance of the research paper */\n      description: string;\n      /** WebsetItemResearchPaperPropertiesFields */\n      researchPaper: {\n        /** @description The author(s) of the research paper */\n        author: string | null;\n        /** @description The date and time the research paper was published */\n        publishedAt: string | null;\n        /** @description The title of the research paper */\n        title: string | null;\n      };\n      /**\n       * @default research_paper\n       * @constant\n       */\n      type: \"research_paper\";\n      /**\n       * Format: uri\n       * @description The URL of the research paper\n       */\n      url: string;\n    };\n    WebsetSearch: {\n      /**\n       * @description The behavior of the search when it is added to a Webset.\n       *\n       *     - `override`: the search will replace the existing Items found in the Webset and evaluate them against the new criteria. Any Items that don't match the new criteria will be discarded.\n       *     - `append`: the search will add the new Items found to the existing Webset. Any Items that don't match the new criteria will be discarded.\n       * @default override\n       */\n      behavior: components[\"schemas\"][\"WebsetSearchBehavior\"];\n      /**\n       * Format: date-time\n       * @description The date and time the search was canceled\n       */\n      canceledAt: string | null;\n      /** @description The reason the search was canceled */\n      canceledReason: components[\"schemas\"][\"WebsetSearchCanceledReason\"];\n      /** @description The number of results the search will attempt to find. The actual number of results may be less than this number depending on the search complexity. */\n      count: number;\n      /**\n       * Format: date-time\n       * @description The date and time the search was created\n       */\n      createdAt: string;\n      /** @description The criteria the search will use to evaluate the results. If not provided, we will automatically generate them for you. */\n      criteria: {\n        /** @description The description of the criterion */\n        description: string;\n        /** @description Value between 0 and 100 representing the percentage of results that meet the criterion. */\n        successRate: number;\n      }[];\n      /** @description The entity the search will return results for.\n       *\n       *     When no entity is provided during creation, we will automatically select the best entity based on the query. */\n      entity: components[\"schemas\"][\"Entity\"];\n      /** @description Sources (existing imports or websets) used to omit certain results to be found during the search. */\n      exclude: {\n        id: string;\n        /** @enum {string} */\n        source: WebsetSearchExcludeSource;\n      }[];\n      /** @description The unique identifier for the search */\n      id: string;\n      /**\n       * @description Set of key-value pairs you want to associate with this object.\n       * @default {}\n       */\n      metadata: {\n        [key: string]: string;\n      };\n      /**\n       * @default webset_search\n       * @constant\n       */\n      object: \"webset_search\";\n      /** @description The progress of the search */\n      progress: {\n        /** @description The number of results analyzed so far */\n        analyzed: number;\n        /** @description The completion percentage of the search */\n        completion: number;\n        /** @description The number of results found so far */\n        found: number;\n        /** @description The estimated time remaining in seconds, null if unknown */\n        timeLeft: number | null;\n      };\n      /** @description The query used to create the search. */\n      query: string;\n      /** @description Recall metrics for the search, null if not yet computed or requested. */\n      recall: {\n        expected: {\n          bounds: {\n            /** @description The maximum estimated total number of potential matches */\n            max: number;\n            /** @description The minimum estimated total number of potential matches */\n            min: number;\n          };\n          /**\n           * @description The confidence in the estimate\n           * @enum {string}\n           */\n          confidence: WebsetSearchRecallExpectedConfidence;\n          /** @description The estimated total number of potential matches */\n          total: number;\n        };\n        /** @description The reasoning for the estimate */\n        reasoning: string;\n      } | null;\n      /**\n       * WebsetSearchStatus\n       * @description The status of the search\n       * @enum {string}\n       */\n      status: WebsetSearchStatus;\n      /**\n       * Format: date-time\n       * @description The date and time the search was updated\n       */\n      updatedAt: string;\n    };\n    /** @enum {string} */\n    WebsetSearchBehavior: WebsetSearchBehavior;\n    /** @enum {string} */\n    WebsetSearchCanceledReason: WebsetSearchCanceledReason;\n  };\n  responses: never;\n  parameters: never;\n  requestBodies: never;\n  headers: never;\n  pathItems: never;\n}\nexport type ArticleEntity = components[\"schemas\"][\"ArticleEntity\"];\nexport type CompanyEntity = components[\"schemas\"][\"CompanyEntity\"];\nexport type CreateCriterionParameters =\n  components[\"schemas\"][\"CreateCriterionParameters\"];\nexport type CreateEnrichmentParameters =\n  components[\"schemas\"][\"CreateEnrichmentParameters\"];\nexport type CreateImportParameters =\n  components[\"schemas\"][\"CreateImportParameters\"];\nexport type CreateImportResponse =\n  components[\"schemas\"][\"CreateImportResponse\"];\nexport type CreateMonitorParameters =\n  components[\"schemas\"][\"CreateMonitorParameters\"];\nexport type CreateWebhookParameters =\n  components[\"schemas\"][\"CreateWebhookParameters\"];\nexport type CreateWebsetParameters =\n  components[\"schemas\"][\"CreateWebsetParameters\"];\nexport type CreateWebsetSearchParameters =\n  components[\"schemas\"][\"CreateWebsetSearchParameters\"];\nexport type CustomEntity = components[\"schemas\"][\"CustomEntity\"];\nexport type EnrichmentResult = components[\"schemas\"][\"EnrichmentResult\"];\nexport type Entity = components[\"schemas\"][\"Entity\"];\nexport type Event = components[\"schemas\"][\"Event\"];\nexport type GetWebsetResponse = components[\"schemas\"][\"GetWebsetResponse\"];\nexport type Import = components[\"schemas\"][\"Import\"];\nexport type ListEventsResponse = components[\"schemas\"][\"ListEventsResponse\"];\nexport type ListImportsResponse = components[\"schemas\"][\"ListImportsResponse\"];\nexport type ListMonitorRunsResponse =\n  components[\"schemas\"][\"ListMonitorRunsResponse\"];\nexport type ListMonitorsResponse =\n  components[\"schemas\"][\"ListMonitorsResponse\"];\nexport type ListWebhookAttemptsResponse =\n  components[\"schemas\"][\"ListWebhookAttemptsResponse\"];\nexport type ListWebhooksResponse =\n  components[\"schemas\"][\"ListWebhooksResponse\"];\nexport type ListWebsetItemResponse =\n  components[\"schemas\"][\"ListWebsetItemResponse\"];\nexport type ListWebsetsResponse = components[\"schemas\"][\"ListWebsetsResponse\"];\nexport type Monitor = components[\"schemas\"][\"Monitor\"];\nexport type MonitorBehavior = components[\"schemas\"][\"MonitorBehavior\"];\nexport type MonitorCadence = components[\"schemas\"][\"MonitorCadence\"];\nexport type MonitorRun = components[\"schemas\"][\"MonitorRun\"];\nexport type PersonEntity = components[\"schemas\"][\"PersonEntity\"];\nexport type ResearchPaperEntity = components[\"schemas\"][\"ResearchPaperEntity\"];\nexport type UpdateImport = components[\"schemas\"][\"UpdateImport\"];\nexport type UpdateMonitor = components[\"schemas\"][\"UpdateMonitor\"];\nexport type UpdateWebhookParameters =\n  components[\"schemas\"][\"UpdateWebhookParameters\"];\nexport type UpdateWebsetRequest = components[\"schemas\"][\"UpdateWebsetRequest\"];\nexport type Webhook = components[\"schemas\"][\"Webhook\"];\nexport type WebhookAttempt = components[\"schemas\"][\"WebhookAttempt\"];\nexport type Webset = components[\"schemas\"][\"Webset\"];\nexport type WebsetEnrichment = components[\"schemas\"][\"WebsetEnrichment\"];\nexport type WebsetItem = components[\"schemas\"][\"WebsetItem\"];\nexport type WebsetItemArticleProperties =\n  components[\"schemas\"][\"WebsetItemArticleProperties\"];\nexport type WebsetItemCompanyProperties =\n  components[\"schemas\"][\"WebsetItemCompanyProperties\"];\nexport type WebsetItemCustomProperties =\n  components[\"schemas\"][\"WebsetItemCustomProperties\"];\nexport type WebsetItemEvaluation =\n  components[\"schemas\"][\"WebsetItemEvaluation\"];\nexport type WebsetItemPersonProperties =\n  components[\"schemas\"][\"WebsetItemPersonProperties\"];\nexport type WebsetItemResearchPaperProperties =\n  components[\"schemas\"][\"WebsetItemResearchPaperProperties\"];\nexport type WebsetSearch = components[\"schemas\"][\"WebsetSearch\"];\nexport type $defs = Record<string, never>;\nexport interface operations {\n  \"events-list\": {\n    parameters: {\n      query?: {\n        /** @description Filter events created after or at this timestamp (inclusive). Must be a valid ISO 8601 datetime string. All times are in UTC. */\n        createdAfter?: string;\n        /** @description Filter events created before or at this timestamp (inclusive). Must be a valid ISO 8601 datetime string. All times are in UTC. */\n        createdBefore?: string;\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The number of results to return */\n        limit?: number;\n        /** @description The types of events to filter by */\n        types?: EventType[];\n      };\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of events */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListEventsResponse\"];\n        };\n      };\n    };\n  };\n  \"events-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the event */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Event\"];\n        };\n      };\n      /** @description Event not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"imports-list\": {\n    parameters: {\n      query?: {\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The number of results to return */\n        limit?: number;\n      };\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of imports */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListImportsResponse\"];\n        };\n      };\n    };\n  };\n  \"imports-create\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"CreateImportParameters\"];\n      };\n    };\n    responses: {\n      /** @description Import created successfully */\n      201: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"CreateImportResponse\"];\n        };\n      };\n    };\n  };\n  \"imports-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Import */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Import details */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Import\"];\n        };\n      };\n    };\n  };\n  \"imports-delete\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Import */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Import deleted successfully */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Import\"];\n        };\n      };\n    };\n  };\n  \"imports-update\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Import */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"UpdateImport\"];\n      };\n    };\n    responses: {\n      /** @description Import updated successfully */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Import\"];\n        };\n      };\n    };\n  };\n  \"monitors-list\": {\n    parameters: {\n      query?: {\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The number of results to return */\n        limit?: number;\n        /** @description The id of the Webset to list monitors for */\n        websetId?: string;\n      };\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of monitors */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListMonitorsResponse\"];\n        };\n      };\n    };\n  };\n  \"monitors-create\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"CreateMonitorParameters\"];\n      };\n    };\n    responses: {\n      /** @description Monitor created successfully */\n      201: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Monitor\"];\n        };\n      };\n    };\n  };\n  \"monitors-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Monitor */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Monitor details */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Monitor\"];\n        };\n      };\n    };\n  };\n  \"monitors-delete\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Monitor */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Monitor deleted successfully */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Monitor\"];\n        };\n      };\n    };\n  };\n  \"monitors-update\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Monitor */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"UpdateMonitor\"];\n      };\n    };\n    responses: {\n      /** @description Monitor updated successfully */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Monitor\"];\n        };\n      };\n    };\n  };\n  \"monitors-runs-list\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Monitor to list runs for */\n        monitor: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of monitor runs */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListMonitorRunsResponse\"];\n        };\n      };\n    };\n  };\n  \"monitors-runs-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        id: string;\n        /** @description The id of the Monitor to get the run for */\n        monitor: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Monitor run details */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"MonitorRun\"];\n        };\n      };\n    };\n  };\n  \"webhooks-list\": {\n    parameters: {\n      query?: {\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The number of results to return */\n        limit?: number;\n      };\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of webhooks */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListWebhooksResponse\"];\n        };\n      };\n    };\n  };\n  \"webhooks-create\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"CreateWebhookParameters\"];\n      };\n    };\n    responses: {\n      /** @description Webhook */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webhook\"];\n        };\n      };\n    };\n  };\n  \"webhooks-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the webhook */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webhook */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webhook\"];\n        };\n      };\n      /** @description Webhook not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"webhooks-delete\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the webhook */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webhook */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webhook\"];\n        };\n      };\n      /** @description Webhook not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"webhooks-update\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the webhook */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"UpdateWebhookParameters\"];\n      };\n    };\n    responses: {\n      /** @description Webhook */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webhook\"];\n        };\n      };\n      /** @description Webhook not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"webhooks-attempts-list\": {\n    parameters: {\n      query?: {\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The type of event to filter by */\n        eventType?: EventType;\n        /** @description The number of results to return */\n        limit?: number;\n      };\n      header?: never;\n      path: {\n        /** @description The ID of the webhook */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of webhook attempts */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListWebhookAttemptsResponse\"];\n        };\n      };\n    };\n  };\n  \"websets-list\": {\n    parameters: {\n      query?: {\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The number of Websets to return */\n        limit?: number;\n      };\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description List of Websets */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListWebsetsResponse\"];\n        };\n      };\n    };\n  };\n  \"websets-create\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path?: never;\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"CreateWebsetParameters\"];\n      };\n    };\n    responses: {\n      /** @description Webset created */\n      201: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webset\"];\n        };\n      };\n      /** @description Webset with this externalId already exists */\n      409: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"websets-get\": {\n    parameters: {\n      query?: {\n        /** @description Expand the response with the specified resources */\n        expand?: PathsV0WebsetsIdGetParametersQueryExpand[];\n      };\n      header?: never;\n      path: {\n        /** @description The id or externalId of the Webset. */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webset */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"GetWebsetResponse\"];\n        };\n      };\n      /** @description Webset not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"websets-update\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id or externalId of the Webset */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"UpdateWebsetRequest\"];\n      };\n    };\n    responses: {\n      /** @description Webset updated */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webset\"];\n        };\n      };\n      /** @description Webset not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"websets-delete\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id or externalId of the Webset */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webset deleted */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webset\"];\n        };\n      };\n      /** @description Webset not found */\n      404: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content?: never;\n      };\n    };\n  };\n  \"websets-cancel\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id or externalId of the Webset */\n        id: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webset canceled */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"Webset\"];\n        };\n      };\n    };\n  };\n  \"websets-enrichments-create\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"CreateEnrichmentParameters\"];\n      };\n    };\n    responses: {\n      /** @description Enrichment created */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetEnrichment\"];\n        };\n      };\n    };\n  };\n  \"websets-enrichments-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Enrichment */\n        id: string;\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Enrichment */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetEnrichment\"];\n        };\n      };\n    };\n  };\n  \"websets-enrichments-delete\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Enrichment */\n        id: string;\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Enrichment deleted */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetEnrichment\"];\n        };\n      };\n    };\n  };\n  \"websets-enrichments-cancel\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Enrichment */\n        id: string;\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Enrichment cancelled */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetEnrichment\"];\n        };\n      };\n    };\n  };\n  \"websets-items-list\": {\n    parameters: {\n      query?: {\n        /** @description The cursor to paginate through the results */\n        cursor?: string;\n        /** @description The number of results to return */\n        limit?: number;\n      };\n      header?: never;\n      path: {\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webset Items */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"ListWebsetItemResponse\"];\n        };\n      };\n    };\n  };\n  \"websets-items-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Webset item */\n        id: string;\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webset Item */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetItem\"];\n        };\n      };\n    };\n  };\n  \"websets-items-delete\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Webset item */\n        id: string;\n        /** @description The id or externalId of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Webset Item deleted */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetItem\"];\n        };\n      };\n    };\n  };\n  \"websets-searches-create\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody: {\n      content: {\n        \"application/json\": components[\"schemas\"][\"CreateWebsetSearchParameters\"];\n      };\n    };\n    responses: {\n      /** @description Webset Search created */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetSearch\"];\n        };\n      };\n    };\n  };\n  \"websets-searches-get\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Search */\n        id: string;\n        /** @description The id of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Search retrieved */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetSearch\"];\n        };\n      };\n    };\n  };\n  \"websets-searches-cancel\": {\n    parameters: {\n      query?: never;\n      header?: never;\n      path: {\n        /** @description The id of the Search */\n        id: string;\n        /** @description The id of the Webset */\n        webset: string;\n      };\n      cookie?: never;\n    };\n    requestBody?: never;\n    responses: {\n      /** @description Search canceled */\n      200: {\n        headers: {\n          /**\n           * @description Unique identifier for the request.\n           * @example req_N6SsgoiaOQOPqsYKKiw5\n           */\n          \"X-Request-Id\": string;\n          [name: string]: unknown;\n        };\n        content: {\n          \"application/json\": components[\"schemas\"][\"WebsetSearch\"];\n        };\n      };\n    };\n  };\n}\nexport enum PathsV0WebsetsIdGetParametersQueryExpand {\n  items = \"items\",\n}\nexport enum CreateEnrichmentParametersFormat {\n  text = \"text\",\n  date = \"date\",\n  number = \"number\",\n  options = \"options\",\n  email = \"email\",\n  phone = \"phone\",\n}\nexport enum CreateImportParametersFormat {\n  csv = \"csv\",\n}\nexport enum CreateImportResponseFailedReason {\n  invalid_format = \"invalid_format\",\n  invalid_file_content = \"invalid_file_content\",\n  missing_identifier = \"missing_identifier\",\n}\nexport enum CreateImportResponseFormat {\n  csv = \"csv\",\n  webset = \"webset\",\n}\nexport enum CreateImportResponseObject {\n  import = \"import\",\n}\nexport enum CreateImportResponseStatus {\n  pending = \"pending\",\n  processing = \"processing\",\n  completed = \"completed\",\n  failed = \"failed\",\n}\nexport enum CreateMonitorParametersBehaviorConfigBehavior {\n  override = \"override\",\n  append = \"append\",\n}\nexport enum CreateWebsetParametersImportSource {\n  import = \"import\",\n  webset = \"webset\",\n}\nexport enum CreateWebsetParametersSearchExcludeSource {\n  import = \"import\",\n  webset = \"webset\",\n}\nexport enum CreateWebsetSearchParametersExcludeSource {\n  import = \"import\",\n  webset = \"webset\",\n}\nexport enum EnrichmentResultStatus {\n  pending = \"pending\",\n  completed = \"completed\",\n  canceled = \"canceled\",\n}\nexport enum EventType {\n  webset_created = \"webset.created\",\n  webset_deleted = \"webset.deleted\",\n  webset_paused = \"webset.paused\",\n  webset_idle = \"webset.idle\",\n  webset_search_created = \"webset.search.created\",\n  webset_search_canceled = \"webset.search.canceled\",\n  webset_search_completed = \"webset.search.completed\",\n  webset_search_updated = \"webset.search.updated\",\n  import_created = \"import.created\",\n  import_completed = \"import.completed\",\n  import_processing = \"import.processing\",\n  webset_item_created = \"webset.item.created\",\n  webset_item_enriched = \"webset.item.enriched\",\n  webset_export_created = \"webset.export.created\",\n  webset_export_completed = \"webset.export.completed\",\n}\nexport enum ImportFailedReason {\n  invalid_format = \"invalid_format\",\n  invalid_file_content = \"invalid_file_content\",\n  missing_identifier = \"missing_identifier\",\n}\nexport enum ImportFormat {\n  csv = \"csv\",\n  webset = \"webset\",\n}\nexport enum ImportObject {\n  import = \"import\",\n}\nexport enum ImportStatus {\n  pending = \"pending\",\n  processing = \"processing\",\n  completed = \"completed\",\n  failed = \"failed\",\n}\nexport enum MonitorObject {\n  monitor = \"monitor\",\n}\nexport enum MonitorStatus {\n  enabled = \"enabled\",\n  disabled = \"disabled\",\n}\nexport enum MonitorBehaviorConfigBehavior {\n  override = \"override\",\n  append = \"append\",\n}\nexport enum MonitorRunObject {\n  monitor_run = \"monitor_run\",\n}\nexport enum MonitorRunStatus {\n  created = \"created\",\n  running = \"running\",\n  completed = \"completed\",\n  canceled = \"canceled\",\n}\nexport enum MonitorRunType {\n  search = \"search\",\n  refresh = \"refresh\",\n}\nexport enum UpdateMonitorStatus {\n  enabled = \"enabled\",\n  disabled = \"disabled\",\n}\nexport enum WebhookStatus {\n  active = \"active\",\n  inactive = \"inactive\",\n}\nexport enum WebhookAttemptEventType {\n  webset_created = \"webset.created\",\n  webset_deleted = \"webset.deleted\",\n  webset_paused = \"webset.paused\",\n  webset_idle = \"webset.idle\",\n  webset_search_created = \"webset.search.created\",\n  webset_search_canceled = \"webset.search.canceled\",\n  webset_search_completed = \"webset.search.completed\",\n  webset_search_updated = \"webset.search.updated\",\n  import_created = \"import.created\",\n  import_completed = \"import.completed\",\n  import_processing = \"import.processing\",\n  webset_item_created = \"webset.item.created\",\n  webset_item_enriched = \"webset.item.enriched\",\n  webset_export_created = \"webset.export.created\",\n  webset_export_completed = \"webset.export.completed\",\n}\nexport enum WebsetStatus {\n  idle = \"idle\",\n  running = \"running\",\n  paused = \"paused\",\n}\nexport enum WebsetEnrichmentStatus {\n  pending = \"pending\",\n  canceled = \"canceled\",\n  completed = \"completed\",\n}\nexport enum WebsetEnrichmentFormat {\n  text = \"text\",\n  date = \"date\",\n  number = \"number\",\n  options = \"options\",\n  email = \"email\",\n  phone = \"phone\",\n}\nexport enum WebsetItemSource {\n  search = \"search\",\n  import = \"import\",\n}\nexport enum WebsetItemEvaluationSatisfied {\n  yes = \"yes\",\n  no = \"no\",\n  unclear = \"unclear\",\n}\nexport enum WebsetSearchExcludeSource {\n  import = \"import\",\n  webset = \"webset\",\n}\nexport enum WebsetSearchRecallExpectedConfidence {\n  high = \"high\",\n  medium = \"medium\",\n  low = \"low\",\n}\nexport enum WebsetSearchStatus {\n  created = \"created\",\n  running = \"running\",\n  completed = \"completed\",\n  canceled = \"canceled\",\n}\nexport enum WebsetSearchBehavior {\n  override = \"override\",\n  append = \"append\",\n}\nexport enum WebsetSearchCanceledReason {\n  webset_deleted = \"webset_deleted\",\n  webset_canceled = \"webset_canceled\",\n}\n", "/**\n * Client for managing Imports\n */\nimport { Exa<PERSON><PERSON><PERSON>, HttpStatusCode } from \"../errors\";\nimport { PaginationParams, WebsetsBaseClient } from \"./base\";\nimport {\n  CreateImportParameters,\n  CreateImportParametersFormat,\n  CreateImportResponse,\n  Import,\n  ImportStatus,\n  ListImportsResponse,\n  UpdateImport,\n} from \"./openapi\";\n\n/**\n * Options for waiting until import completion\n */\nexport interface WaitUntilCompletedOptions {\n  /**\n   * Maximum time to wait in milliseconds (default: 5 minutes)\n   */\n  timeout?: number;\n  /**\n   * How often to poll for status in milliseconds (default: 2 seconds)\n   */\n  pollInterval?: number;\n  /**\n   * Callback function called on each poll with the current status\n   */\n  onPoll?: (status: ImportStatus) => void;\n}\n\n/**\n * Parameters for creating an import with CSV data\n */\nexport interface CreateImportWithCsvParameters {\n  /**\n   * Title of the import\n   */\n  title: string;\n  /**\n   * Entity type and configuration\n   */\n  entity: CreateImportParameters[\"entity\"];\n  /**\n   * Optional metadata\n   */\n  metadata?: CreateImportParameters[\"metadata\"];\n  /**\n   * Optional CSV-specific parameters\n   */\n  csv?: CreateImportParameters[\"csv\"];\n}\n\n/**\n * CSV data input - can be raw data or buffer\n */\nexport type CsvDataInput = string | Buffer;\n\n/**\n * Client for managing Imports\n */\nexport class ImportsClient extends WebsetsBaseClient {\n  /**\n   * Create a new Import (basic version - returns upload URL)\n   * @param params The import creation parameters\n   * @returns The created Import response with upload URL\n   */\n  async create(params: CreateImportParameters): Promise<CreateImportResponse>;\n\n  /**\n   * Create a new Import with CSV data (handles upload)\n   * @param params The import creation parameters (without size/count - calculated automatically)\n   * @param csv CSV data as string or Buffer\n   * @returns The Import after upload (not waited for completion)\n   */\n  async create(\n    params: CreateImportWithCsvParameters,\n    csv: CsvDataInput\n  ): Promise<Import>;\n\n  async create(\n    params: CreateImportParameters | CreateImportWithCsvParameters,\n    csv?: CsvDataInput\n  ): Promise<CreateImportResponse | Import> {\n    if (csv === undefined) {\n      return this.request<CreateImportResponse>(\n        \"/v0/imports\",\n        \"POST\",\n        params as CreateImportParameters\n      );\n    }\n\n    let csvBuffer: Buffer;\n    if (typeof csv === \"string\") {\n      csvBuffer = Buffer.from(csv, \"utf8\");\n    } else if (Buffer.isBuffer(csv)) {\n      csvBuffer = csv;\n    } else {\n      throw new ExaError(\n        \"Invalid CSV data input. Must be string or Buffer\",\n        HttpStatusCode.BadRequest\n      );\n    }\n\n    const sizeInBytes = csvBuffer.length;\n    const sizeInMB = Math.max(1, Math.ceil(sizeInBytes / (1024 * 1024)));\n\n    const csvText = csvBuffer.toString(\"utf8\");\n    const lines = csvText.split(\"\\n\").filter((line) => line.trim().length > 0);\n    const recordCount = Math.max(0, lines.length - 1); // Subtract 1 for header\n\n    if (sizeInMB > 50) {\n      throw new ExaError(\n        `CSV file too large: ${sizeInMB}MB. Maximum size is 50MB.`,\n        HttpStatusCode.BadRequest\n      );\n    }\n\n    if (recordCount === 0) {\n      throw new ExaError(\n        \"CSV file appears to have no data records (only header or empty)\",\n        HttpStatusCode.BadRequest\n      );\n    }\n\n    const createParams: CreateImportParameters = {\n      title: params.title,\n      format: CreateImportParametersFormat.csv,\n      entity: params.entity,\n      size: sizeInBytes,\n      count: recordCount,\n      metadata: params.metadata,\n      csv: (params as CreateImportWithCsvParameters).csv,\n    };\n\n    const importResponse = await this.request<CreateImportResponse>(\n      \"/v0/imports\",\n      \"POST\",\n      createParams\n    );\n\n    try {\n      const uploadResponse = await fetch(importResponse.uploadUrl, {\n        method: \"PUT\",\n        body: csvBuffer,\n      });\n\n      if (!uploadResponse.ok) {\n        const errorText = await uploadResponse.text();\n        throw new ExaError(\n          `Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}. ${errorText}`,\n          HttpStatusCode.BadRequest\n        );\n      }\n    } catch (error) {\n      if (error instanceof ExaError) {\n        throw error;\n      }\n      throw new ExaError(\n        `Failed to upload CSV data: ${(error as Error).message}`,\n        HttpStatusCode.BadRequest\n      );\n    }\n\n    return importResponse;\n  }\n\n  /**\n   * Get an Import by ID\n   * @param id The ID of the Import\n   * @returns The Import\n   */\n  async get(id: string): Promise<Import> {\n    return this.request<Import>(`/v0/imports/${id}`, \"GET\");\n  }\n\n  /**\n   * List all Imports\n   * @param options Pagination options\n   * @returns The list of Imports\n   */\n  async list(options?: PaginationParams): Promise<ListImportsResponse> {\n    const params = this.buildPaginationParams(options);\n    return this.request<ListImportsResponse>(\n      \"/v0/imports\",\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Update an Import\n   * @param id The ID of the Import\n   * @param params The import update parameters\n   * @returns The updated Import\n   */\n  async update(id: string, params: UpdateImport): Promise<Import> {\n    return this.request<Import>(`/v0/imports/${id}`, \"PATCH\", params);\n  }\n\n  /**\n   * Delete an Import\n   * @param id The ID of the Import\n   * @returns The deleted Import\n   */\n  async delete(id: string): Promise<Import> {\n    return this.request<Import>(`/v0/imports/${id}`, \"DELETE\");\n  }\n\n  /**\n   * Wait until an Import is completed or failed\n   * @param id The ID of the Import\n   * @param options Configuration options for timeout and polling\n   * @returns The Import once it reaches a final state (completed or failed)\n   * @throws Error if the Import does not complete within the timeout or fails\n   */\n  async waitUntilCompleted(\n    id: string,\n    options?: WaitUntilCompletedOptions\n  ): Promise<Import> {\n    const timeout = options?.timeout ?? 30 * 60 * 1000;\n    const pollInterval = options?.pollInterval ?? 2_000;\n    const onPoll = options?.onPoll;\n\n    const startTime = Date.now();\n\n    while (true) {\n      const importItem = await this.get(id);\n\n      if (onPoll) {\n        onPoll(importItem.status);\n      }\n\n      if (importItem.status === ImportStatus.completed) {\n        return importItem;\n      }\n\n      if (importItem.status === ImportStatus.failed) {\n        throw new ExaError(\n          `Import ${id} failed: ${importItem.failedMessage || \"Unknown error\"}`,\n          HttpStatusCode.BadRequest\n        );\n      }\n\n      if (Date.now() - startTime > timeout) {\n        throw new ExaError(\n          `Import ${id} did not complete within ${timeout}ms. Current status: ${importItem.status}`,\n          HttpStatusCode.RequestTimeout\n        );\n      }\n\n      await new Promise((resolve) => setTimeout(resolve, pollInterval));\n    }\n  }\n}\n", "/**\n * Client for managing Webset Items\n */\nimport { PaginationParams, WebsetsBaseClient } from \"./base\";\nimport { ListWebsetItemResponse, WebsetItem } from \"./openapi\";\n\n/**\n * Client for managing Webset Items\n */\nexport class WebsetItemsClient extends WebsetsBaseClient {\n  /**\n   * List all Items for a Webset\n   * @param websetId The ID of the Webset\n   * @param params - Optional pagination parameters\n   * @returns A promise that resolves with the list of Items\n   */\n  list(\n    websetId: string,\n    params?: PaginationParams\n  ): Promise<ListWebsetItemResponse> {\n    const queryParams = this.buildPaginationParams(params);\n    return this.request<ListWebsetItemResponse>(\n      `/v0/websets/${websetId}/items`,\n      \"GET\",\n      undefined,\n      queryParams\n    );\n  }\n\n  /**\n   * Iterate through all Items in a Webset, handling pagination automatically\n   * @param websetId The ID of the Webset\n   * @param options Pagination options\n   * @returns Async generator of Webset Items\n   */\n  async *listAll(\n    websetId: string,\n    options?: PaginationParams\n  ): AsyncGenerator<WebsetItem> {\n    let cursor: string | undefined = undefined;\n    const pageOptions = options ? { ...options } : {};\n\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.list(websetId, pageOptions);\n\n      for (const item of response.data) {\n        yield item;\n      }\n\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n\n      cursor = response.nextCursor;\n    }\n  }\n\n  /**\n   * Collect all items from a Webset into an array\n   * @param websetId The ID of the Webset\n   * @param options Pagination options\n   * @returns Promise resolving to an array of all Webset Items\n   */\n  async getAll(\n    websetId: string,\n    options?: PaginationParams\n  ): Promise<WebsetItem[]> {\n    const items: WebsetItem[] = [];\n    for await (const item of this.listAll(websetId, options)) {\n      items.push(item);\n    }\n    return items;\n  }\n\n  /**\n   * Get an Item by ID\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Item\n   * @returns The Webset Item\n   */\n  async get(websetId: string, id: string): Promise<WebsetItem> {\n    return this.request<WebsetItem>(\n      `/v0/websets/${websetId}/items/${id}`,\n      \"GET\"\n    );\n  }\n\n  /**\n   * Delete an Item\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Item\n   * @returns The deleted Webset Item\n   */\n  async delete(websetId: string, id: string): Promise<WebsetItem> {\n    return this.request<WebsetItem>(\n      `/v0/websets/${websetId}/items/${id}`,\n      \"DELETE\"\n    );\n  }\n}\n", "/**\n * Client for managing Webset Monitors\n */\nimport { Exa } from \"..\";\nimport { PaginationParams, WebsetsBaseClient } from \"./base\";\nimport {\n  CreateMonitorParameters,\n  ListMonitorRunsResponse,\n  ListMonitorsResponse,\n  Monitor,\n  MonitorRun,\n  UpdateMonitor,\n} from \"./openapi\";\n\n/**\n * Options for listing monitors\n */\nexport interface ListMonitorsOptions extends PaginationParams {\n  /**\n   * The id of the Webset to list monitors for\n   */\n  websetId?: string;\n}\n\n/**\n * Client for managing Monitor Runs\n */\nexport class WebsetMonitorRunsClient extends WebsetsBaseClient {\n  /**\n   * List all runs for a Monitor\n   * @param monitorId The ID of the Monitor\n   * @param options Pagination options\n   * @returns The list of Monitor runs\n   */\n  async list(\n    monitorId: string,\n    options?: PaginationParams\n  ): Promise<ListMonitorRunsResponse> {\n    const params = this.buildPaginationParams(options);\n    return this.request<ListMonitorRunsResponse>(\n      `/v0/monitors/${monitorId}/runs`,\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Get a specific Monitor run\n   * @param monitorId The ID of the Monitor\n   * @param runId The ID of the Monitor run\n   * @returns The Monitor run\n   */\n  async get(monitorId: string, runId: string): Promise<MonitorRun> {\n    return this.request<MonitorRun>(\n      `/v0/monitors/${monitorId}/runs/${runId}`,\n      \"GET\"\n    );\n  }\n}\n\n/**\n * Client for managing Webset Monitors\n */\nexport class WebsetMonitorsClient extends WebsetsBaseClient {\n  /**\n   * Client for managing Monitor Runs\n   */\n  runs: WebsetMonitorRunsClient;\n\n  constructor(client: Exa) {\n    super(client);\n    this.runs = new WebsetMonitorRunsClient(client);\n  }\n\n  /**\n   * Create a Monitor\n   * @param params The monitor parameters\n   * @returns The created Monitor\n   */\n  async create(params: CreateMonitorParameters): Promise<Monitor> {\n    return this.request<Monitor>(\"/v0/monitors\", \"POST\", params);\n  }\n\n  /**\n   * Get a Monitor by ID\n   * @param id The ID of the Monitor\n   * @returns The Monitor\n   */\n  async get(id: string): Promise<Monitor> {\n    return this.request<Monitor>(`/v0/monitors/${id}`, \"GET\");\n  }\n\n  /**\n   * List all Monitors\n   * @param options Pagination and filtering options\n   * @returns The list of Monitors\n   */\n  async list(options?: ListMonitorsOptions): Promise<ListMonitorsResponse> {\n    const params = {\n      cursor: options?.cursor,\n      limit: options?.limit,\n      websetId: options?.websetId,\n    };\n    return this.request<ListMonitorsResponse>(\n      \"/v0/monitors\",\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Update a Monitor\n   * @param id The ID of the Monitor\n   * @param params The monitor update parameters (status, metadata)\n   * @returns The updated Monitor\n   */\n  async update(id: string, params: UpdateMonitor): Promise<Monitor> {\n    return this.request<Monitor>(`/v0/monitors/${id}`, \"PATCH\", params);\n  }\n\n  /**\n   * Delete a Monitor\n   * @param id The ID of the Monitor\n   * @returns The deleted Monitor\n   */\n  async delete(id: string): Promise<Monitor> {\n    return this.request<Monitor>(`/v0/monitors/${id}`, \"DELETE\");\n  }\n}\n", "/**\n * Client for managing Webset Searches\n */\nimport { WebsetsBaseClient } from \"./base\";\nimport { CreateWebsetSearchParameters, WebsetSearch } from \"./openapi\";\n\n/**\n * Client for managing Webset Searches\n */\nexport class WebsetSearchesClient extends WebsetsBaseClient {\n  /**\n   * Create a new Search for the Webset\n   * @param websetId The ID of the Webset\n   * @param params The search parameters\n   * @returns The created Webset Search\n   */\n  async create(\n    websetId: string,\n    params: CreateWebsetSearchParameters\n  ): Promise<WebsetSearch> {\n    return this.request<WebsetSearch>(\n      `/v0/websets/${websetId}/searches`,\n      \"POST\",\n      params\n    );\n  }\n\n  /**\n   * Get a Search by ID\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Search\n   * @returns The Webset Search\n   */\n  async get(websetId: string, id: string): Promise<WebsetSearch> {\n    return this.request<WebsetSearch>(\n      `/v0/websets/${websetId}/searches/${id}`,\n      \"GET\"\n    );\n  }\n\n  /**\n   * Cancel a running Search\n   * @param websetId The ID of the Webset\n   * @param id The ID of the Search\n   * @returns The canceled Webset Search\n   */\n  async cancel(websetId: string, id: string): Promise<WebsetSearch> {\n    return this.request<WebsetSearch>(\n      `/v0/websets/${websetId}/searches/${id}/cancel`,\n      \"POST\"\n    );\n  }\n}\n", "/**\n * Client for managing Webset Webhooks\n */\nimport { PaginationParams, WebsetsBaseClient } from \"./base\";\nimport {\n  CreateWebhookParameters,\n  EventType,\n  ListWebhookAttemptsResponse,\n  ListWebhooksResponse,\n  UpdateWebhookParameters,\n  Webhook,\n  WebhookAttempt,\n} from \"./openapi\";\n\n/**\n * Options for listing webhooks (only pagination is supported by API)\n */\nexport interface ListWebhooksOptions extends PaginationParams {}\n\n/**\n * Options for listing webhook attempts\n */\nexport interface ListWebhookAttemptsOptions extends PaginationParams {\n  /**\n   * The type of event to filter by\n   */\n  eventType?: EventType;\n}\n\n/**\n * Client for managing Webset Webhooks\n */\nexport class WebsetWebhooksClient extends WebsetsBaseClient {\n  /**\n   * Create a Webhook\n   * @param params The webhook parameters\n   * @returns The created Webhook\n   */\n  async create(params: CreateWebhookParameters): Promise<Webhook> {\n    return this.request<Webhook>(\"/v0/webhooks\", \"POST\", params);\n  }\n\n  /**\n   * Get a Webhook by ID\n   * @param id The ID of the Webhook\n   * @returns The Webhook\n   */\n  async get(id: string): Promise<Webhook> {\n    return this.request<Webhook>(`/v0/webhooks/${id}`, \"GET\");\n  }\n\n  /**\n   * List all Webhooks\n   * @param options Pagination options\n   * @returns The list of Webhooks\n   */\n  async list(options?: ListWebhooksOptions): Promise<ListWebhooksResponse> {\n    const params = this.buildPaginationParams(options);\n    return this.request<ListWebhooksResponse>(\n      \"/v0/webhooks\",\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Iterate through all Webhooks, handling pagination automatically\n   * @param options Pagination options\n   * @returns Async generator of Webhooks\n   */\n  async *listAll(options?: ListWebhooksOptions): AsyncGenerator<Webhook> {\n    let cursor: string | undefined = undefined;\n    const pageOptions = options ? { ...options } : {};\n\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.list(pageOptions);\n\n      for (const webhook of response.data) {\n        yield webhook;\n      }\n\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n\n      cursor = response.nextCursor;\n    }\n  }\n\n  /**\n   * Collect all Webhooks into an array\n   * @param options Pagination options\n   * @returns Promise resolving to an array of all Webhooks\n   */\n  async getAll(options?: ListWebhooksOptions): Promise<Webhook[]> {\n    const webhooks: Webhook[] = [];\n    for await (const webhook of this.listAll(options)) {\n      webhooks.push(webhook);\n    }\n    return webhooks;\n  }\n\n  /**\n   * Update a Webhook\n   * @param id The ID of the Webhook\n   * @param params The webhook update parameters (events, metadata, url)\n   * @returns The updated Webhook\n   */\n  async update(id: string, params: UpdateWebhookParameters): Promise<Webhook> {\n    return this.request<Webhook>(`/v0/webhooks/${id}`, \"PATCH\", params);\n  }\n\n  /**\n   * Delete a Webhook\n   * @param id The ID of the Webhook\n   * @returns The deleted Webhook\n   */\n  async delete(id: string): Promise<Webhook> {\n    return this.request<Webhook>(`/v0/webhooks/${id}`, \"DELETE\");\n  }\n\n  /**\n   * List all attempts for a Webhook\n   * @param id The ID of the Webhook\n   * @param options Pagination and filtering options\n   * @returns The list of Webhook attempts\n   */\n  async listAttempts(\n    id: string,\n    options?: ListWebhookAttemptsOptions\n  ): Promise<ListWebhookAttemptsResponse> {\n    const params = {\n      cursor: options?.cursor,\n      limit: options?.limit,\n      eventType: options?.eventType,\n    };\n\n    return this.request<ListWebhookAttemptsResponse>(\n      `/v0/webhooks/${id}/attempts`,\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Iterate through all attempts for a Webhook, handling pagination automatically\n   * @param id The ID of the Webhook\n   * @param options Pagination and filtering options\n   * @returns Async generator of Webhook attempts\n   */\n  async *listAllAttempts(\n    id: string,\n    options?: ListWebhookAttemptsOptions\n  ): AsyncGenerator<WebhookAttempt> {\n    let cursor: string | undefined = undefined;\n    const pageOptions = options ? { ...options } : {};\n\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.listAttempts(id, pageOptions);\n\n      for (const attempt of response.data) {\n        yield attempt;\n      }\n\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n\n      cursor = response.nextCursor;\n    }\n  }\n\n  /**\n   * Collect all attempts for a Webhook into an array\n   * @param id The ID of the Webhook\n   * @param options Pagination and filtering options\n   * @returns Promise resolving to an array of all Webhook attempts\n   */\n  async getAllAttempts(\n    id: string,\n    options?: ListWebhookAttemptsOptions\n  ): Promise<WebhookAttempt[]> {\n    const attempts: WebhookAttempt[] = [];\n    for await (const attempt of this.listAllAttempts(id, options)) {\n      attempts.push(attempt);\n    }\n    return attempts;\n  }\n}\n", "/**\n * Main client for Websets API\n */\nimport { <PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, HttpStatusCode } from \"../index\";\nimport { PaginationParams, WebsetsBaseClient } from \"./base\";\nimport { WebsetEnrichmentsClient } from \"./enrichments\";\nimport { EventsClient } from \"./events\";\nimport { ImportsClient } from \"./imports\";\nimport { WebsetItemsClient } from \"./items\";\nimport { WebsetMonitorsClient } from \"./monitors\";\nimport {\n  CreateWebsetParameters,\n  GetWebsetResponse,\n  ListWebsetsResponse,\n  UpdateWebsetRequest,\n  Webset,\n  WebsetStatus,\n} from \"./openapi\";\nimport { WebsetSearchesClient } from \"./searches\";\nimport { WebsetWebhooksClient } from \"./webhooks\";\n\n/**\n * Options for listing Websets (API only supports pagination)\n */\nexport interface ListWebsetsOptions extends PaginationParams {}\n\n/**\n * Client for managing Websets\n */\nexport class WebsetsClient extends WebsetsBaseClient {\n  /**\n   * Client for managing Events\n   */\n  events: EventsClient;\n\n  /**\n   * Client for managing Imports\n   */\n  imports: ImportsClient;\n\n  /**\n   * Client for managing Webset Items\n   */\n  items: WebsetItemsClient;\n\n  /**\n   * Client for managing Webset Searches\n   */\n  searches: WebsetSearchesClient;\n\n  /**\n   * Client for managing Webset Enrichments\n   */\n  enrichments: WebsetEnrichmentsClient;\n\n  /**\n   * Client for managing Webset Monitors\n   */\n  monitors: WebsetMonitorsClient;\n\n  /**\n   * Client for managing Webset Webhooks\n   */\n  webhooks: WebsetWebhooksClient;\n\n  /**\n   * Initialize a new Websets client\n   * @param client The Exa client instance\n   */\n  constructor(client: Exa) {\n    super(client);\n    this.events = new EventsClient(client);\n    this.imports = new ImportsClient(client);\n    this.items = new WebsetItemsClient(client);\n    this.searches = new WebsetSearchesClient(client);\n    this.enrichments = new WebsetEnrichmentsClient(client);\n    this.monitors = new WebsetMonitorsClient(client);\n    this.webhooks = new WebsetWebhooksClient(client);\n  }\n\n  /**\n   * Create a new Webset\n   * @param params The Webset creation parameters\n   * @returns The created Webset\n   */\n  async create(params: CreateWebsetParameters): Promise<Webset> {\n    return this.request<Webset>(\"/v0/websets\", \"POST\", params);\n  }\n\n  /**\n   * Get a Webset by ID\n   * @param id The ID of the Webset\n   * @param expand Optional array of relations to expand\n   * @returns The Webset\n   */\n  async get(id: string, expand?: Array<\"items\">): Promise<GetWebsetResponse> {\n    const params: { expand?: Array<\"items\"> } = {};\n    if (expand) {\n      params.expand = expand;\n    }\n\n    return this.request<GetWebsetResponse>(\n      `/v0/websets/${id}`,\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * List all Websets\n   * @param options Pagination options (filtering by status is not supported by API)\n   * @returns The list of Websets\n   */\n  async list(options?: ListWebsetsOptions): Promise<ListWebsetsResponse> {\n    const params = this.buildPaginationParams(options);\n    return this.request<ListWebsetsResponse>(\n      \"/v0/websets\",\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n\n  /**\n   * Iterate through all Websets, handling pagination automatically\n   * @param options Pagination options\n   * @returns Async generator of Websets\n   */\n  async *listAll(options?: ListWebsetsOptions): AsyncGenerator<Webset> {\n    let cursor: string | undefined = undefined;\n    const pageOptions = options ? { ...options } : {};\n\n    while (true) {\n      pageOptions.cursor = cursor;\n      const response = await this.list(pageOptions);\n\n      for (const webset of response.data) {\n        yield webset;\n      }\n\n      if (!response.hasMore || !response.nextCursor) {\n        break;\n      }\n\n      cursor = response.nextCursor;\n    }\n  }\n\n  /**\n   * Collect all Websets into an array\n   * @param options Pagination options\n   * @returns Promise resolving to an array of all Websets\n   */\n  async getAll(options?: ListWebsetsOptions): Promise<Webset[]> {\n    const websets: Webset[] = [];\n    for await (const webset of this.listAll(options)) {\n      websets.push(webset);\n    }\n    return websets;\n  }\n\n  /**\n   * Update a Webset\n   * @param id The ID of the Webset\n   * @param params The Webset update parameters\n   * @returns The updated Webset\n   */\n  async update(id: string, params: UpdateWebsetRequest): Promise<Webset> {\n    return this.request<Webset>(`/v0/websets/${id}`, \"POST\", params);\n  }\n\n  /**\n   * Delete a Webset\n   * @param id The ID of the Webset\n   * @returns The deleted Webset\n   */\n  async delete(id: string): Promise<Webset> {\n    return this.request<Webset>(`/v0/websets/${id}`, \"DELETE\");\n  }\n\n  /**\n   * Cancel a running Webset\n   * @param id The ID or external ID of the Webset\n   * @returns The canceled Webset (as returned by the API)\n   */\n  async cancel(id: string): Promise<Webset> {\n    return this.request<Webset>(`/v0/websets/${id}/cancel`, \"POST\");\n  }\n\n  /**\n   * Wait until a Webset is idle\n   * @param id The ID of the Webset\n   * @param options Configuration options for timeout and polling\n   * @returns The Webset once it becomes idle\n   * @throws Error if the Webset does not become idle within the timeout\n   */\n  async waitUntilIdle(\n    id: string,\n    options?:\n      | {\n          timeout?: number;\n          pollInterval?: number;\n          onPoll?: (status: WebsetStatus) => void;\n        }\n      | number // Legacy: timeout only\n  ): Promise<Webset> {\n    let timeout: number | undefined;\n    let pollInterval = 1000;\n    let onPoll: ((status: WebsetStatus) => void) | undefined;\n\n    // Handle legacy timeout argument or new options object\n    if (typeof options === \"number\") {\n      timeout = options;\n    } else if (options) {\n      timeout = options.timeout;\n      pollInterval = options.pollInterval || 1000;\n      onPoll = options.onPoll;\n    }\n\n    const startTime = Date.now();\n\n    while (true) {\n      const webset = await this.get(id);\n\n      if (onPoll) {\n        onPoll(webset.status);\n      }\n\n      if (webset.status === WebsetStatus.idle) {\n        return webset;\n      }\n\n      if (timeout && Date.now() - startTime > timeout) {\n        throw new ExaError(\n          `Webset ${id} did not reach idle state within ${timeout}ms. Current status: ${webset.status}`,\n          HttpStatusCode.RequestTimeout\n        );\n      }\n\n      await new Promise((resolve) => setTimeout(resolve, pollInterval));\n    }\n  }\n}\n", "import { ZodType, ZodSchema } from \"zod\";\nimport { zodToJsonSchema as convertZodToJsonSchema } from \"zod-to-json-schema\";\n\nexport function isZodSchema(obj: any): obj is ZodSchema<any> {\n  return obj instanceof ZodType;\n}\n\nexport function zodToJsonSchema(\n  schema: ZodSchema<any>\n): Record<string, unknown> {\n  return convertZodToJsonSchema(schema, {\n    $refStrategy: \"none\",\n  }) as Record<string, unknown>;\n}\n", "import { Exa, ListResearchTasksRequest } from \"../index\";\n\ntype QueryParams = Record<\n  string,\n  string | number | boolean | string[] | undefined\n>;\n\ninterface RequestBody {\n  [key: string]: unknown;\n}\n\n/**\n * Base client class for all Research-related API clients\n */\nexport class ResearchBaseClient {\n  protected client: Exa;\n\n  /**\n   * Initialize a new Research base client\n   * @param client The Exa client instance\n   */\n  constructor(client: Exa) {\n    this.client = client;\n  }\n\n  /**\n   * Make a request to the Research API (prefixes all paths with `/research`).\n   * @param endpoint The endpoint path, beginning with a slash (e.g. \"/tasks\").\n   * @param method The HTTP method. Defaults to \"POST\".\n   * @param data Optional request body\n   * @param params Optional query parameters\n   * @returns The parsed JSON response\n   */\n  protected async request<T = unknown>(\n    endpoint: string,\n    method: string = \"POST\",\n    data?: RequestBody,\n    params?: QueryParams\n  ): Promise<T> {\n    // Delegate to the root Exa client. Internally this handles error mapping and\n    // query-string construction.\n    return this.client.request<T>(\n      `/research/v0${endpoint}`,\n      method,\n      data,\n      params\n    );\n  }\n\n  /**\n   * Make a request to the Research API (prefixes all paths with `/research`).\n   * @param endpoint The endpoint path, beginning with a slash (e.g. \"/tasks\").\n   * @param method The HTTP method. Defaults to \"POST\".\n   * @param data Optional request body\n   * @param params Optional query parameters\n   * @returns The parsed JSON response\n   */\n  protected async rawRequest(\n    endpoint: string,\n    method: string = \"POST\",\n    data?: RequestBody,\n    params?: QueryParams\n  ): Promise<Response> {\n    // Delegate to the root Exa client. Internally this handles error mapping and\n    // query-string construction.\n    return this.client.rawRequest(\n      `/research/v0${endpoint}`,\n      method,\n      data,\n      params\n    );\n  }\n\n  /**\n   * Helper to build pagination parameters.\n   * @param pagination The pagination parameters\n   * @returns QueryParams object with pagination parameters\n   */\n  protected buildPaginationParams(\n    pagination?: ListResearchTasksRequest\n  ): QueryParams {\n    const params: QueryParams = {};\n    if (!pagination) return params;\n\n    if (pagination.cursor) params.cursor = pagination.cursor;\n    if (pagination.limit) params.limit = pagination.limit;\n\n    return params;\n  }\n}\n", "import {\n  Exa,\n  ListResearchTasksRequest,\n  ListResearchTasksResponse,\n  ResearchTaskEvent,\n  ResearchTask,\n} from \"../index\";\nimport {\n  JSONSchema,\n  ResearchCreateTaskRequest,\n  ResearchCreateTaskResponse,\n  ResearchCreateTaskParamsTyped,\n} from \"../index\";\nimport { ZodSchema } from \"zod\";\nimport { isZodSchema, zodToJsonSchema } from \"../zod-utils\";\nimport { ResearchBaseClient } from \"./base\";\n\n/**\n * Client for interacting with the Research Tasks API.\n */\nexport class ResearchClient extends ResearchBaseClient {\n  constructor(client: Exa) {\n    super(client);\n  }\n\n  /**\n   * Create a new research task with Zod schema for strongly typed output\n   */\n  async createTask<T>(\n    params: ResearchCreateTaskParamsTyped<ZodSchema<T>>\n  ): Promise<ResearchCreateTaskResponse>;\n\n  /**\n   * Create a new research task.\n   *\n   * @param params Object containing:\n   *   - model: The research model to use (e.g., ResearchModel.ExaResearch).\n   *   - instructions: High-level guidance for the research agent.\n   *   - output: An object with a `schema` property (JSONSchema) that defines the expected output structure.\n   *\n   * @returns An object containing the unique ID of the created research task.\n   */\n  async createTask(params: {\n    instructions: string;\n    model?: \"exa-research\" | \"exa-research-pro\";\n    output?: { inferSchema?: boolean; schema?: JSONSchema };\n  }): Promise<ResearchCreateTaskResponse>;\n\n  async createTask<T>(params: {\n    instructions: string;\n    model?: \"exa-research\" | \"exa-research-pro\";\n    output?: { inferSchema?: boolean; schema?: JSONSchema | ZodSchema<T> };\n  }): Promise<ResearchCreateTaskResponse> {\n    // Ensure we have a model (default to exa_research)\n    const { instructions, model, output } = params;\n\n    // Convert Zod schema to JSON schema if needed\n    let schema = output?.schema;\n    if (schema && isZodSchema(schema)) {\n      schema = zodToJsonSchema(schema);\n    }\n\n    const payload: ResearchCreateTaskRequest = {\n      instructions,\n      model: model ?? \"exa-research\",\n      output: output\n        ? {\n            schema,\n            inferSchema: output.inferSchema ?? true,\n          }\n        : { inferSchema: true },\n    };\n\n    return this.request<ResearchCreateTaskResponse>(\"/tasks\", \"POST\", payload);\n  }\n\n  /**\n   * Retrieve a research task by ID.\n   *\n   * Overloads:\n   *   - getTask(id)\n   *   - getTask(id, {stream: false})\n   *     => Promise<ResearchTask>\n   *   - getTask(id, {stream: true})\n   *     => AsyncGenerator<ResearchTaskEvent>\n   */\n  getTask(id: string): Promise<ResearchTask>;\n  getTask(id: string, options: { stream?: false }): Promise<ResearchTask>;\n  getTask(\n    id: string,\n    options: { stream: true }\n  ): Promise<AsyncGenerator<ResearchTaskEvent, any, any>>;\n  getTask(\n    id: string,\n    options?: { stream?: boolean }\n  ): Promise<ResearchTask> | Promise<AsyncGenerator<ResearchTaskEvent>> {\n    if (options?.stream) {\n      const promise = async () => {\n        const resp = await this.rawRequest(`/tasks/${id}?stream=true`, \"GET\");\n        if (!resp.body) {\n          throw new Error(\"No response body for SSE stream\");\n        }\n        const reader = resp.body.getReader();\n        const decoder = new TextDecoder();\n        let buffer = \"\";\n\n        function processPart(part: string): ResearchTaskEvent | null {\n          const lines = part.split(\"\\n\");\n          let data = lines.slice(1).join(\"\\n\");\n          if (data.startsWith(\"data:\")) {\n            data = data.slice(5).trimStart();\n          }\n          try {\n            return JSON.parse(data);\n          } catch (e) {\n            return null;\n          }\n        }\n\n        async function* streamEvents() {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            buffer += decoder.decode(value, { stream: true });\n\n            let parts = buffer.split(\"\\n\\n\");\n            buffer = parts.pop() ?? \"\";\n\n            for (const part of parts) {\n              const processed = processPart(part);\n              if (processed) {\n                yield processed;\n              }\n            }\n          }\n          if (buffer.trim()) {\n            const processed = processPart(buffer.trim());\n            if (processed) {\n              yield processed;\n            }\n          }\n        }\n\n        return streamEvents();\n      };\n      return promise() as\n        | Promise<ResearchTask>\n        | Promise<AsyncGenerator<ResearchTaskEvent>>;\n    } else {\n      // Non-streaming: just fetch the task as before\n      return this.request<ResearchTask>(`/tasks/${id}`, \"GET\") as\n        | Promise<ResearchTask>\n        | Promise<AsyncGenerator<ResearchTaskEvent>>;\n    }\n  }\n\n  /**\n   * @deprecated This method is deprecated and may be removed in a future release.\n   * @see getTask(id, {stream: true})\n   * Poll a research task until completion or failure.\n   * Polls every 1 second with a maximum timeout of 10 minutes.\n   * Resilient to up to 10 consecutive polling failures.\n   */\n  async pollTask(id: string): Promise<ResearchTask> {\n    const pollingInterval = 1000; // 1 second\n    const maxPollingTime = 10 * 60 * 1000; // 10 minutes\n    const maxConsecutiveFailures = 10;\n    const startTime = Date.now();\n    let consecutiveFailures = 0;\n\n    while (true) {\n      try {\n        const task = await this.request<ResearchTask>(`/tasks/${id}`, \"GET\");\n        consecutiveFailures = 0; // Reset on success\n\n        if (task.status === \"completed\" || task.status === \"failed\") {\n          return task;\n        }\n      } catch (err) {\n        consecutiveFailures += 1;\n        if (consecutiveFailures >= maxConsecutiveFailures) {\n          throw new Error(\n            `Polling failed ${maxConsecutiveFailures} times in a row for task ${id}: ${err}`\n          );\n        }\n      }\n\n      // Check if we've exceeded the maximum polling time\n      if (Date.now() - startTime > maxPollingTime) {\n        throw new Error(\n          `Polling timeout: Task ${id} did not complete within 10 minutes`\n        );\n      }\n\n      // Wait before next poll\n      await new Promise((resolve) => setTimeout(resolve, pollingInterval));\n    }\n  }\n\n  /**\n   * List research tasks\n   * @param options Pagination options\n   * @returns The paginated list of research tasks\n   */\n  async listTasks(\n    options?: ListResearchTasksRequest\n  ): Promise<ListResearchTasksResponse> {\n    const params = this.buildPaginationParams(options);\n    return this.request<ListResearchTasksResponse>(\n      \"/tasks\",\n      \"GET\",\n      undefined,\n      params\n    );\n  }\n}\n"], "mappings": ";AAAA,OAAOA,UAAS,eAAe;;;ACGxB,IAAK,iBAAL,kBAAKC,oBAAL;AACL,EAAAA,gCAAA,gBAAa,OAAb;AACA,EAAAA,gCAAA,cAAW,OAAX;AACA,EAAAA,gCAAA,kBAAe,OAAf;AACA,EAAAA,gCAAA,eAAY,OAAZ;AACA,EAAAA,gCAAA,qBAAkB,OAAlB;AACA,EAAAA,gCAAA,oBAAiB,OAAjB;AACA,EAAAA,gCAAA,yBAAsB,OAAtB;AACA,EAAAA,gCAAA,wBAAqB,OAArB;AARU,SAAAA;AAAA,GAAA;AAcL,IAAM,WAAN,cAAuB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBlC,YACE,SACA,YACA,WACA,MACA;AACA,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,YAAY,cAAa,oBAAI,KAAK,GAAE,YAAY;AACrD,SAAK,OAAO;AAAA,EACd;AACF;;;ACdO,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,YAAY,QAAa;AACvB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAgB,QACd,UACA,SAAiB,QACjB,MACA,QACY;AACZ,WAAO,KAAK,OAAO,QAAW,WAAW,QAAQ,IAAI,QAAQ,MAAM,MAAM;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,sBAAsB,YAA4C;AAC1E,UAAM,SAAsB,CAAC;AAC7B,QAAI,CAAC,WAAY,QAAO;AAExB,QAAI,WAAW,OAAQ,QAAO,SAAS,WAAW;AAClD,QAAI,WAAW,MAAO,QAAO,QAAQ,WAAW;AAEhD,WAAO;AAAA,EACT;AACF;;;ACxEO,IAAM,0BAAN,cAAsC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7D,MAAM,OACJ,UACA,QAC2B;AAC3B,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ;AAAA,MACvB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,UAAkB,IAAuC;AACjE,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,gBAAgB,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,UAAkB,IAAuC;AACpE,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,gBAAgB,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,UAAkB,IAAuC;AACpE,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,gBAAgB,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACF;;;ACxCO,IAAM,eAAN,cAA2B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,YAAY,QAAa;AACvB,UAAM,MAAM;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAK,SAA0D;AACnE,UAAM,SAAS;AAAA,MACb,QAAQ,SAAS;AAAA,MACjB,OAAO,SAAS;AAAA,MAChB,OAAO,SAAS;AAAA,IAClB;AAEA,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,IAAI,IAA4B;AACpC,WAAO,KAAK,QAAe,cAAc,EAAE,IAAI,KAAK;AAAA,EACtD;AACF;;;AC4/FO,IAAK,mCAAL,kBAAKC,sCAAL;AACL,EAAAA,kCAAA,UAAO;AACP,EAAAA,kCAAA,UAAO;AACP,EAAAA,kCAAA,YAAS;AACT,EAAAA,kCAAA,aAAU;AACV,EAAAA,kCAAA,WAAQ;AACR,EAAAA,kCAAA,WAAQ;AANE,SAAAA;AAAA,GAAA;AAQL,IAAK,+BAAL,kBAAKC,kCAAL;AACL,EAAAA,8BAAA,SAAM;AADI,SAAAA;AAAA,GAAA;AAyBL,IAAK,qCAAL,kBAAKC,wCAAL;AACL,EAAAA,oCAAA,YAAS;AACT,EAAAA,oCAAA,YAAS;AAFC,SAAAA;AAAA,GAAA;AAIL,IAAK,4CAAL,kBAAKC,+CAAL;AACL,EAAAA,2CAAA,YAAS;AACT,EAAAA,2CAAA,YAAS;AAFC,SAAAA;AAAA,GAAA;AAIL,IAAK,4CAAL,kBAAKC,+CAAL;AACL,EAAAA,2CAAA,YAAS;AACT,EAAAA,2CAAA,YAAS;AAFC,SAAAA;AAAA,GAAA;AASL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,oBAAiB;AACjB,EAAAA,WAAA,oBAAiB;AACjB,EAAAA,WAAA,mBAAgB;AAChB,EAAAA,WAAA,iBAAc;AACd,EAAAA,WAAA,2BAAwB;AACxB,EAAAA,WAAA,4BAAyB;AACzB,EAAAA,WAAA,6BAA0B;AAC1B,EAAAA,WAAA,2BAAwB;AACxB,EAAAA,WAAA,oBAAiB;AACjB,EAAAA,WAAA,sBAAmB;AACnB,EAAAA,WAAA,uBAAoB;AACpB,EAAAA,WAAA,yBAAsB;AACtB,EAAAA,WAAA,0BAAuB;AACvB,EAAAA,WAAA,2BAAwB;AACxB,EAAAA,WAAA,6BAA0B;AAfhB,SAAAA;AAAA,GAAA;AAiBL,IAAK,qBAAL,kBAAKC,wBAAL;AACL,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,0BAAuB;AACvB,EAAAA,oBAAA,wBAAqB;AAHX,SAAAA;AAAA,GAAA;AAKL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,SAAM;AACN,EAAAA,cAAA,YAAS;AAFC,SAAAA;AAAA,GAAA;AAIL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,YAAS;AADC,SAAAA;AAAA,GAAA;AAGL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,gBAAa;AACb,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AAML,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,aAAU;AADA,SAAAA;AAAA,GAAA;AAGL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,aAAU;AACV,EAAAA,eAAA,cAAW;AAFD,SAAAA;AAAA,GAAA;AAQL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,iBAAc;AADJ,SAAAA;AAAA,GAAA;AAGL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,aAAU;AACV,EAAAA,kBAAA,aAAU;AACV,EAAAA,kBAAA,eAAY;AACZ,EAAAA,kBAAA,cAAW;AAJD,SAAAA;AAAA,GAAA;AAML,IAAK,iBAAL,kBAAKC,oBAAL;AACL,EAAAA,gBAAA,YAAS;AACT,EAAAA,gBAAA,aAAU;AAFA,SAAAA;AAAA,GAAA;AAIL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,cAAW;AAFD,SAAAA;AAAA,GAAA;AAIL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,cAAW;AAFD,SAAAA;AAAA,GAAA;AAqBL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,UAAO;AACP,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,YAAS;AAHC,SAAAA;AAAA,GAAA;AAKL,IAAK,yBAAL,kBAAKC,4BAAL;AACL,EAAAA,wBAAA,aAAU;AACV,EAAAA,wBAAA,cAAW;AACX,EAAAA,wBAAA,eAAY;AAHF,SAAAA;AAAA,GAAA;AAKL,IAAK,yBAAL,kBAAKC,4BAAL;AACL,EAAAA,wBAAA,UAAO;AACP,EAAAA,wBAAA,UAAO;AACP,EAAAA,wBAAA,YAAS;AACT,EAAAA,wBAAA,aAAU;AACV,EAAAA,wBAAA,WAAQ;AACR,EAAAA,wBAAA,WAAQ;AANE,SAAAA;AAAA,GAAA;AAQL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,YAAS;AACT,EAAAA,kBAAA,YAAS;AAFC,SAAAA;AAAA,GAAA;AAIL,IAAK,gCAAL,kBAAKC,mCAAL;AACL,EAAAA,+BAAA,SAAM;AACN,EAAAA,+BAAA,QAAK;AACL,EAAAA,+BAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AAcL,IAAK,qBAAL,kBAAKC,wBAAL;AACL,EAAAA,oBAAA,aAAU;AACV,EAAAA,oBAAA,aAAU;AACV,EAAAA,oBAAA,eAAY;AACZ,EAAAA,oBAAA,cAAW;AAJD,SAAAA;AAAA,GAAA;AAML,IAAK,uBAAL,kBAAKC,0BAAL;AACL,EAAAA,sBAAA,cAAW;AACX,EAAAA,sBAAA,YAAS;AAFC,SAAAA;AAAA,GAAA;AAIL,IAAK,6BAAL,kBAAKC,gCAAL;AACL,EAAAA,4BAAA,oBAAiB;AACjB,EAAAA,4BAAA,qBAAkB;AAFR,SAAAA;AAAA,GAAA;;;AC/qGL,IAAM,gBAAN,cAA4B,kBAAkB;AAAA,EAmBnD,MAAM,OACJ,QACA,KACwC;AACxC,QAAI,QAAQ,QAAW;AACrB,aAAO,KAAK;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI;AACJ,QAAI,OAAO,QAAQ,UAAU;AAC3B,kBAAY,OAAO,KAAK,KAAK,MAAM;AAAA,IACrC,WAAW,OAAO,SAAS,GAAG,GAAG;AAC/B,kBAAY;AAAA,IACd,OAAO;AACL,YAAM,IAAI;AAAA,QACR;AAAA;AAAA,MAEF;AAAA,IACF;AAEA,UAAM,cAAc,UAAU;AAC9B,UAAM,WAAW,KAAK,IAAI,GAAG,KAAK,KAAK,eAAe,OAAO,KAAK,CAAC;AAEnE,UAAM,UAAU,UAAU,SAAS,MAAM;AACzC,UAAM,QAAQ,QAAQ,MAAM,IAAI,EAAE,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,SAAS,CAAC;AACzE,UAAM,cAAc,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC;AAEhD,QAAI,WAAW,IAAI;AACjB,YAAM,IAAI;AAAA,QACR,uBAAuB,QAAQ;AAAA;AAAA,MAEjC;AAAA,IACF;AAEA,QAAI,gBAAgB,GAAG;AACrB,YAAM,IAAI;AAAA,QACR;AAAA;AAAA,MAEF;AAAA,IACF;AAEA,UAAM,eAAuC;AAAA,MAC3C,OAAO,OAAO;AAAA,MACd;AAAA,MACA,QAAQ,OAAO;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,OAAO;AAAA,MACjB,KAAM,OAAyC;AAAA,IACjD;AAEA,UAAM,iBAAiB,MAAM,KAAK;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI;AACF,YAAM,iBAAiB,MAAM,MAAM,eAAe,WAAW;AAAA,QAC3D,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAED,UAAI,CAAC,eAAe,IAAI;AACtB,cAAM,YAAY,MAAM,eAAe,KAAK;AAC5C,cAAM,IAAI;AAAA,UACR,kBAAkB,eAAe,MAAM,IAAI,eAAe,UAAU,KAAK,SAAS;AAAA;AAAA,QAEpF;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI;AAAA,QACR,8BAA+B,MAAgB,OAAO;AAAA;AAAA,MAExD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,IAAI,IAA6B;AACrC,WAAO,KAAK,QAAgB,eAAe,EAAE,IAAI,KAAK;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAK,SAA0D;AACnE,UAAM,SAAS,KAAK,sBAAsB,OAAO;AACjD,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,IAAY,QAAuC;AAC9D,WAAO,KAAK,QAAgB,eAAe,EAAE,IAAI,SAAS,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,IAA6B;AACxC,WAAO,KAAK,QAAgB,eAAe,EAAE,IAAI,QAAQ;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,mBACJ,IACA,SACiB;AACjB,UAAM,UAAU,SAAS,WAAW,KAAK,KAAK;AAC9C,UAAM,eAAe,SAAS,gBAAgB;AAC9C,UAAM,SAAS,SAAS;AAExB,UAAM,YAAY,KAAK,IAAI;AAE3B,WAAO,MAAM;AACX,YAAM,aAAa,MAAM,KAAK,IAAI,EAAE;AAEpC,UAAI,QAAQ;AACV,eAAO,WAAW,MAAM;AAAA,MAC1B;AAEA,UAAI,WAAW,wCAAmC;AAChD,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,kCAAgC;AAC7C,cAAM,IAAI;AAAA,UACR,UAAU,EAAE,YAAY,WAAW,iBAAiB,eAAe;AAAA;AAAA,QAErE;AAAA,MACF;AAEA,UAAI,KAAK,IAAI,IAAI,YAAY,SAAS;AACpC,cAAM,IAAI;AAAA,UACR,UAAU,EAAE,4BAA4B,OAAO,uBAAuB,WAAW,MAAM;AAAA;AAAA,QAEzF;AAAA,MACF;AAEA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,YAAY,CAAC;AAAA,IAClE;AAAA,EACF;AACF;;;ACxPO,IAAM,oBAAN,cAAgC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvD,KACE,UACA,QACiC;AACjC,UAAM,cAAc,KAAK,sBAAsB,MAAM;AACrD,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QACL,UACA,SAC4B;AAC5B,QAAI,SAA6B;AACjC,UAAM,cAAc,UAAU,EAAE,GAAG,QAAQ,IAAI,CAAC;AAEhD,WAAO,MAAM;AACX,kBAAY,SAAS;AACrB,YAAM,WAAW,MAAM,KAAK,KAAK,UAAU,WAAW;AAEtD,iBAAW,QAAQ,SAAS,MAAM;AAChC,cAAM;AAAA,MACR;AAEA,UAAI,CAAC,SAAS,WAAW,CAAC,SAAS,YAAY;AAC7C;AAAA,MACF;AAEA,eAAS,SAAS;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OACJ,UACA,SACuB;AACvB,UAAM,QAAsB,CAAC;AAC7B,qBAAiB,QAAQ,KAAK,QAAQ,UAAU,OAAO,GAAG;AACxD,YAAM,KAAK,IAAI;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,UAAkB,IAAiC;AAC3D,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,UAAU,EAAE;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,UAAkB,IAAiC;AAC9D,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,UAAU,EAAE;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;;;ACzEO,IAAM,0BAAN,cAAsC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7D,MAAM,KACJ,WACA,SACkC;AAClC,UAAM,SAAS,KAAK,sBAAsB,OAAO;AACjD,WAAO,KAAK;AAAA,MACV,gBAAgB,SAAS;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,WAAmB,OAAoC;AAC/D,WAAO,KAAK;AAAA,MACV,gBAAgB,SAAS,SAAS,KAAK;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACF;AAKO,IAAM,uBAAN,cAAmC,kBAAkB;AAAA,EAM1D,YAAY,QAAa;AACvB,UAAM,MAAM;AACZ,SAAK,OAAO,IAAI,wBAAwB,MAAM;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,QAAmD;AAC9D,WAAO,KAAK,QAAiB,gBAAgB,QAAQ,MAAM;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,IAAI,IAA8B;AACtC,WAAO,KAAK,QAAiB,gBAAgB,EAAE,IAAI,KAAK;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAK,SAA8D;AACvE,UAAM,SAAS;AAAA,MACb,QAAQ,SAAS;AAAA,MACjB,OAAO,SAAS;AAAA,MAChB,UAAU,SAAS;AAAA,IACrB;AACA,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,IAAY,QAAyC;AAChE,WAAO,KAAK,QAAiB,gBAAgB,EAAE,IAAI,SAAS,MAAM;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,IAA8B;AACzC,WAAO,KAAK,QAAiB,gBAAgB,EAAE,IAAI,QAAQ;AAAA,EAC7D;AACF;;;ACzHO,IAAM,uBAAN,cAAmC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1D,MAAM,OACJ,UACA,QACuB;AACvB,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ;AAAA,MACvB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,UAAkB,IAAmC;AAC7D,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,aAAa,EAAE;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,UAAkB,IAAmC;AAChE,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,aAAa,EAAE;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;;;ACpBO,IAAM,uBAAN,cAAmC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1D,MAAM,OAAO,QAAmD;AAC9D,WAAO,KAAK,QAAiB,gBAAgB,QAAQ,MAAM;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,IAAI,IAA8B;AACtC,WAAO,KAAK,QAAiB,gBAAgB,EAAE,IAAI,KAAK;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAK,SAA8D;AACvE,UAAM,SAAS,KAAK,sBAAsB,OAAO;AACjD,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,SAAwD;AACrE,QAAI,SAA6B;AACjC,UAAM,cAAc,UAAU,EAAE,GAAG,QAAQ,IAAI,CAAC;AAEhD,WAAO,MAAM;AACX,kBAAY,SAAS;AACrB,YAAM,WAAW,MAAM,KAAK,KAAK,WAAW;AAE5C,iBAAW,WAAW,SAAS,MAAM;AACnC,cAAM;AAAA,MACR;AAEA,UAAI,CAAC,SAAS,WAAW,CAAC,SAAS,YAAY;AAC7C;AAAA,MACF;AAEA,eAAS,SAAS;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,SAAmD;AAC9D,UAAM,WAAsB,CAAC;AAC7B,qBAAiB,WAAW,KAAK,QAAQ,OAAO,GAAG;AACjD,eAAS,KAAK,OAAO;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,IAAY,QAAmD;AAC1E,WAAO,KAAK,QAAiB,gBAAgB,EAAE,IAAI,SAAS,MAAM;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,IAA8B;AACzC,WAAO,KAAK,QAAiB,gBAAgB,EAAE,IAAI,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,aACJ,IACA,SACsC;AACtC,UAAM,SAAS;AAAA,MACb,QAAQ,SAAS;AAAA,MACjB,OAAO,SAAS;AAAA,MAChB,WAAW,SAAS;AAAA,IACtB;AAEA,WAAO,KAAK;AAAA,MACV,gBAAgB,EAAE;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,gBACL,IACA,SACgC;AAChC,QAAI,SAA6B;AACjC,UAAM,cAAc,UAAU,EAAE,GAAG,QAAQ,IAAI,CAAC;AAEhD,WAAO,MAAM;AACX,kBAAY,SAAS;AACrB,YAAM,WAAW,MAAM,KAAK,aAAa,IAAI,WAAW;AAExD,iBAAW,WAAW,SAAS,MAAM;AACnC,cAAM;AAAA,MACR;AAEA,UAAI,CAAC,SAAS,WAAW,CAAC,SAAS,YAAY;AAC7C;AAAA,MACF;AAEA,eAAS,SAAS;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,eACJ,IACA,SAC2B;AAC3B,UAAM,WAA6B,CAAC;AACpC,qBAAiB,WAAW,KAAK,gBAAgB,IAAI,OAAO,GAAG;AAC7D,eAAS,KAAK,OAAO;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACF;;;ACnKO,IAAM,gBAAN,cAA4B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAwCnD,YAAY,QAAa;AACvB,UAAM,MAAM;AACZ,SAAK,SAAS,IAAI,aAAa,MAAM;AACrC,SAAK,UAAU,IAAI,cAAc,MAAM;AACvC,SAAK,QAAQ,IAAI,kBAAkB,MAAM;AACzC,SAAK,WAAW,IAAI,qBAAqB,MAAM;AAC/C,SAAK,cAAc,IAAI,wBAAwB,MAAM;AACrD,SAAK,WAAW,IAAI,qBAAqB,MAAM;AAC/C,SAAK,WAAW,IAAI,qBAAqB,MAAM;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,QAAiD;AAC5D,WAAO,KAAK,QAAgB,eAAe,QAAQ,MAAM;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,IAAY,QAAqD;AACzE,UAAM,SAAsC,CAAC;AAC7C,QAAI,QAAQ;AACV,aAAO,SAAS;AAAA,IAClB;AAEA,WAAO,KAAK;AAAA,MACV,eAAe,EAAE;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAK,SAA4D;AACrE,UAAM,SAAS,KAAK,sBAAsB,OAAO;AACjD,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,SAAsD;AACnE,QAAI,SAA6B;AACjC,UAAM,cAAc,UAAU,EAAE,GAAG,QAAQ,IAAI,CAAC;AAEhD,WAAO,MAAM;AACX,kBAAY,SAAS;AACrB,YAAM,WAAW,MAAM,KAAK,KAAK,WAAW;AAE5C,iBAAW,UAAU,SAAS,MAAM;AAClC,cAAM;AAAA,MACR;AAEA,UAAI,CAAC,SAAS,WAAW,CAAC,SAAS,YAAY;AAC7C;AAAA,MACF;AAEA,eAAS,SAAS;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,SAAiD;AAC5D,UAAM,UAAoB,CAAC;AAC3B,qBAAiB,UAAU,KAAK,QAAQ,OAAO,GAAG;AAChD,cAAQ,KAAK,MAAM;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,IAAY,QAA8C;AACrE,WAAO,KAAK,QAAgB,eAAe,EAAE,IAAI,QAAQ,MAAM;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,IAA6B;AACxC,WAAO,KAAK,QAAgB,eAAe,EAAE,IAAI,QAAQ;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,IAA6B;AACxC,WAAO,KAAK,QAAgB,eAAe,EAAE,WAAW,MAAM;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,cACJ,IACA,SAOiB;AACjB,QAAI;AACJ,QAAI,eAAe;AACnB,QAAI;AAGJ,QAAI,OAAO,YAAY,UAAU;AAC/B,gBAAU;AAAA,IACZ,WAAW,SAAS;AAClB,gBAAU,QAAQ;AAClB,qBAAe,QAAQ,gBAAgB;AACvC,eAAS,QAAQ;AAAA,IACnB;AAEA,UAAM,YAAY,KAAK,IAAI;AAE3B,WAAO,MAAM;AACX,YAAM,SAAS,MAAM,KAAK,IAAI,EAAE;AAEhC,UAAI,QAAQ;AACV,eAAO,OAAO,MAAM;AAAA,MACtB;AAEA,UAAI,OAAO,8BAA8B;AACvC,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,KAAK,IAAI,IAAI,YAAY,SAAS;AAC/C,cAAM,IAAI;AAAA,UACR,UAAU,EAAE,oCAAoC,OAAO,uBAAuB,OAAO,MAAM;AAAA;AAAA,QAE7F;AAAA,MACF;AAEA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,YAAY,CAAC;AAAA,IAClE;AAAA,EACF;AACF;;;ACnPA,SAAS,eAA0B;AACnC,SAAS,mBAAmB,8BAA8B;AAEnD,SAAS,YAAY,KAAiC;AAC3D,SAAO,eAAe;AACxB;AAEO,SAAS,gBACd,QACyB;AACzB,SAAO,uBAAuB,QAAQ;AAAA,IACpC,cAAc;AAAA,EAChB,CAAC;AACH;;;ACCO,IAAM,qBAAN,MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9B,YAAY,QAAa;AACvB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAgB,QACd,UACA,SAAiB,QACjB,MACA,QACY;AAGZ,WAAO,KAAK,OAAO;AAAA,MACjB,eAAe,QAAQ;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAgB,WACd,UACA,SAAiB,QACjB,MACA,QACmB;AAGnB,WAAO,KAAK,OAAO;AAAA,MACjB,eAAe,QAAQ;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,sBACR,YACa;AACb,UAAM,SAAsB,CAAC;AAC7B,QAAI,CAAC,WAAY,QAAO;AAExB,QAAI,WAAW,OAAQ,QAAO,SAAS,WAAW;AAClD,QAAI,WAAW,MAAO,QAAO,QAAQ,WAAW;AAEhD,WAAO;AAAA,EACT;AACF;;;ACrEO,IAAM,iBAAN,cAA6B,mBAAmB;AAAA,EACrD,YAAY,QAAa;AACvB,UAAM,MAAM;AAAA,EACd;AAAA,EAyBA,MAAM,WAAc,QAIoB;AAEtC,UAAM,EAAE,cAAc,OAAO,OAAO,IAAI;AAGxC,QAAI,SAAS,QAAQ;AACrB,QAAI,UAAU,YAAY,MAAM,GAAG;AACjC,eAAS,gBAAgB,MAAM;AAAA,IACjC;AAEA,UAAM,UAAqC;AAAA,MACzC;AAAA,MACA,OAAO,SAAS;AAAA,MAChB,QAAQ,SACJ;AAAA,QACE;AAAA,QACA,aAAa,OAAO,eAAe;AAAA,MACrC,IACA,EAAE,aAAa,KAAK;AAAA,IAC1B;AAEA,WAAO,KAAK,QAAoC,UAAU,QAAQ,OAAO;AAAA,EAC3E;AAAA,EAkBA,QACE,IACA,SACoE;AACpE,QAAI,SAAS,QAAQ;AACnB,YAAM,UAAU,YAAY;AAC1B,cAAM,OAAO,MAAM,KAAK,WAAW,UAAU,EAAE,gBAAgB,KAAK;AACpE,YAAI,CAAC,KAAK,MAAM;AACd,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AACA,cAAM,SAAS,KAAK,KAAK,UAAU;AACnC,cAAM,UAAU,IAAI,YAAY;AAChC,YAAI,SAAS;AAEb,iBAAS,YAAY,MAAwC;AAC3D,gBAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,cAAI,OAAO,MAAM,MAAM,CAAC,EAAE,KAAK,IAAI;AACnC,cAAI,KAAK,WAAW,OAAO,GAAG;AAC5B,mBAAO,KAAK,MAAM,CAAC,EAAE,UAAU;AAAA,UACjC;AACA,cAAI;AACF,mBAAO,KAAK,MAAM,IAAI;AAAA,UACxB,SAAS,GAAG;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,wBAAgB,eAAe;AAC7B,iBAAO,MAAM;AACX,kBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,gBAAI,KAAM;AACV,sBAAU,QAAQ,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AAEhD,gBAAI,QAAQ,OAAO,MAAM,MAAM;AAC/B,qBAAS,MAAM,IAAI,KAAK;AAExB,uBAAW,QAAQ,OAAO;AACxB,oBAAM,YAAY,YAAY,IAAI;AAClC,kBAAI,WAAW;AACb,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,cAAI,OAAO,KAAK,GAAG;AACjB,kBAAM,YAAY,YAAY,OAAO,KAAK,CAAC;AAC3C,gBAAI,WAAW;AACb,oBAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,eAAO,aAAa;AAAA,MACtB;AACA,aAAO,QAAQ;AAAA,IAGjB,OAAO;AAEL,aAAO,KAAK,QAAsB,UAAU,EAAE,IAAI,KAAK;AAAA,IAGzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,SAAS,IAAmC;AAChD,UAAM,kBAAkB;AACxB,UAAM,iBAAiB,KAAK,KAAK;AACjC,UAAM,yBAAyB;AAC/B,UAAM,YAAY,KAAK,IAAI;AAC3B,QAAI,sBAAsB;AAE1B,WAAO,MAAM;AACX,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,QAAsB,UAAU,EAAE,IAAI,KAAK;AACnE,8BAAsB;AAEtB,YAAI,KAAK,WAAW,eAAe,KAAK,WAAW,UAAU;AAC3D,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,KAAK;AACZ,+BAAuB;AACvB,YAAI,uBAAuB,wBAAwB;AACjD,gBAAM,IAAI;AAAA,YACR,kBAAkB,sBAAsB,4BAA4B,EAAE,KAAK,GAAG;AAAA,UAChF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,KAAK,IAAI,IAAI,YAAY,gBAAgB;AAC3C,cAAM,IAAI;AAAA,UACR,yBAAyB,EAAE;AAAA,QAC7B;AAAA,MACF;AAGA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,eAAe,CAAC;AAAA,IACrE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,UACJ,SACoC;AACpC,UAAM,SAAS,KAAK,sBAAsB,OAAO;AACjD,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;Ad/MA,IAAM,YACJ,OAAO,WAAW,eAAe,OAAO,QAAQ,OAAO,QAAQC;AACjE,IAAM,cACJ,OAAO,WAAW,eAAe,OAAO,UAAU,OAAO,UAAU;AAuZ9D,IAAMC,OAAN,MAAU;AAAA;AAAA;AAAA;AAAA,EAiBP,uBACN,SAIA;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AAEJ,UAAM,kBAAmC,CAAC;AAG1C,QACE,SAAS,UACT,YAAY,UACZ,eAAe,UACf,WAAW,QACX;AACA,sBAAgB,OAAO;AAAA,IACzB;AAEA,QAAI,SAAS,OAAW,iBAAgB,OAAO;AAC/C,QAAI,YAAY,QAAW;AAEzB,UACE,OAAO,YAAY,YACnB,YAAY,QACZ,YAAY,WACZ,QAAQ,UACR,YAAY,QAAQ,MAAM,GAC1B;AACA,wBAAgB,UAAU;AAAA,UACxB,GAAG;AAAA,UACH,QAAQ,gBAAgB,QAAQ,MAAM;AAAA,QACxC;AAAA,MACF,OAAO;AACL,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,eAAe,OAAW,iBAAgB,aAAa;AAC3D,QAAI,aAAa,OAAW,iBAAgB,WAAW;AACvD,QAAI,kBAAkB;AACpB,sBAAgB,gBAAgB;AAClC,QAAI,WAAW,OAAW,iBAAgB,SAAS;AACnD,QAAI,cAAc,OAAW,iBAAgB,YAAY;AACzD,QAAI,qBAAqB;AACvB,sBAAgB,mBAAmB;AACrC,QAAI,YAAY,OAAW,iBAAgB,UAAU;AAErD,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,QAAiB,UAAkB,sBAAsB;AACnE,SAAK,UAAU;AACf,QAAI,CAAC,QAAQ;AACX,eAAS,QAAQ,IAAI;AACrB,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI;AAAA,UACR;AAAA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU,IAAI,YAAY;AAAA,MAC7B,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB,CAAC;AAGD,SAAK,UAAU,IAAI,cAAc,IAAI;AAErC,SAAK,WAAW,IAAI,eAAe,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,QACJ,UACA,QACA,MACA,QACY;AAEZ,QAAI,MAAM,KAAK,UAAU;AACzB,QAAI,UAAU,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG;AAC5C,YAAM,eAAe,IAAI,gBAAgB;AACzC,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,qBAAW,QAAQ,OAAO;AACxB,yBAAa,OAAO,KAAK,IAAI;AAAA,UAC/B;AAAA,QACF,WAAW,UAAU,QAAW;AAC9B,uBAAa,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,QACxC;AAAA,MACF;AACA,aAAO,IAAI,aAAa,SAAS,CAAC;AAAA,IACpC;AAEA,UAAM,WAAW,MAAM,UAAU,KAAK;AAAA,MACpC;AAAA,MACA,SAAS,KAAK;AAAA,MACd,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;AAAA,IACtC,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,YAAY,MAAM,SAAS,KAAK;AAEtC,UAAI,CAAC,UAAU,YAAY;AACzB,kBAAU,aAAa,SAAS;AAAA,MAClC;AACA,UAAI,CAAC,UAAU,WAAW;AACxB,kBAAU,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,MAC/C;AACA,UAAI,CAAC,UAAU,MAAM;AACnB,kBAAU,OAAO;AAAA,MACnB;AAGA,UAAI,UAAU,UAAU,SAAS;AACjC,UAAI,UAAU,SAAS;AACrB,oBAAY,QAAQ,SAAS,IAAI,OAAO,MAAM,UAAU;AAAA,MAC1D;AACA,YAAM,IAAI;AAAA,QACR;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAGA,UAAM,cAAc,SAAS,QAAQ,IAAI,cAAc,KAAK;AAC5D,QAAI,YAAY,SAAS,mBAAmB,GAAG;AAC7C,aAAQ,MAAM,KAAK,eAAkB,QAAQ;AAAA,IAC/C;AAEA,WAAQ,MAAM,SAAS,KAAK;AAAA,EAC9B;AAAA,EAEA,MAAM,WACJ,UACA,SAAiB,QACjB,MACA,aAImB;AACnB,QAAI,MAAM,KAAK,UAAU;AAEzB,QAAI,aAAa;AACf,YAAM,eAAe,IAAI,gBAAgB;AACzC,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAG;AACtD,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,qBAAW,QAAQ,OAAO;AACxB,yBAAa,OAAO,KAAK,OAAO,IAAI,CAAC;AAAA,UACvC;AAAA,QACF,WAAW,UAAU,QAAW;AAC9B,uBAAa,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,QACxC;AAAA,MACF;AACA,aAAO,IAAI,aAAa,SAAS,CAAC;AAAA,IACpC;AAEA,UAAM,WAAW,MAAM,UAAU,KAAK;AAAA,MACpC;AAAA,MACA,SAAS,KAAK;AAAA,MACd,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;AAAA,IACtC,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,OACJ,OACA,SAC6B;AAC7B,WAAO,MAAM,KAAK,QAAQ,WAAW,QAAQ,EAAE,OAAO,GAAG,QAAQ,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,kBACJ,OACA,SAC4B;AAC5B,UAAM,EAAE,iBAAiB,YAAY,IACnC,YAAY,SACR,EAAE,iBAAiB,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,EAAE,IACnD,KAAK,uBAAuB,OAAO;AAEzC,WAAO,MAAM,KAAK,QAAQ,WAAW,QAAQ;AAAA,MAC3C;AAAA,MACA,UAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,YACJ,KACA,SAC6B;AAC7B,WAAO,MAAM,KAAK,QAAQ,gBAAgB,QAAQ,EAAE,KAAK,GAAG,QAAQ,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,uBACJ,KACA,SAC4B;AAC5B,UAAM,EAAE,iBAAiB,YAAY,IACnC,YAAY,SACR,EAAE,iBAAiB,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,EAAE,IACnD,KAAK,uBAAuB,OAAO;AAEzC,WAAO,MAAM,KAAK,QAAQ,gBAAgB,QAAQ;AAAA,MAChD;AAAA,MACA,UAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,YACJ,MACA,SAC4B;AAC5B,QAAI,CAAC,QAAS,MAAM,QAAQ,IAAI,KAAK,KAAK,WAAW,GAAI;AACvD,YAAM,IAAI;AAAA,QACR;AAAA;AAAA,MAEF;AAAA,IACF;AAEA,QAAI;AAEJ,QAAI,OAAO,SAAS,UAAU;AAC5B,oBAAc,CAAC,IAAI;AAAA,IACrB,WAAW,OAAO,KAAK,CAAC,MAAM,UAAU;AACtC,oBAAc;AAAA,IAChB,OAAO;AACL,oBAAe,KAA2B,IAAI,CAAC,WAAW,OAAO,GAAG;AAAA,IACtE;AAEA,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN,GAAG;AAAA,IACL;AAEA,WAAO,MAAM,KAAK,QAAQ,aAAa,QAAQ,OAAO;AAAA,EACxD;AAAA,EAkCA,MAAM,OACJ,OACA,SACkD;AAClD,QAAI,SAAS,QAAQ;AACnB,YAAM,IAAI;AAAA,QACR;AAAA;AAAA,MAKF;AAAA,IACF;AAGA,QAAI,eAAe,SAAS;AAG5B,QAAI,gBAAgB,YAAY,YAAY,GAAG;AAC7C,qBAAe,gBAAgB,YAAY;AAAA,IAC7C;AAEA,UAAM,cAAc;AAAA,MAClB;AAAA,MACA,QAAQ;AAAA,MACR,MAAM,SAAS,QAAQ;AAAA,MACvB,OAAO,SAAS,SAAS;AAAA,MACzB,cAAc,SAAS;AAAA,MACvB;AAAA,IACF;AAEA,WAAO,MAAM,KAAK,QAAQ,WAAW,QAAQ,WAAW;AAAA,EAC1D;AAAA,EA6CA,OAAO,aACL,OACA,SAMmC;AAEnC,QAAI,eAAe,SAAS;AAC5B,QAAI,gBAAgB,YAAY,YAAY,GAAG;AAC7C,qBAAe,gBAAgB,YAAY;AAAA,IAC7C;AAGA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,MAAM,SAAS,QAAQ;AAAA,MACvB,QAAQ;AAAA,MACR,OAAO,SAAS,SAAS;AAAA,MACzB,cAAc,SAAS;AAAA,MACvB;AAAA,IACF;AAEA,UAAM,WAAW,MAAM,UAAU,KAAK,UAAU,WAAW;AAAA,MACzD,QAAQ;AAAA,MACR,SAAS,KAAK;AAAA,MACd,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,UAAU,MAAM,SAAS,KAAK;AACpC,YAAM,IAAI,SAAS,SAAS,SAAS,SAAQ,oBAAI,KAAK,GAAE,YAAY,CAAC;AAAA,IACvE;AAEA,UAAM,SAAS,SAAS,MAAM,UAAU;AACxC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,SACA,oBAAI,KAAK,GAAE,YAAY;AAAA,MACzB;AAAA,IACF;AAEA,UAAM,UAAU,IAAI,YAAY;AAChC,QAAI,SAAS;AAEb,QAAI;AACF,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI,KAAM;AAEV,kBAAU,QAAQ,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AAChD,cAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,iBAAS,MAAM,IAAI,KAAK;AAExB,mBAAW,QAAQ,OAAO;AACxB,cAAI,CAAC,KAAK,WAAW,QAAQ,EAAG;AAEhC,gBAAM,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE,KAAK;AACnD,cAAI,CAAC,WAAW,YAAY,UAAU;AACpC;AAAA,UACF;AAEA,cAAI;AACJ,cAAI;AACF,wBAAY,KAAK,MAAM,OAAO;AAAA,UAChC,SAAS,KAAK;AACZ;AAAA,UACF;AAEA,gBAAM,QAAQ,KAAK,aAAa,SAAS;AACzC,cAAI,MAAM,WAAW,MAAM,WAAW;AACpC,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,OAAO,WAAW,QAAQ,GAAG;AAC/B,cAAM,WAAW,OAAO,QAAQ,aAAa,EAAE,EAAE,KAAK;AACtD,YAAI,YAAY,aAAa,UAAU;AACrC,cAAI;AACF,kBAAM,YAAY,KAAK,MAAM,QAAQ;AACrC,kBAAM,QAAQ,KAAK,aAAa,SAAS;AACzC,gBAAI,MAAM,WAAW,MAAM,WAAW;AACpC,oBAAM;AAAA,YACR;AAAA,UACF,SAAS,GAAG;AAAA,UAAC;AAAA,QACf;AAAA,MACF;AAAA,IACF,UAAE;AACA,aAAO,YAAY;AAAA,IACrB;AAAA,EACF;AAAA,EAEQ,aAAa,WAAmC;AACtD,QAAI;AACJ,QAAI;AAWJ,QACE,UAAU,WACV,UAAU,QAAQ,CAAC,KACnB,UAAU,QAAQ,CAAC,EAAE,OACrB;AACA,gBAAU,UAAU,QAAQ,CAAC,EAAE,MAAM;AAAA,IACvC;AAEA,QAAI,UAAU,aAAa,UAAU,cAAc,QAAQ;AACzD,kBAAY,UAAU,UAAU,IAAI,CAAC,OAAY;AAAA,QAC/C,IAAI,EAAE;AAAA,QACN,KAAK,EAAE;AAAA,QACP,OAAO,EAAE;AAAA,QACT,eAAe,EAAE;AAAA,QACjB,QAAQ,EAAE;AAAA,QACV,MAAM,EAAE;AAAA,MACV,EAAE;AAAA,IACJ;AAEA,WAAO,EAAE,SAAS,UAAU;AAAA,EAC9B;AAAA,EAEA,MAAc,eAAkB,UAAgC;AAC9D,UAAM,SAAS,SAAS,MAAM,UAAU;AACxC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,SACA,oBAAI,KAAK,GAAE,YAAY;AAAA,MACzB;AAAA,IACF;AAEA,UAAM,UAAU,IAAI,YAAY;AAChC,QAAI,SAAS;AAEb,WAAO,IAAI,QAAW,OAAO,SAAS,WAAW;AAC/C,UAAI;AACF,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI,KAAM;AAEV,oBAAU,QAAQ,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AAChD,gBAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,mBAAS,MAAM,IAAI,KAAK;AAExB,qBAAW,QAAQ,OAAO;AACxB,gBAAI,CAAC,KAAK,WAAW,QAAQ,EAAG;AAEhC,kBAAM,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE,KAAK;AACnD,gBAAI,CAAC,WAAW,YAAY,UAAU;AACpC;AAAA,YACF;AAEA,gBAAI;AACJ,gBAAI;AACF,sBAAQ,KAAK,MAAM,OAAO;AAAA,YAC5B,QAAQ;AACN;AAAA,YACF;AAEA,oBAAQ,MAAM,KAAK;AAAA,cACjB,KAAK;AACH,uBAAO,YAAY;AACnB,wBAAQ,MAAM,IAAS;AACvB;AAAA,cACF,KAAK,SAAS;AACZ,sBAAM,UAAU,MAAM,OAAO,WAAW;AACxC,uBAAO,YAAY;AACnB;AAAA,kBACE,IAAI;AAAA,oBACF;AAAA;AAAA,qBAEA,oBAAI,KAAK,GAAE,YAAY;AAAA,kBACzB;AAAA,gBACF;AACA;AAAA,cACF;AAAA;AAAA,cAEA;AACE;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AAGA;AAAA,UACE,IAAI;AAAA,YACF;AAAA;AAAA,aAEA,oBAAI,KAAK,GAAE,YAAY;AAAA,UACzB;AAAA,QACF;AAAA,MACF,SAAS,KAAK;AACZ,eAAO,GAAY;AAAA,MACrB,UAAE;AACA,YAAI;AACF,iBAAO,YAAY;AAAA,QACrB,QAAQ;AAAA,QAER;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQA,IAAO,gBAAQA;", "names": ["fetch", "HttpStatusCode", "CreateEnrichmentParametersFormat", "CreateImportParametersFormat", "CreateWebsetParametersImportSource", "CreateWebsetParametersSearchExcludeSource", "CreateWebsetSearchParametersExcludeSource", "EventType", "ImportFailedReason", "ImportFormat", "ImportObject", "ImportStatus", "MonitorObject", "MonitorStatus", "MonitorRunObject", "MonitorRunStatus", "MonitorRunType", "UpdateMonitorStatus", "WebhookStatus", "WebsetStatus", "WebsetEnrichmentStatus", "WebsetEnrichmentFormat", "WebsetItemSource", "WebsetItemEvaluationSatisfied", "WebsetSearchStatus", "WebsetSearchBehavior", "WebsetSearchCanceledReason", "fetch", "Exa"]}