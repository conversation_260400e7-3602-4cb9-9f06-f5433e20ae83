{"version": 3, "sources": ["../index.ts", "../../core/test/not-implemented.ts", "../../core/test/mock-embedding-model-v1.ts", "../../core/test/mock-language-model-v1.ts", "../../core/test/mock-values.ts", "../../core/util/simulate-readable-stream.ts"], "sourcesContent": ["export {\n  convertArrayToReadableStream,\n  mockId,\n} from '@ai-sdk/provider-utils/test';\nexport { MockEmbeddingModelV1 } from '../core/test/mock-embedding-model-v1';\nexport { MockLanguageModelV1 } from '../core/test/mock-language-model-v1';\nexport { mockValues } from '../core/test/mock-values';\n\nimport { simulateReadableStream as originalSimulateReadableStream } from '../core/util/simulate-readable-stream';\n\n/**\n * @deprecated Use `simulateReadableStream` from `ai` instead.\n */\nexport const simulateReadableStream = originalSimulateReadableStream;\n", "export function notImplemented(): never {\n  throw new Error('Not implemented');\n}\n", "import { EmbeddingModelV1 } from '@ai-sdk/provider';\nimport { Embedding } from '../types';\nimport { EmbeddingModelUsage } from '../types/usage';\nimport { notImplemented } from './not-implemented';\n\nexport class MockEmbeddingModelV1<VALUE> implements EmbeddingModelV1<VALUE> {\n  readonly specificationVersion = 'v1';\n\n  readonly provider: EmbeddingModelV1<VALUE>['provider'];\n  readonly modelId: EmbeddingModelV1<VALUE>['modelId'];\n  readonly maxEmbeddingsPerCall: EmbeddingModelV1<VALUE>['maxEmbeddingsPerCall'];\n  readonly supportsParallelCalls: EmbeddingModelV1<VALUE>['supportsParallelCalls'];\n\n  doEmbed: EmbeddingModelV1<VALUE>['doEmbed'];\n\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    maxEmbeddingsPerCall = 1,\n    supportsParallelCalls = false,\n    doEmbed = notImplemented,\n  }: {\n    provider?: EmbeddingModelV1<VALUE>['provider'];\n    modelId?: EmbeddingModelV1<VALUE>['modelId'];\n    maxEmbeddingsPerCall?:\n      | EmbeddingModelV1<VALUE>['maxEmbeddingsPerCall']\n      | null;\n    supportsParallelCalls?: EmbeddingModelV1<VALUE>['supportsParallelCalls'];\n    doEmbed?: EmbeddingModelV1<VALUE>['doEmbed'];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.maxEmbeddingsPerCall = maxEmbeddingsPerCall ?? undefined;\n    this.supportsParallelCalls = supportsParallelCalls;\n    this.doEmbed = doEmbed;\n  }\n}\n\nexport function mockEmbed<VALUE>(\n  expectedValues: Array<VALUE>,\n  embeddings: Array<Embedding>,\n  usage?: EmbeddingModelUsage,\n): EmbeddingModelV1<VALUE>['doEmbed'] {\n  return async ({ values }) => {\n    assert.deepStrictEqual(expectedValues, values);\n    return { embeddings, usage };\n  };\n}\n", "import { LanguageModelV1 } from '@ai-sdk/provider';\nimport { notImplemented } from './not-implemented';\n\nexport class MockLanguageModelV1 implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  readonly provider: LanguageModelV1['provider'];\n  readonly modelId: LanguageModelV1['modelId'];\n\n  supportsUrl: LanguageModelV1['supportsUrl'];\n  doGenerate: LanguageModelV1['doGenerate'];\n  doStream: LanguageModelV1['doStream'];\n\n  readonly defaultObjectGenerationMode: LanguageModelV1['defaultObjectGenerationMode'];\n  readonly supportsStructuredOutputs: LanguageModelV1['supportsStructuredOutputs'];\n  constructor({\n    provider = 'mock-provider',\n    modelId = 'mock-model-id',\n    supportsUrl = undefined,\n    doGenerate = notImplemented,\n    doStream = notImplemented,\n    defaultObjectGenerationMode = undefined,\n    supportsStructuredOutputs = undefined,\n  }: {\n    provider?: LanguageModelV1['provider'];\n    modelId?: LanguageModelV1['modelId'];\n    supportsUrl?: LanguageModelV1['supportsUrl'];\n    doGenerate?: LanguageModelV1['doGenerate'];\n    doStream?: LanguageModelV1['doStream'];\n    defaultObjectGenerationMode?: LanguageModelV1['defaultObjectGenerationMode'];\n    supportsStructuredOutputs?: LanguageModelV1['supportsStructuredOutputs'];\n  } = {}) {\n    this.provider = provider;\n    this.modelId = modelId;\n    this.doGenerate = doGenerate;\n    this.doStream = doStream;\n    this.supportsUrl = supportsUrl;\n\n    this.defaultObjectGenerationMode = defaultObjectGenerationMode;\n    this.supportsStructuredOutputs = supportsStructuredOutputs;\n  }\n}\n", "export function mockValues<T>(...values: T[]): () => T {\n  let counter = 0;\n  return () => values[counter++] ?? values[values.length - 1];\n}\n", "import { delay as delayFunction } from '@ai-sdk/provider-utils';\n\n/**\n * Creates a ReadableStream that emits the provided values with an optional delay between each value.\n *\n * @param options - The configuration options\n * @param options.chunks - Array of values to be emitted by the stream\n * @param options.initialDelayInMs - Optional initial delay in milliseconds before emitting the first value (default: 0). Can be set to `null` to skip the initial delay. The difference between `initialDelayInMs: null` and `initialDelayInMs: 0` is that `initialDelayInMs: null` will emit the values without any delay, while `initialDelayInMs: 0` will emit the values with a delay of 0 milliseconds.\n * @param options.chunkDelayInMs - Optional delay in milliseconds between emitting each value (default: 0). Can be set to `null` to skip the delay. The difference between `chunkDelayInMs: null` and `chunkDelayInMs: 0` is that `chunkDelayInMs: null` will emit the values without any delay, while `chunkDelayInMs: 0` will emit the values with a delay of 0 milliseconds.\n * @returns A ReadableStream that emits the provided values\n */\nexport function simulateReadableStream<T>({\n  chunks,\n  initialDelayInMs = 0,\n  chunkDelayInMs = 0,\n  _internal,\n}: {\n  chunks: T[];\n  initialDelayInMs?: number | null;\n  chunkDelayInMs?: number | null;\n  _internal?: {\n    delay?: (ms: number | null) => Promise<void>;\n  };\n}): ReadableStream<T> {\n  const delay = _internal?.delay ?? delayFunction;\n\n  let index = 0;\n\n  return new ReadableStream({\n    async pull(controller) {\n      if (index < chunks.length) {\n        await delay(index === 0 ? initialDelayInMs : chunkDelayInMs);\n        controller.enqueue(chunks[index++]);\n      } else {\n        controller.close();\n      }\n    },\n  });\n}\n"], "mappings": ";AAAA;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACHA,SAAS,iBAAwB;AACtC,QAAM,IAAI,MAAM,iBAAiB;AACnC;;;ACGO,IAAM,uBAAN,MAAqE;AAAA,EAU1E,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,UAAU;AAAA,EACZ,IAQI,CAAC,GAAG;AAvBR,SAAS,uBAAuB;AAwB9B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,uBAAuB,sDAAwB;AACpD,SAAK,wBAAwB;AAC7B,SAAK,UAAU;AAAA,EACjB;AACF;;;ACjCO,IAAM,sBAAN,MAAqD;AAAA,EAY1D,YAAY;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,8BAA8B;AAAA,IAC9B,4BAA4B;AAAA,EAC9B,IAQI,CAAC,GAAG;AA3BR,SAAS,uBAAuB;AA4B9B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,cAAc;AAEnB,SAAK,8BAA8B;AACnC,SAAK,4BAA4B;AAAA,EACnC;AACF;;;ACzCO,SAAS,cAAiB,QAAsB;AACrD,MAAI,UAAU;AACd,SAAO,MAAG;AAFZ;AAEe,wBAAO,SAAS,MAAhB,YAAqB,OAAO,OAAO,SAAS,CAAC;AAAA;AAC5D;;;ACHA,SAAS,SAAS,qBAAqB;AAWhC,SAAS,uBAA0B;AAAA,EACxC;AAAA,EACA,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB;AACF,GAOsB;AAvBtB;AAwBE,QAAM,SAAQ,4CAAW,UAAX,YAAoB;AAElC,MAAI,QAAQ;AAEZ,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,KAAK,YAAY;AACrB,UAAI,QAAQ,OAAO,QAAQ;AACzB,cAAM,MAAM,UAAU,IAAI,mBAAmB,cAAc;AAC3D,mBAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,MACpC,OAAO;AACL,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ALzBO,IAAMA,0BAAyB;", "names": ["simulateReadableStream"]}