{"version": 3, "file": "SynchronizedRef.js", "names": ["circular", "ref", "internal", "SynchronizedRefTypeId", "SynchronizedTypeId", "make", "makeSynchronized", "get", "getAndSet", "getAndUpdate", "getAndUpdateEffect", "getAndUpdateSome", "getAndUpdateSomeEffect", "modify", "modifyEffect", "modifySome", "modifySomeEffect", "set", "setAndGet", "update", "updateEffect", "updateAndGet", "updateAndGetEffect", "updateSome", "updateSomeEffect", "updateSomeAndGet", "updateSomeAndGetEffect", "updateSomeAndGetEffectSynchronized", "unsafeMake", "unsafeMakeSynchronized"], "sources": ["../../src/SynchronizedRef.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,QAAQ,MAAM,+BAA+B;AACzD,OAAO,KAAKC,GAAG,MAAM,mBAAmB;AACxC,OAAO,KAAKC,QAAQ,MAAM,+BAA+B;AAMzD;;;;AAIA,OAAO,MAAMC,qBAAqB,GAAkBH,QAAQ,CAACI,kBAAkB;AAkD/E;;;;AAIA,OAAO,MAAMC,IAAI,GAAuDL,QAAQ,CAACM,gBAAgB;AAEjG;;;;AAIA,OAAO,MAAMC,GAAG,GAAsDN,GAAG,CAACM,GAAG;AAE7E;;;;AAIA,OAAO,MAAMC,SAAS,GAWlBP,GAAG,CAACO,SAAS;AAEjB;;;;AAIA,OAAO,MAAMC,YAAY,GAWrBR,GAAG,CAACQ,YAAY;AAEpB;;;;AAIA,OAAO,MAAMC,kBAAkB,GAW3BR,QAAQ,CAACQ,kBAAkB;AAE/B;;;;AAIA,OAAO,MAAMC,gBAAgB,GAWzBV,GAAG,CAACU,gBAAgB;AAExB;;;;AAIA,OAAO,MAAMC,sBAAsB,GAc/BV,QAAQ,CAACU,sBAAsB;AAEnC;;;;AAIA,OAAO,MAAMC,MAAM,GAWfX,QAAQ,CAACW,MAAM;AAEnB;;;;AAIA,OAAO,MAAMC,YAAY,GAcrBZ,QAAQ,CAACY,YAAY;AAEzB;;;;AAIA,OAAO,MAAMC,UAAU,GAenBd,GAAG,CAACc,UAAU;AAElB;;;;AAIA,OAAO,MAAMC,gBAAgB,GAkBzBd,QAAQ,CAACc,gBAAgB;AAE7B;;;;AAIA,OAAO,MAAMC,GAAG,GAWZhB,GAAG,CAACgB,GAAG;AAEX;;;;AAIA,OAAO,MAAMC,SAAS,GAWlBjB,GAAG,CAACiB,SAAS;AAEjB;;;;AAIA,OAAO,MAAMC,MAAM,GAWflB,GAAG,CAACkB,MAAM;AAEd;;;;AAIA,OAAO,MAAMC,YAAY,GAWrBlB,QAAQ,CAACkB,YAAY;AAEzB;;;;AAIA,OAAO,MAAMC,YAAY,GAWrBpB,GAAG,CAACoB,YAAY;AAEpB;;;;AAIA,OAAO,MAAMC,kBAAkB,GAW3BpB,QAAQ,CAACoB,kBAAkB;AAE/B;;;;AAIA,OAAO,MAAMC,UAAU,GAWnBtB,GAAG,CAACsB,UAAU;AAElB;;;;AAIA,OAAO,MAAMC,gBAAgB,GAczBtB,QAAQ,CAACsB,gBAAgB;AAE7B;;;;AAIA,OAAO,MAAMC,gBAAgB,GAWzBxB,GAAG,CAACwB,gBAAgB;AAExB;;;;AAIA,OAAO,MAAMC,sBAAsB,GAc/B1B,QAAQ,CAAC2B,kCAAkC;AAE/C;;;;AAIA,OAAO,MAAMC,UAAU,GAAwC5B,QAAQ,CAAC6B,sBAAsB", "ignoreList": []}