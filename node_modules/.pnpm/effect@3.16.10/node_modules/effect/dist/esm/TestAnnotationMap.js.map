{"version": 3, "file": "TestAnnotationMap.js", "names": ["dual", "HashMap", "hasProperty", "TestAnnotationMapTypeId", "Symbol", "for", "TestAnnotationMapImpl", "map", "constructor", "isTestAnnotationMap", "u", "empty", "make", "overwrite", "self", "key", "value", "set", "update", "f", "initial", "has", "unsafeGet", "get", "annotate", "_", "combine", "that", "result", "entry"], "sources": ["../../src/TestAnnotationMap.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,SAASA,IAAI,QAAQ,eAAe;AACpC,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,WAAW,QAAQ,gBAAgB;AAG5C;;;AAGA,OAAO,MAAMC,uBAAuB,gBAAkBC,MAAM,CAACC,GAAG,CAAC,0BAA0B,CAAC;AAkB5F;AACA,MAAMC,qBAAqB;EAEJC,GAAA;EADZ,CAACJ,uBAAuB,IAA6BA,uBAAuB;EACrFK,YAAqBD,GAA6D;IAA7D,KAAAA,GAAG,GAAHA,GAAG;EACxB;;AAGF;;;AAGA,OAAO,MAAME,mBAAmB,GAAIC,CAAU,IAA6BR,WAAW,CAACQ,CAAC,EAAEP,uBAAuB,CAAC;AAElH;;;AAGA,OAAO,MAAMQ,KAAK,GAAmCA,CAAA,KAAM,IAAIL,qBAAqB,CAACL,OAAO,CAACU,KAAK,EAAE,CAAC;AAErG;;;AAGA,OAAO,MAAMC,IAAI,GAAIL,GAA6D,IAAuB;EACvG,OAAO,IAAID,qBAAqB,CAACC,GAAG,CAAC;AACvC,CAAC;AAED;;;AAGA,OAAO,MAAMM,SAAS,gBAAGb,IAAI,CAS3B,CAAC,EAAE,CAACc,IAAI,EAAEC,GAAG,EAAEC,KAAK,KAAKJ,IAAI,CAACX,OAAO,CAACgB,GAAG,CAACH,IAAI,CAACP,GAAG,EAAEQ,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC;AAEnE;;;AAGA,OAAO,MAAME,MAAM,gBAAGlB,IAAI,CAaxB,CAAC,EAAE,CAAIc,IAAuB,EAAEC,GAAqC,EAAEI,CAAkB,KAAI;EAC7F,IAAIH,KAAK,GAAGD,GAAG,CAACK,OAAO;EACvB,IAAInB,OAAO,CAACoB,GAAG,CAACP,IAAI,CAACP,GAAG,EAAEQ,GAAG,CAAC,EAAE;IAC9BC,KAAK,GAAGf,OAAO,CAACqB,SAAS,CAACR,IAAI,CAACP,GAAG,EAAEQ,GAAG,CAAM;EAC/C;EACA,OAAOF,SAAS,CAACC,IAAI,EAAEC,GAAG,EAAEI,CAAC,CAACH,KAAK,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF;;;;;;AAMA,OAAO,MAAMO,GAAG,gBAAGvB,IAAI,CAerB,CAAC,EAAE,CAAIc,IAAuB,EAAEC,GAAqC,KAAI;EACzE,IAAId,OAAO,CAACoB,GAAG,CAACP,IAAI,CAACP,GAAG,EAAEQ,GAAG,CAAC,EAAE;IAC9B,OAAOd,OAAO,CAACqB,SAAS,CAACR,IAAI,CAACP,GAAG,EAAEQ,GAAG,CAAM;EAC9C;EACA,OAAOA,GAAG,CAACK,OAAO;AACpB,CAAC,CAAC;AAEF;;;;;AAKA,OAAO,MAAMI,QAAQ,gBAAGxB,IAAI,CAa1B,CAAC,EAAE,CAACc,IAAI,EAAEC,GAAG,EAAEC,KAAK,KAAKE,MAAM,CAACJ,IAAI,EAAEC,GAAG,EAAGU,CAAC,IAAKV,GAAG,CAACW,OAAO,CAACD,CAAC,EAAET,KAAK,CAAC,CAAC,CAAC;AAE3E;;;AAGA,OAAO,MAAMU,OAAO,gBAAG1B,IAAI,CASzB,CAAC,EAAE,CAACc,IAAI,EAAEa,IAAI,KAAI;EAClB,IAAIC,MAAM,GAAGd,IAAI,CAACP,GAAG;EACrB,KAAK,MAAMsB,KAAK,IAAIF,IAAI,CAACpB,GAAG,EAAE;IAC5B,IAAIN,OAAO,CAACoB,GAAG,CAACO,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMb,KAAK,GAAGf,OAAO,CAACsB,GAAG,CAACK,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAE;MAC5CD,MAAM,GAAG3B,OAAO,CAACgB,GAAG,CAACW,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAACH,OAAO,CAACV,KAAK,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLD,MAAM,GAAG3B,OAAO,CAACgB,GAAG,CAACW,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAClD;EACF;EACA,OAAOjB,IAAI,CAACgB,MAAM,CAAC;AACrB,CAAC,CAAC", "ignoreList": []}