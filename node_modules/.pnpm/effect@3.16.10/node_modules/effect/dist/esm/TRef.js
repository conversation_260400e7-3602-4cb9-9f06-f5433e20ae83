/**
 * @since 2.0.0
 */
import * as internal from "./internal/stm/tRef.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const TRefTypeId = internal.TRefTypeId;
/**
 * @since 2.0.0
 * @category mutations
 */
export const get = internal.get;
/**
 * @since 2.0.0
 * @category mutations
 */
export const getAndSet = internal.getAndSet;
/**
 * @since 2.0.0
 * @category mutations
 */
export const getAndUpdate = internal.getAndUpdate;
/**
 * @since 2.0.0
 * @category mutations
 */
export const getAndUpdateSome = internal.getAndUpdateSome;
/**
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * @since 2.0.0
 * @category mutations
 */
export const modify = internal.modify;
/**
 * @since 2.0.0
 * @category mutations
 */
export const modifySome = internal.modifySome;
/**
 * @since 2.0.0
 * @category mutations
 */
export const set = internal.set;
/**
 * @since 2.0.0
 * @category mutations
 */
export const setAndGet = internal.setAndGet;
/**
 * @since 2.0.0
 * @category mutations
 */
export const update = internal.update;
/**
 * @since 2.0.0
 * @category mutations
 */
export const updateAndGet = internal.updateAndGet;
/**
 * @since 2.0.0
 * @category mutations
 */
export const updateSome = internal.updateSome;
/**
 * @since 2.0.0
 * @category mutations
 */
export const updateSomeAndGet = internal.updateSomeAndGet;
//# sourceMappingURL=TRef.js.map