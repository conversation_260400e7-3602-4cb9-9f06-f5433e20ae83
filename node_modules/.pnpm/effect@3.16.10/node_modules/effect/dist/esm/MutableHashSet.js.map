{"version": 3, "file": "MutableHashSet.js", "names": ["Dual", "format", "NodeInspectSymbol", "toJSON", "MutableHashMap", "pipeArguments", "TypeId", "Symbol", "for", "MutableHashSetProto", "iterator", "Array", "from", "keyMap", "map", "_", "toString", "_id", "values", "pipe", "arguments", "fromHashMap", "set", "Object", "create", "empty", "fromIterable", "keys", "k", "make", "add", "dual", "self", "key", "has", "remove", "size", "clear"], "sources": ["../../src/MutableHashSet.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FA,OAAO,KAAKA,IAAI,MAAM,eAAe;AACrC,SAASC,MAAM,EAAoBC,iBAAiB,EAAEC,MAAM,QAAQ,kBAAkB;AACtF,OAAO,KAAKC,cAAc,MAAM,qBAAqB;AAErD,SAASC,aAAa,QAAQ,eAAe;AAE7C,MAAMC,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,uBAAuB,CAAW;AAmB3E,MAAMC,mBAAmB,GAA4C;EACnE,CAACH,MAAM,GAAGA,MAAM;EAChB,CAACC,MAAM,CAACG,QAAQ,IAAC;IACf,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,MAAM,CAAC,CAC3BC,GAAG,CAAC,CAAC,CAACC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAACR,MAAM,CAACG,QAAQ,CAAC,EAAE;EACvC,CAAC;EACDM,QAAQA,CAAA;IACN,OAAOf,MAAM,CAAC,IAAI,CAACE,MAAM,EAAE,CAAC;EAC9B,CAAC;EACDA,MAAMA,CAAA;IACJ,OAAO;MACLc,GAAG,EAAE,gBAAgB;MACrBC,MAAM,EAAEP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,CAACE,GAAG,CAACX,MAAM;KACpC;EACH,CAAC;EACD,CAACD,iBAAiB,IAAC;IACjB,OAAO,IAAI,CAACC,MAAM,EAAE;EACtB,CAAC;EACDgB,IAAIA,CAAA;IACF,OAAOd,aAAa,CAAC,IAAI,EAAEe,SAAS,CAAC;EACvC;CACD;AAED,MAAMC,WAAW,GACfR,MAAiD,IAC5B;EACrB,MAAMS,GAAG,GAAGC,MAAM,CAACC,MAAM,CAACf,mBAAmB,CAAC;EAC9Ca,GAAG,CAACT,MAAM,GAAGA,MAAM;EACnB,OAAOS,GAAG;AACZ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAO,MAAMG,KAAK,GAAGA,CAAA,KAAoCJ,WAAW,CAACjB,cAAc,CAACqB,KAAK,EAAE,CAAC;AAE5F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA,OAAO,MAAMC,YAAY,GAAeC,IAAiB,IACvDN,WAAW,CACTjB,cAAc,CAACsB,YAAY,CAACf,KAAK,CAACC,IAAI,CAACe,IAAI,CAAC,CAACb,GAAG,CAAEc,CAAC,IAAK,CAACA,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CACpE;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA,OAAO,MAAMC,IAAI,GAAGA,CAClB,GAAGF,IAAU,KACoBD,YAAY,CAACC,IAAI,CAAC;AAErD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,OAAO,MAAMG,GAAG,gBA2DZ9B,IAAI,CAAC+B,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEC,GAAG,MAAM7B,cAAc,CAACkB,GAAG,CAACU,IAAI,CAACnB,MAAM,EAAEoB,GAAG,EAAE,IAAI,CAAC,EAAED,IAAI,CAAC,CAAC;AAEvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAO,MAAME,GAAG,gBAkCZlC,IAAI,CAAC+B,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAK7B,cAAc,CAAC8B,GAAG,CAACF,IAAI,CAACnB,MAAM,EAAEoB,GAAG,CAAC,CAAC;AAEzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,OAAO,MAAME,MAAM,gBAuDfnC,IAAI,CAAC+B,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEC,GAAG,MAAM7B,cAAc,CAAC+B,MAAM,CAACH,IAAI,CAACnB,MAAM,EAAEoB,GAAG,CAAC,EAAED,IAAI,CAAC,CAAC;AAEpE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAO,MAAMI,IAAI,GAAOJ,IAAuB,IAAa5B,cAAc,CAACgC,IAAI,CAACJ,IAAI,CAACnB,MAAM,CAAC;AAE5F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,OAAO,MAAMwB,KAAK,GAAOL,IAAuB,KAC9C5B,cAAc,CAACiC,KAAK,CAACL,IAAI,CAACnB,MAAM,CAAC,EAAEmB,IAAI,CACxC", "ignoreList": []}