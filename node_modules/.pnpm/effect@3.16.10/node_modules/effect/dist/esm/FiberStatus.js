import * as internal from "./internal/fiberStatus.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const FiberStatusTypeId = internal.FiberStatusTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const done = internal.done;
/**
 * @since 2.0.0
 * @category constructors
 */
export const running = internal.running;
/**
 * @since 2.0.0
 * @category constructors
 */
export const suspended = internal.suspended;
/**
 * Returns `true` if the specified value is a `FiberStatus`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isFiberStatus = internal.isFiberStatus;
/**
 * Returns `true` if the specified `FiberStatus` is `Done`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isDone = internal.isDone;
/**
 * Returns `true` if the specified `FiberStatus` is `Running`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isRunning = internal.isRunning;
/**
 * Returns `true` if the specified `FiberStatus` is `Suspended`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isSuspended = internal.isSuspended;
//# sourceMappingURL=FiberStatus.js.map