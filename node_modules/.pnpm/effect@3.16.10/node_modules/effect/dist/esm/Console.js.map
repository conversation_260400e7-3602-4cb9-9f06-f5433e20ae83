{"version": 3, "file": "Console.js", "names": ["internal", "defaultConsole", "TypeId", "<PERSON><PERSON><PERSON>", "consoleTag", "with<PERSON><PERSON>ole", "setConsole", "consoleWith", "assert", "clear", "count", "<PERSON><PERSON><PERSON><PERSON>", "debug", "dir", "dirxml", "error", "group", "info", "log", "table", "time", "timeLog", "trace", "warn", "withGroup", "withTime"], "sources": ["../../src/Console.ts"], "sourcesContent": [null], "mappings": "AAKA,OAAO,KAAKA,QAAQ,MAAM,uBAAuB;AACjD,OAAO,KAAKC,cAAc,MAAM,uCAAuC;AAIvE;;;;AAIA,OAAO,MAAMC,MAAM,GAAkBD,cAAc,CAACC,MAAM;AAgE1D;;;;AAIA,OAAO,MAAMC,OAAO,GAAkCF,cAAc,CAACG,UAAU;AAE/E;;;;AAIA,OAAO,MAAMC,WAAW,GAWpBL,QAAQ,CAACK,WAAW;AAExB;;;;AAIA,OAAO,MAAMC,UAAU,GAA0DN,QAAQ,CAACM,UAAU;AAEpG;;;;AAIA,OAAO,MAAMC,WAAW,GAA2EP,QAAQ,CAACO,WAAW;AAEvH;;;;AAIA,OAAO,MAAMC,MAAM,GAAsER,QAAQ,CAACQ,MAAM;AAExG;;;;AAIA,OAAO,MAAMC,KAAK,GAAiBT,QAAQ,CAACS,KAAK;AAEjD;;;;AAIA,OAAO,MAAMC,KAAK,GAAqCV,QAAQ,CAACU,KAAK;AAErE;;;;AAIA,OAAO,MAAMC,UAAU,GAAqCX,QAAQ,CAACW,UAAU;AAE/E;;;;AAIA,OAAO,MAAMC,KAAK,GAAkDZ,QAAQ,CAACY,KAAK;AAElF;;;;AAIA,OAAO,MAAMC,GAAG,GAA+Cb,QAAQ,CAACa,GAAG;AAE3E;;;;AAIA,OAAO,MAAMC,MAAM,GAAkDd,QAAQ,CAACc,MAAM;AAEpF;;;;AAIA,OAAO,MAAMC,KAAK,GAAkDf,QAAQ,CAACe,KAAK;AAElF;;;;AAIA,OAAO,MAAMC,KAAK,GAEgBhB,QAAQ,CAACgB,KAAK;AAEhD;;;;AAIA,OAAO,MAAMC,IAAI,GAAkDjB,QAAQ,CAACiB,IAAI;AAEhF;;;;AAIA,OAAO,MAAMC,GAAG,GAAkDlB,QAAQ,CAACkB,GAAG;AAE9E;;;;AAIA,OAAO,MAAMC,KAAK,GAA2EnB,QAAQ,CAACmB,KAAK;AAE3G;;;;AAIA,OAAO,MAAMC,IAAI,GAA+DpB,QAAQ,CAACoB,IAAI;AAE7F;;;;AAIA,OAAO,MAAMC,OAAO,GAAkErB,QAAQ,CAACqB,OAAO;AAEtG;;;;AAIA,OAAO,MAAMC,KAAK,GAAkDtB,QAAQ,CAACsB,KAAK;AAElF;;;;AAIA,OAAO,MAAMC,IAAI,GAAkDvB,QAAQ,CAACuB,IAAI;AAEhF;;;;AAIA,OAAO,MAAMC,SAAS,GAsBlBxB,QAAQ,CAACwB,SAAS;AAEtB;;;;AAIA,OAAO,MAAMC,QAAQ,GAWjBzB,QAAQ,CAACyB,QAAQ", "ignoreList": []}