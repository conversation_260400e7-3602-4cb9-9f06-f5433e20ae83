{"version": 3, "file": "FiberRef.js", "names": ["core", "fiberRuntime", "query", "Scheduler", "FiberRefTypeId", "make", "fiberRefMake", "makeWith", "fiberRefMakeWith", "makeContext", "fiberRefMakeContext", "makeRuntimeFlags", "fiberRefMakeRuntimeFlags", "unsafeMake", "fiberRefUnsafeMake", "unsafeMakeHashSet", "fiberRefUnsafeMakeHashSet", "unsafeMakeContext", "fiberRefUnsafeMakeContext", "unsafeMakeSupervisor", "fiberRefUnsafeMakeSupervisor", "unsafeMakePatch", "fiberRefUnsafeMakePatch", "get", "fiberRefGet", "getAndSet", "fiberRefGetAndSet", "getAndUpdate", "fiberRefGetAndUpdate", "getAndUpdateSome", "fiberRefGetAndUpdateSome", "getWith", "fiberRefGetWith", "set", "fiberRefSet", "_delete", "fiberRefDelete", "delete", "reset", "fiberRefReset", "modify", "fiberRefModify", "modifySome", "fiberRefModifySome", "update", "fiberRefUpdate", "updateSome", "fiberRefUpdateSome", "updateAndGet", "fiberRefUpdateAndGet", "updateSomeAndGet", "fiberRefUpdateSomeAndGet", "currentConcurrency", "currentRequestBatchingEnabled", "currentRequestBatching", "currentRequestCache", "currentCache", "currentRequestCacheEnabled", "currentCacheEnabled", "currentContext", "currentSchedulingPriority", "currentMaxOpsBeforeYield", "unhandledErrorLogLevel", "currentUnhandledErrorLogLevel", "currentLogAnnotations", "currentLoggers", "currentLogLevel", "currentMinimumLogLevel", "currentLogSpan", "currentRuntimeFlags", "currentScheduler", "currentSupervisor", "currentMetricLabels", "currentTracerEnabled", "currentTracer<PERSON><PERSON>ing<PERSON>nabled", "currentTracerSpanAnnotations", "currentTracerSpanLinks", "interruptedCause", "currentInterruptedCause"], "sources": ["../../src/FiberRef.ts"], "sourcesContent": [null], "mappings": "AAWA,OAAO,KAAKA,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,YAAY,MAAM,4BAA4B;AAC1D,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAS5C,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAO3C;;;;AAIA,OAAO,MAAMC,cAAc,GAAkBJ,IAAI,CAACI,cAAc;AAwDhE;;;;AAIA,OAAO,MAAMC,IAAI,GAMqCJ,YAAY,CAACK,YAAY;AAE/E;;;;AAIA,OAAO,MAAMC,QAAQ,GACnBN,YAAY,CAACO,gBAAgB;AAE/B;;;;AAIA,OAAO,MAAMC,WAAW,GAE+CR,YAAY,CAACS,mBAAmB;AAEvG;;;;AAIA,OAAO,MAAMC,gBAAgB,GAEiDV,YAAY,CAACW,wBAAwB;AAEnH;;;;AAIA,OAAO,MAAMC,UAAU,GAMAb,IAAI,CAACc,kBAAkB;AAE9C;;;;AAIA,OAAO,MAAMC,iBAAiB,GAC5Bf,IAAI,CAACgB,yBAAyB;AAEhC;;;;AAIA,OAAO,MAAMC,iBAAiB,GAC5BjB,IAAI,CAACkB,yBAAyB;AAEhC;;;;AAIA,OAAO,MAAMC,oBAAoB,GAC/BlB,YAAY,CAACmB,4BAA4B;AAE3C;;;;AAIA,OAAO,MAAMC,eAAe,GAOLrB,IAAI,CAACsB,uBAAuB;AAEnD;;;;AAIA,OAAO,MAAMC,GAAG,GAA+CvB,IAAI,CAACwB,WAAW;AAE/E;;;;AAIA,OAAO,MAAMC,SAAS,GAWlBzB,IAAI,CAAC0B,iBAAiB;AAE1B;;;;AAIA,OAAO,MAAMC,YAAY,GAWrB3B,IAAI,CAAC4B,oBAAoB;AAE7B;;;;AAIA,OAAO,MAAMC,gBAAgB,GAWzB7B,IAAI,CAAC8B,wBAAwB;AAEjC;;;;AAIA,OAAO,MAAMC,OAAO,GAWhB/B,IAAI,CAACgC,eAAe;AAExB;;;;AAIA,OAAO,MAAMC,GAAG,GAWZjC,IAAI,CAACkC,WAAW;AAEpB,MAAMC,OAAO,GAAkDnC,IAAI,CAACoC,cAAc;AAElF;AACE;;;;AAIAD,OAAO,IAAIE,MAAM;AAGnB;;;;AAIA,OAAO,MAAMC,KAAK,GAAkDtC,IAAI,CAACuC,aAAa;AAEtF;;;;AAIA,OAAO,MAAMC,MAAM,GAWfxC,IAAI,CAACyC,cAAc;AAEvB;;;;AAIA,OAAO,MAAMC,UAAU,GAIC1C,IAAI,CAAC2C,kBAAkB;AAE/C;;;;AAIA,OAAO,MAAMC,MAAM,GAWf5C,IAAI,CAAC6C,cAAc;AAEvB;;;;AAIA,OAAO,MAAMC,UAAU,GAWnB9C,IAAI,CAAC+C,kBAAkB;AAE3B;;;;AAIA,OAAO,MAAMC,YAAY,GAWrBhD,IAAI,CAACiD,oBAAoB;AAE7B;;;;AAIA,OAAO,MAAMC,gBAAgB,GAWzBlD,IAAI,CAACmD,wBAAwB;AAEjC;;;;AAIA,OAAO,MAAMC,kBAAkB,GAAmCpD,IAAI,CAACoD,kBAAkB;AAEzF;;;;AAIA,OAAO,MAAMC,6BAA6B,GAAsBrD,IAAI,CAACsD,sBAAsB;AAE3F;;;;AAIA,OAAO,MAAMC,mBAAmB,GAA4BrD,KAAK,CAACsD,YAAmB;AAErF;;;;AAIA,OAAO,MAAMC,0BAA0B,GAAsBvD,KAAK,CAACwD,mBAAmB;AAEtF;;;;AAIA,OAAO,MAAMC,cAAc,GAAqC3D,IAAI,CAAC2D,cAAc;AAEnF;;;;AAIA,OAAO,MAAMC,yBAAyB,GAAqB5D,IAAI,CAAC4D,yBAAyB;AAEzF;;;;AAIA,OAAO,MAAMC,wBAAwB,GAAqB7D,IAAI,CAAC6D,wBAAwB;AAEvF;;;;AAIA,OAAO,MAAMC,sBAAsB,GAA+C9D,IAAI,CAAC+D,6BAA6B;AAEpH;;;;AAIA,OAAO,MAAMC,qBAAqB,GAA+ChE,IAAI,CAACgE,qBAAqB;AAE3G;;;;AAIA,OAAO,MAAMC,cAAc,GAA2DhE,YAAY,CAACgE,cAAc;AAEjH;;;;AAIA,OAAO,MAAMC,eAAe,GAAgClE,IAAI,CAACkE,eAAe;AAEhF;;;;AAIA,OAAO,MAAMC,sBAAsB,GAAgClE,YAAY,CAACkE,sBAAsB;AAEtG;;;;AAIA,OAAO,MAAMC,cAAc,GAAyCpE,IAAI,CAACoE,cAAc;AAEvF;;;;AAIA,OAAO,MAAMC,mBAAmB,GAAwCpE,YAAY,CAACoE,mBAAmB;AAExG;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAkCnE,SAAS,CAACmE,gBAAgB;AAEzF;;;;AAIA,OAAO,MAAMC,iBAAiB,GAAyCtE,YAAY,CAACsE,iBAAiB;AAErG;;;;AAIA,OAAO,MAAMC,mBAAmB,GAAqDxE,IAAI,CAACwE,mBAAmB;AAE7G;;;;AAIA,OAAO,MAAMC,oBAAoB,GAAsBzE,IAAI,CAACyE,oBAAoB;AAEhF;;;;AAIA,OAAO,MAAMC,0BAA0B,GAAsB1E,IAAI,CAAC0E,0BAA0B;AAE5F;;;;AAIA,OAAO,MAAMC,4BAA4B,GACvC3E,IAAI,CAAC2E,4BAA4B;AAEnC;;;;AAIA,OAAO,MAAMC,sBAAsB,GAA2C5E,IAAI,CAAC4E,sBAAsB;AAEzG;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAiC7E,IAAI,CAAC8E,uBAAuB", "ignoreList": []}