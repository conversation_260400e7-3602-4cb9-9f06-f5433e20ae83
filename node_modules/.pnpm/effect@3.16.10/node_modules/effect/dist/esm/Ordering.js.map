{"version": 3, "file": "Ordering.js", "names": ["dual", "reverse", "o", "match", "self", "onEqual", "onG<PERSON>r<PERSON><PERSON>", "onLessThan", "combine", "that", "combineMany", "collection", "ordering", "combineAll"], "sources": ["../../src/Ordering.ts"], "sourcesContent": [null], "mappings": "AAIA,SAASA,IAAI,QAAQ,eAAe;AAQpC;;;;;;;;;;;;;;;AAeA,OAAO,MAAMC,OAAO,GAAIC,CAAW,IAAgBA,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE;AAEnF;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,KAAK,gBA8DdH,IAAI,CAAC,CAAC,EAAE,CACVI,IAAc,EACd;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAU,CAInC,KACaH,IAAI,KAAK,CAAC,CAAC,GAAGG,UAAU,EAAE,GAAGH,IAAI,KAAK,CAAC,GAAGC,OAAO,EAAE,GAAGC,aAAa,EAAE,CAAC;AAEtF;;;;AAIA,OAAO,MAAME,OAAO,gBAWhBR,IAAI,CAAC,CAAC,EAAE,CAACI,IAAc,EAAEK,IAAc,KAAeL,IAAI,KAAK,CAAC,GAAGA,IAAI,GAAGK,IAAI,CAAC;AAEnF;;;;AAIA,OAAO,MAAMC,WAAW,gBAWpBV,IAAI,CAAC,CAAC,EAAE,CAACI,IAAc,EAAEO,UAA8B,KAAc;EACvE,IAAIC,QAAQ,GAAGR,IAAI;EACnB,IAAIQ,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAOA,QAAQ;EACjB;EACA,KAAKA,QAAQ,IAAID,UAAU,EAAE;IAC3B,IAAIC,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAOA,QAAQ;IACjB;EACF;EACA,OAAOA,QAAQ;AACjB,CAAC,CAAC;AAEF;;;;AAIA,OAAO,MAAMC,UAAU,GAAIF,UAA8B,IAAeD,WAAW,CAAC,CAAC,EAAEC,UAAU,CAAC", "ignoreList": []}