{"version": 3, "file": "MergeState.js", "names": ["internal", "MergeStateTypeId", "BothRunning", "LeftDone", "RightDone", "isMergeState", "isBothRunning", "isLeftDone", "isRightDone", "match"], "sources": ["../../src/MergeState.ts"], "sourcesContent": [null], "mappings": "AAOA,OAAO,KAAKA,QAAQ,MAAM,kCAAkC;AAE5D;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAkBD,QAAQ,CAACC,gBAAgB;AAgExE;;;;AAIA,OAAO,MAAMC,WAAW,GAG0CF,QAAQ,CAACE,WAAW;AAEtF;;;;AAIA,OAAO,MAAMC,QAAQ,GAE6CH,QAAQ,CAACG,QAAQ;AAEnF;;;;AAIA,OAAO,MAAMC,SAAS,GAE4CJ,QAAQ,CAACI,SAAS;AAEpF;;;;;;AAMA,OAAO,MAAMC,YAAY,GAEsEL,QAAQ,CAACK,YAAY;AAEpH;;;;;;;AAOA,OAAO,MAAMC,aAAa,GAEiDN,QAAQ,CAACM,aAAa;AAEjG;;;;;;;AAOA,OAAO,MAAMC,UAAU,GAEiDP,QAAQ,CAACO,UAAU;AAE3F;;;;;;;AAOA,OAAO,MAAMC,WAAW,GAEiDR,QAAQ,CAACQ,WAAW;AAE7F;;;;AAIA,OAAO,MAAMC,KAAK,GA8BdT,QAAQ,CAACS,KAAK", "ignoreList": []}