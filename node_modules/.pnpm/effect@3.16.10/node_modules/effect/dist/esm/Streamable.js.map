{"version": 3, "file": "Streamable.js", "names": ["pipeArguments", "Stream", "streamVariance", "_R", "_", "_E", "_A", "Class", "StreamTypeId", "pipe", "arguments", "channel", "toChannel", "toStream"], "sources": ["../../src/Streamable.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAIA,SAASA,aAAa,QAAQ,eAAe;AAC7C,OAAO,KAAKC,MAAM,MAAM,aAAa;AAErC,MAAMC,cAAc,GAAG;EACrB;EACAC,EAAE,EAAGC,CAAQ,IAAKA,CAAC;EACnB;EACAC,EAAE,EAAGD,CAAQ,IAAKA,CAAC;EACnB;EACAE,EAAE,EAAGF,CAAQ,IAAKA;CACnB;AAED;;;;AAIA,OAAM,MAAgBG,KAAK;EACzB;;;EAGS,CAACN,MAAM,CAACO,YAAY,IAAIN,cAAc;EAE/C;;;EAGAO,IAAIA,CAAA;IACF,OAAOT,aAAa,CAAC,IAAI,EAAEU,SAAS,CAAC;EACvC;EAOA;;;EAGA,IAAIC,OAAOA,CAAA;IACT,OAAOV,MAAM,CAACW,SAAS,CAAC,IAAI,CAACC,QAAQ,EAAE,CAAC;EAC1C", "ignoreList": []}