/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
import { SvelteComponentTyped } from "svelte";
import type { IconProps } from '../types.js';
declare const __propDef: {
    props: IconProps;
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {
        default: {};
    };
};
export type FireExtinguisherProps = typeof __propDef.props;
export type FireExtinguisherEvents = typeof __propDef.events;
export type FireExtinguisherSlots = typeof __propDef.slots;
/**
 * @component @name FireExtinguisher
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgNi41VjNhMSAxIDAgMCAwLTEtMWgtMmExIDEgMCAwIDAtMSAxdjMuNSIgLz4KICA8cGF0aCBkPSJNOSAxOGg4IiAvPgogIDxwYXRoIGQ9Ik0xOCAzaC0zIiAvPgogIDxwYXRoIGQ9Ik0xMSAzYTYgNiAwIDAgMC02IDZ2MTEiIC8+CiAgPHBhdGggZD0iTTUgMTNoNCIgLz4KICA8cGF0aCBkPSJNMTcgMTBhNCA0IDAgMCAwLTggMHYxMGEyIDIgMCAwIDAgMiAyaDRhMiAyIDAgMCAwIDItMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/fire-extinguisher
 * @see https://lucide.dev/guide/packages/lucide-svelte - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Svelte component
 *
 */
export default class FireExtinguisher extends SvelteComponentTyped<FireExtinguisherProps, FireExtinguisherEvents, FireExtinguisherSlots> {
}
export {};
