{"name": "@opentelemetry/exporter-prometheus", "version": "0.201.1", "description": "OpenTelemetry Exporter Prometheus provides a metrics endpoint for Prometheus", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "test": "nyc mocha 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../../scripts/peer-api-check.js", "align-api-deps": "node ../../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": "^18.19.0 || >=20.6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "1.9.0", "@opentelemetry/semantic-conventions": "^1.29.0", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/sinon": "17.0.4", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "11.1.0", "nyc": "17.1.0", "sinon": "15.1.2", "typescript": "5.0.4"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@opentelemetry/core": "2.0.1", "@opentelemetry/resources": "2.0.1", "@opentelemetry/sdk-metrics": "2.0.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/opentelemetry-exporter-prometheus", "sideEffects": false, "gitHead": "9dbd1e446be0ecc7c22b00051c5cfb2612d9b0f2"}