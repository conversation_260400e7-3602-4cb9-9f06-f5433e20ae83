/**
 * vis-util
 * https://github.com/visjs/vis-util
 *
 * utilitie collection for visjs
 *
 * @version 6.0.0
 * @date    2025-07-12T18:02:43.836Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{})}(this,function(t){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var r,i,o,a,s,c,u,l,h,f,p,d,v,g,m,y,b={};function w(){if(i)return r;i=1;var t=function(t){return t&&t.Math===Math&&t};return r=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof e&&e)||t("object"==typeof r&&r)||function(){return this}()||Function("return this")()}function C(){return a?o:(a=1,o=function(t){try{return!!t()}catch(t){return!0}})}function E(){return c?s:(c=1,s=!C()(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))}function k(){if(l)return u;l=1;var t=E(),e=Function.prototype,n=e.apply,r=e.call;return u="object"==typeof Reflect&&Reflect.apply||(t?r.bind(n):function(){return r.apply(n,arguments)}),u}function O(){if(f)return h;f=1;var t=E(),e=Function.prototype,n=e.call,r=t&&e.bind.bind(n,n);return h=t?r:function(t){return function(){return n.apply(t,arguments)}},h}function T(){if(d)return p;d=1;var t=O(),e=t({}.toString),n=t("".slice);return p=function(t){return n(e(t),8,-1)}}function F(){if(g)return v;g=1;var t=T(),e=O();return v=function(n){if("Function"===t(n))return e(n)}}function _(){if(y)return m;y=1;var t="object"==typeof document&&document.all;return m=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(t){return"function"==typeof t}}var S,D,P,x,A={};function j(){return D?S:(D=1,S=!C()(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function R(){if(x)return P;x=1;var t=E(),e=Function.prototype.call;return P=t?e.bind(e):function(){return e.apply(e,arguments)},P}var N,I,B,M,L,z,H,W,V,q,U,Y,X,G,Q,J,$,Z,K,tt,et,nt,rt,it,ot,at,st,ct,ut,lt,ht,ft,pt,dt,vt,gt,mt,yt={};function bt(){if(N)return yt;N=1;var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,n=e&&!t.call({1:2},1);return yt.f=n?function(t){var n=e(this,t);return!!n&&n.enumerable}:t,yt}function wt(){return B?I:(B=1,I=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}})}function Ct(){if(L)return M;L=1;var t=O(),e=C(),n=T(),r=Object,i=t("".split);return M=e(function(){return!r("z").propertyIsEnumerable(0)})?function(t){return"String"===n(t)?i(t,""):r(t)}:r}function Et(){return H?z:(H=1,z=function(t){return null==t})}function kt(){if(V)return W;V=1;var t=Et(),e=TypeError;return W=function(n){if(t(n))throw new e("Can't call method on "+n);return n}}function Ot(){if(U)return q;U=1;var t=Ct(),e=kt();return q=function(n){return t(e(n))}}function Tt(){if(X)return Y;X=1;var t=_();return Y=function(e){return"object"==typeof e?null!==e:t(e)}}function Ft(){return Q?G:(Q=1,G={})}function _t(){if($)return J;$=1;var t=Ft(),e=w(),n=_(),r=function(t){return n(t)?t:void 0};return J=function(n,i){return arguments.length<2?r(t[n])||r(e[n]):t[n]&&t[n][i]||e[n]&&e[n][i]},J}function St(){return K?Z:(K=1,Z=O()({}.isPrototypeOf))}function Dt(){if(et)return tt;et=1;var t=w().navigator,e=t&&t.userAgent;return tt=e?String(e):""}function Pt(){if(rt)return nt;rt=1;var t,e,n=w(),r=Dt(),i=n.process,o=n.Deno,a=i&&i.versions||o&&o.version,s=a&&a.v8;return s&&(e=(t=s.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!e&&r&&(!(t=r.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=r.match(/Chrome\/(\d+)/))&&(e=+t[1]),nt=e}function xt(){if(ot)return it;ot=1;var t=Pt(),e=C(),n=w().String;return it=!!Object.getOwnPropertySymbols&&!e(function(){var e=Symbol("symbol detection");return!n(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&t&&t<41}),it}function At(){return st?at:(st=1,at=xt()&&!Symbol.sham&&"symbol"==typeof Symbol.iterator)}function jt(){if(ut)return ct;ut=1;var t=_t(),e=_(),n=St(),r=Object;return ct=At()?function(t){return"symbol"==typeof t}:function(i){var o=t("Symbol");return e(o)&&n(o.prototype,r(i))}}function Rt(){if(ht)return lt;ht=1;var t=String;return lt=function(e){try{return t(e)}catch(t){return"Object"}}}function Nt(){if(pt)return ft;pt=1;var t=_(),e=Rt(),n=TypeError;return ft=function(r){if(t(r))return r;throw new n(e(r)+" is not a function")}}function It(){if(vt)return dt;vt=1;var t=Nt(),e=Et();return dt=function(n,r){var i=n[r];return e(i)?void 0:t(i)}}function Bt(){if(mt)return gt;mt=1;var t=R(),e=_(),n=Tt(),r=TypeError;return gt=function(i,o){var a,s;if("string"===o&&e(a=i.toString)&&!n(s=t(a,i)))return s;if(e(a=i.valueOf)&&!n(s=t(a,i)))return s;if("string"!==o&&e(a=i.toString)&&!n(s=t(a,i)))return s;throw new r("Can't convert object to primitive value")}}var Mt,Lt,zt,Ht,Wt,Vt,qt,Ut,Yt,Xt,Gt,Qt,Jt,$t,Zt,Kt,te,ee,ne,re,ie,oe,ae,se,ce,ue,le,he,fe={exports:{}};function pe(){return Lt?Mt:(Lt=1,Mt=!0)}function de(){if(Ht)return zt;Ht=1;var t=w(),e=Object.defineProperty;return zt=function(n,r){try{e(t,n,{value:r,configurable:!0,writable:!0})}catch(e){t[n]=r}return r}}function ve(){if(Wt)return fe.exports;Wt=1;var t=pe(),e=w(),n=de(),r="__core-js_shared__",i=fe.exports=e[r]||n(r,{});return(i.versions||(i.versions=[])).push({version:"3.44.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"}),fe.exports}function ge(){if(qt)return Vt;qt=1;var t=ve();return Vt=function(e,n){return t[e]||(t[e]=n||{})}}function me(){if(Yt)return Ut;Yt=1;var t=kt(),e=Object;return Ut=function(n){return e(t(n))}}function ye(){if(Gt)return Xt;Gt=1;var t=O(),e=me(),n=t({}.hasOwnProperty);return Xt=Object.hasOwn||function(t,r){return n(e(t),r)}}function be(){if(Jt)return Qt;Jt=1;var t=O(),e=0,n=Math.random(),r=t(1.1.toString);return Qt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+r(++e+n,36)}}function we(){if(Zt)return $t;Zt=1;var t=w(),e=ge(),n=ye(),r=be(),i=xt(),o=At(),a=t.Symbol,s=e("wks"),c=o?a.for||a:a&&a.withoutSetter||r;return $t=function(t){return n(s,t)||(s[t]=i&&n(a,t)?a[t]:c("Symbol."+t)),s[t]}}function Ce(){if(te)return Kt;te=1;var t=R(),e=Tt(),n=jt(),r=It(),i=Bt(),o=TypeError,a=we()("toPrimitive");return Kt=function(s,c){if(!e(s)||n(s))return s;var u,l=r(s,a);if(l){if(void 0===c&&(c="default"),u=t(l,s,c),!e(u)||n(u))return u;throw new o("Can't convert object to primitive value")}return void 0===c&&(c="number"),i(s,c)}}function Ee(){if(ne)return ee;ne=1;var t=Ce(),e=jt();return ee=function(n){var r=t(n,"string");return e(r)?r:r+""}}function ke(){if(ie)return re;ie=1;var t=w(),e=Tt(),n=t.document,r=e(n)&&e(n.createElement);return re=function(t){return r?n.createElement(t):{}}}function Oe(){if(ae)return oe;ae=1;var t=j(),e=C(),n=ke();return oe=!t&&!e(function(){return 7!==Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a})}function Te(){if(se)return A;se=1;var t=j(),e=R(),n=bt(),r=wt(),i=Ot(),o=Ee(),a=ye(),s=Oe(),c=Object.getOwnPropertyDescriptor;return A.f=t?c:function(t,u){if(t=i(t),u=o(u),s)try{return c(t,u)}catch(t){}if(a(t,u))return r(!e(n.f,t,u),t[u])},A}function Fe(){if(ue)return ce;ue=1;var t=C(),e=_(),n=/#|\.prototype\./,r=function(n,r){var c=o[i(n)];return c===s||c!==a&&(e(r)?t(r):!!r)},i=r.normalize=function(t){return String(t).replace(n,".").toLowerCase()},o=r.data={},a=r.NATIVE="N",s=r.POLYFILL="P";return ce=r}function _e(){if(he)return le;he=1;var t=F(),e=Nt(),n=E(),r=t(t.bind);return le=function(t,i){return e(t),void 0===i?t:n?r(t,i):function(){return t.apply(i,arguments)}},le}var Se,De,Pe,xe,Ae,je,Re,Ne,Ie,Be,Me,Le,ze,He,We,Ve,qe,Ue,Ye,Xe,Ge,Qe,Je,$e,Ze,Ke,tn,en,nn,rn,on,an,sn,cn,un,ln,hn,fn,pn={};function dn(){return De?Se:(De=1,Se=j()&&C()(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype}))}function vn(){if(xe)return Pe;xe=1;var t=Tt(),e=String,n=TypeError;return Pe=function(r){if(t(r))return r;throw new n(e(r)+" is not an object")}}function gn(){if(Ae)return pn;Ae=1;var t=j(),e=Oe(),n=dn(),r=vn(),i=Ee(),o=TypeError,a=Object.defineProperty,s=Object.getOwnPropertyDescriptor,c="enumerable",u="configurable",l="writable";return pn.f=t?n?function(t,e,n){if(r(t),e=i(e),r(n),"function"==typeof t&&"prototype"===e&&"value"in n&&l in n&&!n[l]){var o=s(t,e);o&&o[l]&&(t[e]=n.value,n={configurable:u in n?n[u]:o[u],enumerable:c in n?n[c]:o[c],writable:!1})}return a(t,e,n)}:a:function(t,n,s){if(r(t),n=i(n),r(s),e)try{return a(t,n,s)}catch(t){}if("get"in s||"set"in s)throw new o("Accessors not supported");return"value"in s&&(t[n]=s.value),t},pn}function mn(){if(Re)return je;Re=1;var t=j(),e=gn(),n=wt();return je=t?function(t,r,i){return e.f(t,r,n(1,i))}:function(t,e,n){return t[e]=n,t}}function yn(){if(Ie)return Ne;Ie=1;var t=w(),e=k(),n=F(),r=_(),i=Te().f,o=Fe(),a=Ft(),s=_e(),c=mn(),u=ye(),l=function(t){var n=function(r,i,o){if(this instanceof n){switch(arguments.length){case 0:return new t;case 1:return new t(r);case 2:return new t(r,i)}return new t(r,i,o)}return e(t,this,arguments)};return n.prototype=t.prototype,n};return Ne=function(e,h){var f,p,d,v,g,m,y,b,w,C=e.target,E=e.global,k=e.stat,O=e.proto,T=E?t:k?t[C]:t[C]&&t[C].prototype,F=E?a:a[C]||c(a,C,{})[C],_=F.prototype;for(v in h)p=!(f=o(E?v:C+(k?".":"#")+v,e.forced))&&T&&u(T,v),m=F[v],p&&(y=e.dontCallGetSet?(w=i(T,v))&&w.value:T[v]),g=p&&y?y:h[v],(f||O||typeof m!=typeof g)&&(b=e.bind&&p?s(g,t):e.wrap&&p?l(g):O&&r(g)?n(g):g,(e.sham||g&&g.sham||m&&m.sham)&&c(b,"sham",!0),c(F,v,b),O&&(u(a,d=C+"Prototype")||c(a,d,{}),c(a[d],v,g),e.real&&_&&(f||!_[v])&&c(_,v,g)))}}function bn(){if(Me)return Be;Me=1;var t=T();return Be=Array.isArray||function(e){return"Array"===t(e)}}function wn(){if(ze)return Le;ze=1;var t=Math.ceil,e=Math.floor;return Le=Math.trunc||function(n){var r=+n;return(r>0?e:t)(r)}}function Cn(){if(We)return He;We=1;var t=wn();return He=function(e){var n=+e;return n!=n||0===n?0:t(n)}}function En(){if(qe)return Ve;qe=1;var t=Cn(),e=Math.min;return Ve=function(n){var r=t(n);return r>0?e(r,9007199254740991):0}}function kn(){if(Ye)return Ue;Ye=1;var t=En();return Ue=function(e){return t(e.length)}}function On(){if(Ge)return Xe;Ge=1;var t=TypeError;return Xe=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}}function Tn(){if(Je)return Qe;Je=1;var t=j(),e=gn(),n=wt();return Qe=function(r,i,o){t?e.f(r,i,n(0,o)):r[i]=o}}function Fn(){if(Ze)return $e;Ze=1;var t={};return t[we()("toStringTag")]="z",$e="[object z]"===String(t)}function _n(){if(tn)return Ke;tn=1;var t=Fn(),e=_(),n=T(),r=we()("toStringTag"),i=Object,o="Arguments"===n(function(){return arguments}());return Ke=t?n:function(t){var a,s,c;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(s=function(t,e){try{return t[e]}catch(t){}}(a=i(t),r))?s:o?n(a):"Object"===(c=n(a))&&e(a.callee)?"Arguments":c}}function Sn(){if(nn)return en;nn=1;var t=O(),e=_(),n=ve(),r=t(Function.toString);return e(n.inspectSource)||(n.inspectSource=function(t){return r(t)}),en=n.inspectSource}function Dn(){if(on)return rn;on=1;var t=O(),e=C(),n=_(),r=_n(),i=_t(),o=Sn(),a=function(){},s=i("Reflect","construct"),c=/^\s*(?:class|function)\b/,u=t(c.exec),l=!c.test(a),h=function(t){if(!n(t))return!1;try{return s(a,[],t),!0}catch(t){return!1}},f=function(t){if(!n(t))return!1;switch(r(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return l||!!u(c,o(t))}catch(t){return!0}};return f.sham=!0,rn=!s||e(function(){var t;return h(h.call)||!h(Object)||!h(function(){t=!0})||t})?f:h}function Pn(){if(sn)return an;sn=1;var t=bn(),e=Dn(),n=Tt(),r=we()("species"),i=Array;return an=function(o){var a;return t(o)&&(a=o.constructor,(e(a)&&(a===i||t(a.prototype))||n(a)&&null===(a=a[r]))&&(a=void 0)),void 0===a?i:a}}function xn(){if(un)return cn;un=1;var t=Pn();return cn=function(e,n){return new(t(e))(0===n?0:n)}}function An(){if(hn)return ln;hn=1;var t=C(),e=we(),n=Pt(),r=e("species");return ln=function(e){return n>=51||!t(function(){var t=[];return(t.constructor={})[r]=function(){return{foo:1}},1!==t[e](Boolean).foo})}}function jn(){if(fn)return b;fn=1;var t=yn(),e=C(),n=bn(),r=Tt(),i=me(),o=kn(),a=On(),s=Tn(),c=xn(),u=An(),l=we(),h=Pt(),f=l("isConcatSpreadable"),p=h>=51||!e(function(){var t=[];return t[f]=!1,t.concat()[0]!==t}),d=function(t){if(!r(t))return!1;var e=t[f];return void 0!==e?!!e:n(t)};return t({target:"Array",proto:!0,arity:1,forced:!p||!u("concat")},{concat:function(t){var e,n,r,u,l,h=i(this),f=c(h,0),p=0;for(e=-1,r=arguments.length;e<r;e++)if(d(l=-1===e?h:arguments[e]))for(u=o(l),a(p+u),n=0;n<u;n++,p++)n in l&&s(f,p,l[n]);else a(p+1),s(f,p++,l);return f.length=p,f}}),b}var Rn,Nn,In={},Bn={};function Mn(){if(Nn)return Rn;Nn=1;var t=_n(),e=String;return Rn=function(n){if("Symbol"===t(n))throw new TypeError("Cannot convert a Symbol value to a string");return e(n)}}var Ln,zn,Hn,Wn,Vn,qn,Un,Yn,Xn,Gn,Qn,Jn,$n,Zn,Kn,tr,er,nr,rr,ir={};function or(){if(zn)return Ln;zn=1;var t=Cn(),e=Math.max,n=Math.min;return Ln=function(r,i){var o=t(r);return o<0?e(o+i,0):n(o,i)}}function ar(){if(Wn)return Hn;Wn=1;var t=Ot(),e=or(),n=kn(),r=function(r){return function(i,o,a){var s=t(i),c=n(s);if(0===c)return!r&&-1;var u,l=e(a,c);if(r&&o!=o){for(;c>l;)if((u=s[l++])!=u)return!0}else for(;c>l;l++)if((r||l in s)&&s[l]===o)return r||l||0;return!r&&-1}};return Hn={includes:r(!0),indexOf:r(!1)}}function sr(){return qn?Vn:(qn=1,Vn={})}function cr(){if(Yn)return Un;Yn=1;var t=O(),e=ye(),n=Ot(),r=ar().indexOf,i=sr(),o=t([].push);return Un=function(t,a){var s,c=n(t),u=0,l=[];for(s in c)!e(i,s)&&e(c,s)&&o(l,s);for(;a.length>u;)e(c,s=a[u++])&&(~r(l,s)||o(l,s));return l}}function ur(){return Gn?Xn:(Gn=1,Xn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}function lr(){if(Jn)return Qn;Jn=1;var t=cr(),e=ur();return Qn=Object.keys||function(n){return t(n,e)}}function hr(){if($n)return ir;$n=1;var t=j(),e=dn(),n=gn(),r=vn(),i=Ot(),o=lr();return ir.f=t&&!e?Object.defineProperties:function(t,e){r(t);for(var a,s=i(e),c=o(e),u=c.length,l=0;u>l;)n.f(t,a=c[l++],s[a]);return t},ir}function fr(){return Kn?Zn:(Kn=1,Zn=_t()("document","documentElement"))}function pr(){if(er)return tr;er=1;var t=ge(),e=be(),n=t("keys");return tr=function(t){return n[t]||(n[t]=e(t))}}function dr(){if(rr)return nr;rr=1;var t,e=vn(),n=hr(),r=ur(),i=sr(),o=fr(),a=ke(),s="prototype",c="script",u=pr()("IE_PROTO"),l=function(){},h=function(t){return"<"+c+">"+t+"</"+c+">"},f=function(t){t.write(h("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{t=new ActiveXObject("htmlfile")}catch(t){}var e,n,i;p="undefined"!=typeof document?document.domain&&t?f(t):(n=a("iframe"),i="java"+c+":",n.style.display="none",o.appendChild(n),n.src=String(i),(e=n.contentWindow.document).open(),e.write(h("document.F=Object")),e.close(),e.F):f(t);for(var u=r.length;u--;)delete p[s][r[u]];return p()};return i[u]=!0,nr=Object.create||function(t,r){var i;return null!==t?(l[s]=e(t),i=new l,l[s]=null,i[u]=t):i=p(),void 0===r?i:n.f(i,r)}}var vr,gr={};function mr(){if(vr)return gr;vr=1;var t=cr(),e=ur().concat("length","prototype");return gr.f=Object.getOwnPropertyNames||function(n){return t(n,e)},gr}var yr,br,wr,Cr={};function Er(){return br?yr:(br=1,yr=O()([].slice))}function kr(){if(wr)return Cr;wr=1;var t=T(),e=Ot(),n=mr().f,r=Er(),i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];return Cr.f=function(o){return i&&"Window"===t(o)?function(t){try{return n(t)}catch(t){return r(i)}}(o):n(e(o))},Cr}var Or,Tr,Fr,_r,Sr,Dr={};function Pr(){return Or||(Or=1,Dr.f=Object.getOwnPropertySymbols),Dr}function xr(){if(Fr)return Tr;Fr=1;var t=mn();return Tr=function(e,n,r,i){return i&&i.enumerable?e[n]=r:t(e,n,r),e}}function Ar(){if(Sr)return _r;Sr=1;var t=gn();return _r=function(e,n,r){return t.f(e,n,r)}}var jr,Rr,Nr,Ir,Br,Mr,Lr,zr,Hr,Wr,Vr,qr,Ur,Yr,Xr,Gr,Qr={};function Jr(){if(jr)return Qr;jr=1;var t=we();return Qr.f=t,Qr}function $r(){if(Nr)return Rr;Nr=1;var t=Ft(),e=ye(),n=Jr(),r=gn().f;return Rr=function(i){var o=t.Symbol||(t.Symbol={});e(o,i)||r(o,i,{value:n.f(i)})}}function Zr(){if(Br)return Ir;Br=1;var t=R(),e=_t(),n=we(),r=xr();return Ir=function(){var i=e("Symbol"),o=i&&i.prototype,a=o&&o.valueOf,s=n("toPrimitive");o&&!o[s]&&r(o,s,function(e){return t(a,this)},{arity:1})}}function Kr(){if(Lr)return Mr;Lr=1;var t=Fn(),e=_n();return Mr=t?{}.toString:function(){return"[object "+e(this)+"]"}}function ti(){if(Hr)return zr;Hr=1;var t=Fn(),e=gn().f,n=mn(),r=ye(),i=Kr(),o=we()("toStringTag");return zr=function(a,s,c,u){var l=c?a:a&&a.prototype;l&&(r(l,o)||e(l,o,{configurable:!0,value:s}),u&&!t&&n(l,"toString",i))}}function ei(){if(Vr)return Wr;Vr=1;var t=w(),e=_(),n=t.WeakMap;return Wr=e(n)&&/native code/.test(String(n))}function ni(){if(Ur)return qr;Ur=1;var t,e,n,r=ei(),i=w(),o=Tt(),a=mn(),s=ye(),c=ve(),u=pr(),l=sr(),h="Object already initialized",f=i.TypeError,p=i.WeakMap;if(r||c.state){var d=c.state||(c.state=new p);d.get=d.get,d.has=d.has,d.set=d.set,t=function(t,e){if(d.has(t))throw new f(h);return e.facade=t,d.set(t,e),e},e=function(t){return d.get(t)||{}},n=function(t){return d.has(t)}}else{var v=u("state");l[v]=!0,t=function(t,e){if(s(t,v))throw new f(h);return e.facade=t,a(t,v,e),e},e=function(t){return s(t,v)?t[v]:{}},n=function(t){return s(t,v)}}return qr={set:t,get:e,has:n,enforce:function(r){return n(r)?e(r):t(r,{})},getterFor:function(t){return function(n){var r;if(!o(n)||(r=e(n)).type!==t)throw new f("Incompatible receiver, "+t+" required");return r}}}}function ri(){if(Xr)return Yr;Xr=1;var t=_e(),e=O(),n=Ct(),r=me(),i=kn(),o=xn(),a=e([].push),s=function(e){var s=1===e,c=2===e,u=3===e,l=4===e,h=6===e,f=7===e,p=5===e||h;return function(d,v,g,m){for(var y,b,w=r(d),C=n(w),E=i(C),k=t(v,g),O=0,T=m||o,F=s?T(d,E):c||f?T(d,0):void 0;E>O;O++)if((p||O in C)&&(b=k(y=C[O],O,w),e))if(s)F[O]=b;else if(b)switch(e){case 3:return!0;case 5:return y;case 6:return O;case 2:a(F,y)}else switch(e){case 4:return!1;case 7:a(F,y)}return h?-1:u||l?l:F}};return Yr={forEach:s(0),map:s(1),filter:s(2),some:s(3),every:s(4),find:s(5),findIndex:s(6),filterReject:s(7)}}var ii,oi,ai,si={};function ci(){return oi?ii:(oi=1,ii=xt()&&!!Symbol.for&&!!Symbol.keyFor)}var ui,li={};var hi,fi,pi,di={};function vi(){if(fi)return hi;fi=1;var t=O(),e=bn(),n=_(),r=T(),i=Mn(),o=t([].push);return hi=function(t){if(n(t))return t;if(e(t)){for(var a=t.length,s=[],c=0;c<a;c++){var u=t[c];"string"==typeof u?o(s,u):"number"!=typeof u&&"Number"!==r(u)&&"String"!==r(u)||o(s,i(u))}var l=s.length,h=!0;return function(t,n){if(h)return h=!1,n;if(e(this))return n;for(var r=0;r<l;r++)if(s[r]===t)return n}}},hi}function gi(){if(pi)return di;pi=1;var t=yn(),e=_t(),n=k(),r=R(),i=O(),o=C(),a=_(),s=jt(),c=Er(),u=vi(),l=xt(),h=String,f=e("JSON","stringify"),p=i(/./.exec),d=i("".charAt),v=i("".charCodeAt),g=i("".replace),m=i(1.1.toString),y=/[\uD800-\uDFFF]/g,b=/^[\uD800-\uDBFF]$/,w=/^[\uDC00-\uDFFF]$/,E=!l||o(function(){var t=e("Symbol")("stringify detection");return"[null]"!==f([t])||"{}"!==f({a:t})||"{}"!==f(Object(t))}),T=o(function(){return'"\\udf06\\ud834"'!==f("\udf06\ud834")||'"\\udead"'!==f("\udead")}),F=function(t,e){var i=c(arguments),o=u(e);if(a(o)||void 0!==t&&!s(t))return i[1]=function(t,e){if(a(o)&&(e=r(o,this,h(t),e)),!s(e))return e},n(f,null,i)},S=function(t,e,n){var r=d(n,e-1),i=d(n,e+1);return p(b,t)&&!p(w,i)||p(w,t)&&!p(b,r)?"\\u"+m(v(t,0),16):t};return f&&t({target:"JSON",stat:!0,arity:3,forced:E||T},{stringify:function(t,e,r){var i=c(arguments),o=n(E?F:f,null,i);return T&&"string"==typeof o?g(o,y,S):o}}),di}var mi,yi,bi={};function wi(){return yi||(yi=1,function(){if(Gr)return Bn;Gr=1;var t=yn(),e=w(),n=R(),r=O(),i=pe(),o=j(),a=xt(),s=C(),c=ye(),u=St(),l=vn(),h=Ot(),f=Ee(),p=Mn(),d=wt(),v=dr(),g=lr(),m=mr(),y=kr(),b=Pr(),E=Te(),k=gn(),T=hr(),F=bt(),_=xr(),S=Ar(),D=ge(),P=pr(),x=sr(),A=be(),N=we(),I=Jr(),B=$r(),M=Zr(),L=ti(),z=ni(),H=ri().forEach,W=P("hidden"),V="Symbol",q="prototype",U=z.set,Y=z.getterFor(V),X=Object[q],G=e.Symbol,Q=G&&G[q],J=e.RangeError,$=e.TypeError,Z=e.QObject,K=E.f,tt=k.f,et=y.f,nt=F.f,rt=r([].push),it=D("symbols"),ot=D("op-symbols"),at=D("wks"),st=!Z||!Z[q]||!Z[q].findChild,ct=function(t,e,n){var r=K(X,e);r&&delete X[e],tt(t,e,n),r&&t!==X&&tt(X,e,r)},ut=o&&s(function(){return 7!==v(tt({},"a",{get:function(){return tt(this,"a",{value:7}).a}})).a})?ct:tt,lt=function(t,e){var n=it[t]=v(Q);return U(n,{type:V,tag:t,description:e}),o||(n.description=e),n},ht=function(t,e,n){t===X&&ht(ot,e,n),l(t);var r=f(e);return l(n),c(it,r)?(n.enumerable?(c(t,W)&&t[W][r]&&(t[W][r]=!1),n=v(n,{enumerable:d(0,!1)})):(c(t,W)||tt(t,W,d(1,v(null))),t[W][r]=!0),ut(t,r,n)):tt(t,r,n)},ft=function(t,e){l(t);var r=h(e),i=g(r).concat(gt(r));return H(i,function(e){o&&!n(pt,r,e)||ht(t,e,r[e])}),t},pt=function(t){var e=f(t),r=n(nt,this,e);return!(this===X&&c(it,e)&&!c(ot,e))&&(!(r||!c(this,e)||!c(it,e)||c(this,W)&&this[W][e])||r)},dt=function(t,e){var n=h(t),r=f(e);if(n!==X||!c(it,r)||c(ot,r)){var i=K(n,r);return!i||!c(it,r)||c(n,W)&&n[W][r]||(i.enumerable=!0),i}},vt=function(t){var e=et(h(t)),n=[];return H(e,function(t){c(it,t)||c(x,t)||rt(n,t)}),n},gt=function(t){var e=t===X,n=et(e?ot:h(t)),r=[];return H(n,function(t){!c(it,t)||e&&!c(X,t)||rt(r,it[t])}),r};a||(G=function(){if(u(Q,this))throw new $("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?p(arguments[0]):void 0,r=A(t),i=function(t){var o=void 0===this?e:this;o===X&&n(i,ot,t),c(o,W)&&c(o[W],r)&&(o[W][r]=!1);var a=d(1,t);try{ut(o,r,a)}catch(t){if(!(t instanceof J))throw t;ct(o,r,a)}};return o&&st&&ut(X,r,{configurable:!0,set:i}),lt(r,t)},_(Q=G[q],"toString",function(){return Y(this).tag}),_(G,"withoutSetter",function(t){return lt(A(t),t)}),F.f=pt,k.f=ht,T.f=ft,E.f=dt,m.f=y.f=vt,b.f=gt,I.f=function(t){return lt(N(t),t)},o&&(S(Q,"description",{configurable:!0,get:function(){return Y(this).description}}),i||_(X,"propertyIsEnumerable",pt,{unsafe:!0}))),t({global:!0,constructor:!0,wrap:!0,forced:!a,sham:!a},{Symbol:G}),H(g(at),function(t){B(t)}),t({target:V,stat:!0,forced:!a},{useSetter:function(){st=!0},useSimple:function(){st=!1}}),t({target:"Object",stat:!0,forced:!a,sham:!o},{create:function(t,e){return void 0===e?v(t):ft(v(t),e)},defineProperty:ht,defineProperties:ft,getOwnPropertyDescriptor:dt}),t({target:"Object",stat:!0,forced:!a},{getOwnPropertyNames:vt}),M(),L(G,V),x[W]=!0}(),function(){if(ai)return si;ai=1;var t=yn(),e=_t(),n=ye(),r=Mn(),i=ge(),o=ci(),a=i("string-to-symbol-registry"),s=i("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!o},{for:function(t){var i=r(t);if(n(a,i))return a[i];var o=e("Symbol")(i);return a[i]=o,s[o]=i,o}})}(),function(){if(ui)return li;ui=1;var t=yn(),e=ye(),n=jt(),r=Rt(),i=ge(),o=ci(),a=i("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!o},{keyFor:function(t){if(!n(t))throw new TypeError(r(t)+" is not a symbol");if(e(a,t))return a[t]}})}(),gi(),function(){if(mi)return bi;mi=1;var t=yn(),e=xt(),n=C(),r=Pr(),i=me();t({target:"Object",stat:!0,forced:!e||n(function(){r.f(1)})},{getOwnPropertySymbols:function(t){var e=r.f;return e?e(i(t)):[]}})}()),In}var Ci;var Ei;var ki;var Oi;var Ti;var Fi;var _i;var Si;var Di;var Pi;var xi;var Ai;var ji,Ri={};var Ni,Ii={};var Bi;var Mi,Li,zi,Hi={};function Wi(){return zi?Li:(zi=1,jn(),wi(),Ci||(Ci=1,$r()("asyncDispose")),Ei||(Ei=1,$r()("asyncIterator")),ki||(ki=1,$r()("dispose")),Oi||(Oi=1,$r()("hasInstance")),Ti||(Ti=1,$r()("isConcatSpreadable")),Fi||(Fi=1,$r()("iterator")),_i||(_i=1,$r()("match")),Si||(Si=1,$r()("matchAll")),Di||(Di=1,$r()("replace")),Pi||(Pi=1,$r()("search")),xi||(xi=1,$r()("species")),Ai||(Ai=1,$r()("split")),function(){if(ji)return Ri;ji=1;var t=$r(),e=Zr();t("toPrimitive"),e()}(),function(){if(Ni)return Ii;Ni=1;var t=_t(),e=$r(),n=ti();e("toStringTag"),n(t("Symbol"),"Symbol")}(),Bi||(Bi=1,$r()("unscopables")),function(){if(Mi)return Hi;Mi=1;var t=w();ti()(t.JSON,"JSON",!0)}(),Li=Ft().Symbol)}var Vi,qi,Ui,Yi,Xi,Gi,Qi,Ji,$i,Zi,Ki,to,eo,no,ro,io,oo,ao,so,co,uo,lo,ho,fo,po,vo,go,mo,yo,bo,wo,Co,Eo,ko,Oo,To={};function Fo(){return qi?Vi:(qi=1,Vi=function(){})}function _o(){return Yi?Ui:(Yi=1,Ui={})}function So(){if(Gi)return Xi;Gi=1;var t=j(),e=ye(),n=Function.prototype,r=t&&Object.getOwnPropertyDescriptor,i=e(n,"name"),o=i&&"something"===function(){}.name,a=i&&(!t||t&&r(n,"name").configurable);return Xi={EXISTS:i,PROPER:o,CONFIGURABLE:a}}function Do(){return Ji?Qi:(Ji=1,Qi=!C()(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))}function Po(){if(Zi)return $i;Zi=1;var t=ye(),e=_(),n=me(),r=pr(),i=Do(),o=r("IE_PROTO"),a=Object,s=a.prototype;return $i=i?a.getPrototypeOf:function(r){var i=n(r);if(t(i,o))return i[o];var c=i.constructor;return e(c)&&i instanceof c?c.prototype:i instanceof a?s:null}}function xo(){if(to)return Ki;to=1;var t,e,n,r=C(),i=_(),o=Tt(),a=dr(),s=Po(),c=xr(),u=we(),l=pe(),h=u("iterator"),f=!1;return[].keys&&("next"in(n=[].keys())?(e=s(s(n)))!==Object.prototype&&(t=e):f=!0),!o(t)||r(function(){var e={};return t[h].call(e)!==e})?t={}:l&&(t=a(t)),i(t[h])||c(t,h,function(){return this}),Ki={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:f}}function Ao(){if(no)return eo;no=1;var t=xo().IteratorPrototype,e=dr(),n=wt(),r=ti(),i=_o(),o=function(){return this};return eo=function(a,s,c,u){var l=s+" Iterator";return a.prototype=e(t,{next:n(+!u,c)}),r(a,l,!1,!0),i[l]=o,a}}function jo(){if(io)return ro;io=1;var t=O(),e=Nt();return ro=function(n,r,i){try{return t(e(Object.getOwnPropertyDescriptor(n,r)[i]))}catch(t){}}}function Ro(){if(ao)return oo;ao=1;var t=Tt();return oo=function(e){return t(e)||null===e}}function No(){if(co)return so;co=1;var t=Ro(),e=String,n=TypeError;return so=function(r){if(t(r))return r;throw new n("Can't set "+e(r)+" as a prototype")}}function Io(){if(lo)return uo;lo=1;var t=jo(),e=Tt(),n=kt(),r=No();return uo=Object.setPrototypeOf||("__proto__"in{}?function(){var i,o=!1,a={};try{(i=t(Object.prototype,"__proto__","set"))(a,[]),o=a instanceof Array}catch(t){}return function(t,a){return n(t),r(a),e(t)?(o?i(t,a):t.__proto__=a,t):t}}():void 0)}function Bo(){if(fo)return ho;fo=1;var t=yn(),e=R(),n=pe(),r=So(),i=_(),o=Ao(),a=Po(),s=Io(),c=ti(),u=mn(),l=xr(),h=we(),f=_o(),p=xo(),d=r.PROPER,v=r.CONFIGURABLE,g=p.IteratorPrototype,m=p.BUGGY_SAFARI_ITERATORS,y=h("iterator"),b="keys",w="values",C="entries",E=function(){return this};return ho=function(r,h,p,k,O,T,F){o(p,h,k);var _,S,D,P=function(t){if(t===O&&N)return N;if(!m&&t&&t in j)return j[t];switch(t){case b:case w:case C:return function(){return new p(this,t)}}return function(){return new p(this)}},x=h+" Iterator",A=!1,j=r.prototype,R=j[y]||j["@@iterator"]||O&&j[O],N=!m&&R||P(O),I="Array"===h&&j.entries||R;if(I&&(_=a(I.call(new r)))!==Object.prototype&&_.next&&(n||a(_)===g||(s?s(_,g):i(_[y])||l(_,y,E)),c(_,x,!0,!0),n&&(f[x]=E)),d&&O===w&&R&&R.name!==w&&(!n&&v?u(j,"name",w):(A=!0,N=function(){return e(R,this)})),O)if(S={values:P(w),keys:T?N:P(b),entries:P(C)},F)for(D in S)(m||A||!(D in j))&&l(j,D,S[D]);else t({target:h,proto:!0,forced:m||A},S);return n&&!F||j[y]===N||l(j,y,N,{name:O}),f[h]=N,S}}function Mo(){return vo?po:(vo=1,po=function(t,e){return{value:t,done:e}})}function Lo(){return bo?yo:(bo=1,yo={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0})}function zo(){if(wo)return To;wo=1,function(){if(mo)return go;mo=1;var t=Ot(),e=Fo(),n=_o(),r=ni(),i=gn().f,o=Bo(),a=Mo(),s=pe(),c=j(),u="Array Iterator",l=r.set,h=r.getterFor(u);go=o(Array,"Array",function(e,n){l(this,{type:u,target:t(e),index:0,kind:n})},function(){var t=h(this),e=t.target,n=t.index++;if(!e||n>=e.length)return t.target=null,a(void 0,!0);switch(t.kind){case"keys":return a(n,!1);case"values":return a(e[n],!1)}return a([n,e[n]],!1)},"values");var f=n.Arguments=n.Array;if(e("keys"),e("values"),e("entries"),!s&&c&&"values"!==f.name)try{i(f,"name",{value:"values"})}catch(t){}}();var t=Lo(),e=w(),n=ti(),r=_o();for(var i in t)n(e[i],i),r[i]=r.Array;return To}function Ho(){if(Eo)return Co;Eo=1;var t=Wi();return zo(),Co=t}var Wo,Vo,qo,Uo,Yo,Xo,Go,Qo,Jo,$o,Zo,Ko=n(Oo?ko:(Oo=1,ko=Ho())),ta={};function ea(){if(qo)return Vo;qo=1;var t=w(),e=Ft();return Vo=function(n,r){var i=e[n+"Prototype"],o=i&&i[r];if(o)return o;var a=t[n],s=a&&a.prototype;return s&&s[r]}}function na(){return Yo?Uo:(Yo=1,function(){if(Wo)return ta;Wo=1;var t=yn(),e=bn(),n=Dn(),r=Tt(),i=or(),o=kn(),a=Ot(),s=Tn(),c=we(),u=An(),l=Er(),h=u("slice"),f=c("species"),p=Array,d=Math.max;t({target:"Array",proto:!0,forced:!h},{slice:function(t,c){var u,h,v,g=a(this),m=o(g),y=i(t,m),b=i(void 0===c?m:c,m);if(e(g)&&(u=g.constructor,(n(u)&&(u===p||e(u.prototype))||r(u)&&null===(u=u[f]))&&(u=void 0),u===p||void 0===u))return l(g,y,b);for(h=new(void 0===u?p:u)(d(b-y,0)),v=0;y<b;y++,v++)y in g&&s(h,v,g[y]);return h.length=v,h}})}(),Uo=ea()("Array","slice"))}function ra(){if(Go)return Xo;Go=1;var t=St(),e=na(),n=Array.prototype;return Xo=function(r){var i=r.slice;return r===n||t(n,r)&&i===n.slice?e:i}}function ia(){return Jo?Qo:(Jo=1,Qo=ra())}var oa,aa,sa,ca,ua,la,ha,fa,pa,da=n(Zo?$o:(Zo=1,$o=ia()));function va(){if(aa)return oa;aa=1;var t=_t(),e=O(),n=mr(),r=Pr(),i=vn(),o=e([].concat);return oa=t("Reflect","ownKeys")||function(t){var e=n.f(i(t)),a=r.f;return a?o(e,a(t)):e},oa}function ga(){return ua?ca:(ua=1,sa||(sa=1,yn()({target:"Reflect",stat:!0},{ownKeys:va()})),ca=Ft().Reflect.ownKeys)}function ma(){return ha?la:(ha=1,la=ga())}var ya,ba,wa,Ca,Ea,ka,Oa,Ta=n(pa?fa:(pa=1,fa=ma()));function Fa(){return wa?ba:(wa=1,ya||(ya=1,yn()({target:"Array",stat:!0},{isArray:bn()})),ba=Ft().Array.isArray)}function _a(){return Ea?Ca:(Ea=1,Ca=Fa())}var Sa,Da,Pa,xa,Aa,ja,Ra,Na,Ia,Ba=n(Oa?ka:(Oa=1,ka=_a())),Ma={};function La(){return Pa?Da:(Pa=1,function(){if(Sa)return Ma;Sa=1;var t=yn(),e=ri().map;t({target:"Array",proto:!0,forced:!An()("map")},{map:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Da=ea()("Array","map"))}function za(){if(Aa)return xa;Aa=1;var t=St(),e=La(),n=Array.prototype;return xa=function(r){var i=r.map;return r===n||t(n,r)&&i===n.map?e:i}}function Ha(){return Ra?ja:(Ra=1,ja=za())}var Wa,Va,qa,Ua,Ya,Xa,Ga,Qa=n(Ia?Na:(Ia=1,Na=Ha())),Ja={};function $a(){return qa?Va:(qa=1,function(){if(Wa)return Ja;Wa=1;var t=yn(),e=me(),n=lr();t({target:"Object",stat:!0,forced:C()(function(){n(1)})},{keys:function(t){return n(e(t))}})}(),Va=Ft().Object.keys)}function Za(){return Ya?Ua:(Ya=1,Ua=$a())}var Ka=n(Ga?Xa:(Ga=1,Xa=Za()));const ts=Ko("DELETE");function es(){const t=ns(...arguments);return is(t),t}function ns(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<2)return e[0];if(e.length>2)return ns(es(e[0],e[1]),...da(e).call(e,2));const r=e[0],i=e[1];if(r instanceof Date&&i instanceof Date)return r.setTime(i.getTime()),r;for(const t of Ta(i))Object.prototype.propertyIsEnumerable.call(i,t)&&(i[t]===ts?delete r[t]:null===r[t]||null===i[t]||"object"!=typeof r[t]||"object"!=typeof i[t]||Ba(r[t])||Ba(i[t])?r[t]=rs(i[t]):r[t]=ns(r[t],i[t]));return r}function rs(t){return Ba(t)?Qa(t).call(t,t=>rs(t)):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):ns({},t):t}function is(t){for(const e of Ka(t))t[e]===ts?delete t[e]:"object"==typeof t[e]&&null!==t[e]&&is(t[e])}var os,as,ss,cs,us,ls,hs,fs={};function ps(){return ss?as:(ss=1,function(){if(os)return fs;os=1;var t=yn(),e=Date,n=O()(e.prototype.getTime);t({target:"Date",stat:!0},{now:function(){return n(new e)}})}(),as=Ft().Date.now)}function ds(){return us?cs:(us=1,cs=ps())}var vs=n(hs?ls:(hs=1,ls=ds()));var gs,ms,ys,bs,ws,Cs,Es,ks,Os,Ts,Fs,_s={};function Ss(){if(ms)return gs;ms=1;var t=O(),e=Nt(),n=Tt(),r=ye(),i=Er(),o=E(),a=Function,s=t([].concat),c=t([].join),u={};return gs=o?a.bind:function(t){var o=e(this),l=o.prototype,h=i(arguments,1),f=function(){var e=s(h,i(arguments));return this instanceof f?function(t,e,n){if(!r(u,e)){for(var i=[],o=0;o<e;o++)i[o]="a["+o+"]";u[e]=a("C,a","return new C("+c(i,",")+")")}return u[e](t,n)}(o,e.length,e):o.apply(t,e)};return n(l)&&(f.prototype=l),f},gs}function Ds(){return ws?bs:(ws=1,function(){if(ys)return _s;ys=1;var t=yn(),e=Ss();t({target:"Function",proto:!0,forced:Function.bind!==e},{bind:e})}(),bs=ea()("Function","bind"))}function Ps(){if(Es)return Cs;Es=1;var t=St(),e=Ds(),n=Function.prototype;return Cs=function(r){var i=r.bind;return r===n||t(n,r)&&i===n.bind?e:i}}function xs(){return Os?ks:(Os=1,ks=Ps())}var As,js,Rs,Ns,Is,Bs,Ms,Ls,zs,Hs,Ws,Vs,qs,Us=n(Fs?Ts:(Fs=1,Ts=xs())),Ys={};function Xs(){if(js)return As;js=1;var t=C();return As=function(e,n){var r=[][e];return!!r&&t(function(){r.call(null,n||function(){return 1},1)})}}function Gs(){if(Ns)return Rs;Ns=1;var t=ri().forEach,e=Xs()("forEach");return Rs=e?[].forEach:function(e){return t(this,e,arguments.length>1?arguments[1]:void 0)},Rs}function Qs(){return Ms?Bs:(Ms=1,function(){if(Is)return Ys;Is=1;var t=yn(),e=Gs();t({target:"Array",proto:!0,forced:[].forEach!==e},{forEach:e})}(),Bs=ea()("Array","forEach"))}function Js(){return zs?Ls:(zs=1,Ls=Qs())}function $s(){if(Ws)return Hs;Ws=1;var t=_n(),e=ye(),n=St(),r=Js(),i=Array.prototype,o={DOMTokenList:!0,NodeList:!0};return Hs=function(a){var s=a.forEach;return a===i||n(i,a)&&s===i.forEach||e(o,t(a))?r:s}}var Zs,Ks,tc,ec,nc,rc,ic,oc,ac,sc=n(qs?Vs:(qs=1,Vs=$s())),cc={};function uc(){return tc?Ks:(tc=1,function(){if(Zs)return cc;Zs=1;var t=yn(),e=O(),n=bn(),r=e([].reverse),i=[1,2];t({target:"Array",proto:!0,forced:String(i)===String(i.reverse())},{reverse:function(){return n(this)&&(this.length=this.length),r(this)}})}(),Ks=ea()("Array","reverse"))}function lc(){if(nc)return ec;nc=1;var t=St(),e=uc(),n=Array.prototype;return ec=function(r){var i=r.reverse;return r===n||t(n,r)&&i===n.reverse?e:i}}function hc(){return ic?rc:(ic=1,rc=lc())}var fc,pc,dc,vc,gc,mc,yc,bc,wc,Cc,Ec,kc,Oc,Tc=n(ac?oc:(ac=1,oc=hc())),Fc={};function _c(){if(pc)return fc;pc=1;var t=j(),e=bn(),n=TypeError,r=Object.getOwnPropertyDescriptor,i=t&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();return fc=i?function(t,i){if(e(t)&&!r(t,"length").writable)throw new n("Cannot set read only .length");return t.length=i}:function(t,e){return t.length=e}}function Sc(){if(vc)return dc;vc=1;var t=Rt(),e=TypeError;return dc=function(n,r){if(!delete n[r])throw new e("Cannot delete property "+t(r)+" of "+t(n))}}function Dc(){return yc?mc:(yc=1,function(){if(gc)return Fc;gc=1;var t=yn(),e=me(),n=or(),r=Cn(),i=kn(),o=_c(),a=On(),s=xn(),c=Tn(),u=Sc(),l=An()("splice"),h=Math.max,f=Math.min;t({target:"Array",proto:!0,forced:!l},{splice:function(t,l){var p,d,v,g,m,y,b=e(this),w=i(b),C=n(t,w),E=arguments.length;for(0===E?p=d=0:1===E?(p=0,d=w-C):(p=E-2,d=f(h(r(l),0),w-C)),a(w+p-d),v=s(b,d),g=0;g<d;g++)(m=C+g)in b&&c(v,g,b[m]);if(v.length=d,p<d){for(g=C;g<w-d;g++)y=g+p,(m=g+d)in b?b[y]=b[m]:u(b,y);for(g=w;g>w-d+p;g--)u(b,g-1)}else if(p>d)for(g=w-d;g>C;g--)y=g+p-1,(m=g+d-1)in b?b[y]=b[m]:u(b,y);for(g=0;g<p;g++)b[g+C]=arguments[g+2];return o(b,w-d+p),v}})}(),mc=ea()("Array","splice"))}function Pc(){if(wc)return bc;wc=1;var t=St(),e=Dc(),n=Array.prototype;return bc=function(r){var i=r.splice;return r===n||t(n,r)&&i===n.splice?e:i}}function xc(){return Ec?Cc:(Ec=1,Cc=Pc())}var Ac,jc=n(Oc?kc:(Oc=1,kc=xc())),Rc={exports:{}};function Nc(){return Ac||(Ac=1,function(t){function e(t){if(t)return function(t){return Object.assign(t,e.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}e.prototype.on=function(t,e){const n=this._callbacks.get(t)??[];return n.push(e),this._callbacks.set(t,n),this},e.prototype.once=function(t,e){const n=(...r)=>{this.off(t,n),e.apply(this,r)};return n.fn=e,this.on(t,n),this},e.prototype.off=function(t,e){if(void 0===t&&void 0===e)return this._callbacks.clear(),this;if(void 0===e)return this._callbacks.delete(t),this;const n=this._callbacks.get(t);if(n){for(const[t,r]of n.entries())if(r===e||r.fn===e){n.splice(t,1);break}0===n.length?this._callbacks.delete(t):this._callbacks.set(t,n)}return this},e.prototype.emit=function(t,...e){const n=this._callbacks.get(t);if(n){const t=[...n];for(const n of t)n.apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks.get(t)??[]},e.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let e=0;for(const t of this._callbacks.values())e+=t.length;return e},e.prototype.hasListeners=function(t){return this.listenerCount(t)>0},e.prototype.addEventListener=e.prototype.on,e.prototype.removeListener=e.prototype.off,e.prototype.removeEventListener=e.prototype.off,e.prototype.removeAllListeners=e.prototype.off,t.exports=e}(Rc)),Rc.exports}var Ic,Bc=n(Nc());
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
	 * http://naver.github.io/egjs
	 *
	 * Forked By Naver egjs
	 * Copyright (c) hammerjs
	 * Licensed under the MIT license */
function Mc(){return Mc=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Mc.apply(this,arguments)}function Lc(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function zc(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}Ic="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var r=arguments[n];if(null!=r)for(var i in r)r.hasOwnProperty(i)&&(e[i]=r[i])}return e}:Object.assign;var Hc,Wc=Ic,Vc=["","webkit","Moz","MS","ms","o"],qc="undefined"==typeof document?{style:{}}:document.createElement("div"),Uc=Math.round,Yc=Math.abs,Xc=Date.now;function Gc(t,e){for(var n,r,i=e[0].toUpperCase()+e.slice(1),o=0;o<Vc.length;){if((r=(n=Vc[o])?n+i:e)in t)return r;o++}}Hc="undefined"==typeof window?{}:window;var Qc=Gc(qc.style,"touchAction"),Jc=void 0!==Qc;var $c="compute",Zc="auto",Kc="manipulation",tu="none",eu="pan-x",nu="pan-y",ru=function(){if(!Jc)return!1;var t={},e=Hc.CSS&&Hc.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(n){return t[n]=!e||Hc.CSS.supports("touch-action",n)}),t}(),iu="ontouchstart"in Hc,ou=void 0!==Gc(Hc,"PointerEvent"),au=iu&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),su="touch",cu="mouse",uu=16,lu=24,hu=["x","y"],fu=["clientX","clientY"];function pu(t,e,n){var r;if(t)if(t.forEach)t.forEach(e,n);else if(void 0!==t.length)for(r=0;r<t.length;)e.call(n,t[r],r,t),r++;else for(r in t)t.hasOwnProperty(r)&&e.call(n,t[r],r,t)}function du(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function vu(t,e){return t.indexOf(e)>-1}var gu=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===$c&&(t=this.compute()),Jc&&this.manager.element.style&&ru[t]&&(this.manager.element.style[Qc]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return pu(this.manager.recognizers,function(e){du(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),function(t){if(vu(t,tu))return tu;var e=vu(t,eu),n=vu(t,nu);return e&&n?tu:e||n?e?eu:nu:vu(t,Kc)?Kc:Zc}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var r=this.actions,i=vu(r,tu)&&!ru[tu],o=vu(r,nu)&&!ru[nu],a=vu(r,eu)&&!ru[eu];if(i){var s=1===t.pointers.length,c=t.distance<2,u=t.deltaTime<250;if(s&&c&&u)return}if(!a||!o)return i||o&&6&n||a&&n&lu?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function mu(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function yu(t){var e=t.length;if(1===e)return{x:Uc(t[0].clientX),y:Uc(t[0].clientY)};for(var n=0,r=0,i=0;i<e;)n+=t[i].clientX,r+=t[i].clientY,i++;return{x:Uc(n/e),y:Uc(r/e)}}function bu(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:Uc(t.pointers[n].clientX),clientY:Uc(t.pointers[n].clientY)},n++;return{timeStamp:Xc(),pointers:e,center:yu(e),deltaX:t.deltaX,deltaY:t.deltaY}}function wu(t,e,n){n||(n=hu);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return Math.sqrt(r*r+i*i)}function Cu(t,e,n){n||(n=hu);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return 180*Math.atan2(i,r)/Math.PI}function Eu(t,e){return t===e?1:Yc(t)>=Yc(e)?t<0?2:4:e<0?8:uu}function ku(t,e,n){return{x:e/t||0,y:n/t||0}}function Ou(t,e){var n=t.session,r=e.pointers,i=r.length;n.firstInput||(n.firstInput=bu(e)),i>1&&!n.firstMultiple?n.firstMultiple=bu(e):1===i&&(n.firstMultiple=!1);var o=n.firstInput,a=n.firstMultiple,s=a?a.center:o.center,c=e.center=yu(r);e.timeStamp=Xc(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=Cu(s,c),e.distance=wu(s,c),function(t,e){var n=e.center,r=t.offsetDelta||{},i=t.prevDelta||{},o=t.prevInput||{};1!==e.eventType&&4!==o.eventType||(i=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},r=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=i.x+(n.x-r.x),e.deltaY=i.y+(n.y-r.y)}(n,e),e.offsetDirection=Eu(e.deltaX,e.deltaY);var u,l,h=ku(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=h.x,e.overallVelocityY=h.y,e.overallVelocity=Yc(h.x)>Yc(h.y)?h.x:h.y,e.scale=a?(u=a.pointers,wu((l=r)[0],l[1],fu)/wu(u[0],u[1],fu)):1,e.rotation=a?function(t,e){return Cu(e[1],e[0],fu)+Cu(t[1],t[0],fu)}(a.pointers,r):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,r,i,o,a=t.lastInterval||e,s=e.timeStamp-a.timeStamp;if(8!==e.eventType&&(s>25||void 0===a.velocity)){var c=e.deltaX-a.deltaX,u=e.deltaY-a.deltaY,l=ku(s,c,u);r=l.x,i=l.y,n=Yc(l.x)>Yc(l.y)?l.x:l.y,o=Eu(c,u),t.lastInterval=e}else n=a.velocity,r=a.velocityX,i=a.velocityY,o=a.direction;e.velocity=n,e.velocityX=r,e.velocityY=i,e.direction=o}(n,e);var f,p=t.element,d=e.srcEvent;mu(f=d.composedPath?d.composedPath()[0]:d.path?d.path[0]:d.target,p)&&(p=f),e.target=p}function Tu(t,e,n){var r=n.pointers.length,i=n.changedPointers.length,o=1&e&&r-i===0,a=12&e&&r-i===0;n.isFirst=!!o,n.isFinal=!!a,o&&(t.session={}),n.eventType=e,Ou(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function Fu(t){return t.trim().split(/\s+/g)}function _u(t,e,n){pu(Fu(e),function(e){t.addEventListener(e,n,!1)})}function Su(t,e,n){pu(Fu(e),function(e){t.removeEventListener(e,n,!1)})}function Du(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var Pu=function(){function t(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){du(t.options.enable,[t])&&n.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&_u(this.element,this.evEl,this.domHandler),this.evTarget&&_u(this.target,this.evTarget,this.domHandler),this.evWin&&_u(Du(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&Su(this.element,this.evEl,this.domHandler),this.evTarget&&Su(this.target,this.evTarget,this.domHandler),this.evWin&&Su(Du(this.element),this.evWin,this.domHandler)},t}();function xu(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var r=0;r<t.length;){if(n&&t[r][n]==e||!n&&t[r]===e)return r;r++}return-1}var Au={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},ju={2:su,3:"pen",4:cu,5:"kinect"},Ru="pointerdown",Nu="pointermove pointerup pointercancel";Hc.MSPointerEvent&&!Hc.PointerEvent&&(Ru="MSPointerDown",Nu="MSPointerMove MSPointerUp MSPointerCancel");var Iu=function(t){function e(){var n,r=e.prototype;return r.evEl=Ru,r.evWin=Nu,(n=t.apply(this,arguments)||this).store=n.manager.session.pointerEvents=[],n}return Lc(e,t),e.prototype.handler=function(t){var e=this.store,n=!1,r=t.type.toLowerCase().replace("ms",""),i=Au[r],o=ju[t.pointerType]||t.pointerType,a=o===su,s=xu(e,t.pointerId,"pointerId");1&i&&(0===t.button||a)?s<0&&(e.push(t),s=e.length-1):12&i&&(n=!0),s<0||(e[s]=t,this.callback(this.manager,i,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(s,1))},e}(Pu);function Bu(t){return Array.prototype.slice.call(t,0)}function Mu(t,e,n){for(var r=[],i=[],o=0;o<t.length;){var a=e?t[o][e]:t[o];xu(i,a)<0&&r.push(t[o]),i[o]=a,o++}return n&&(r=e?r.sort(function(t,n){return t[e]>n[e]}):r.sort()),r}var Lu={touchstart:1,touchmove:2,touchend:4,touchcancel:8},zu=function(t){function e(){var n;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).targetIds={},n}return Lc(e,t),e.prototype.handler=function(t){var e=Lu[t.type],n=Hu.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:su,srcEvent:t})},e}(Pu);function Hu(t,e){var n,r,i=Bu(t.touches),o=this.targetIds;if(3&e&&1===i.length)return o[i[0].identifier]=!0,[i,i];var a=Bu(t.changedTouches),s=[],c=this.target;if(r=i.filter(function(t){return mu(t.target,c)}),1===e)for(n=0;n<r.length;)o[r[n].identifier]=!0,n++;for(n=0;n<a.length;)o[a[n].identifier]&&s.push(a[n]),12&e&&delete o[a[n].identifier],n++;return s.length?[Mu(r.concat(s),"identifier",!0),s]:void 0}var Wu={mousedown:1,mousemove:2,mouseup:4},Vu=function(t){function e(){var n,r=e.prototype;return r.evEl="mousedown",r.evWin="mousemove mouseup",(n=t.apply(this,arguments)||this).pressed=!1,n}return Lc(e,t),e.prototype.handler=function(t){var e=Wu[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:cu,srcEvent:t}))},e}(Pu);function qu(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY},r=this.lastTouches;this.lastTouches.push(n);setTimeout(function(){var t=r.indexOf(n);t>-1&&r.splice(t,1)},2500)}}function Uu(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,qu.call(this,e)):12&t&&qu.call(this,e)}function Yu(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,r=0;r<this.lastTouches.length;r++){var i=this.lastTouches[r],o=Math.abs(e-i.x),a=Math.abs(n-i.y);if(o<=25&&a<=25)return!0}return!1}var Xu=function(){return function(t){function e(e,n){var r;return(r=t.call(this,e,n)||this).handler=function(t,e,n){var i=n.pointerType===su,o=n.pointerType===cu;if(!(o&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)Uu.call(zc(zc(r)),e,n);else if(o&&Yu.call(zc(zc(r)),n))return;r.callback(t,e,n)}},r.touch=new zu(r.manager,r.handler),r.mouse=new Vu(r.manager,r.handler),r.primaryTouch=null,r.lastTouches=[],r}return Lc(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(Pu)}();function Gu(t,e,n){return!!Array.isArray(t)&&(pu(t,n[e],n),!0)}var Qu=32,Ju=1;function $u(t,e){var n=e.manager;return n?n.get(t):t}function Zu(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var Ku=function(){function t(t){void 0===t&&(t={}),this.options=Mc({enable:!0},t),this.id=Ju++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return Wc(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(Gu(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=$u(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return Gu(t,"dropRecognizeWith",this)||(t=$u(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(Gu(t,"requireFailure",this))return this;var e=this.requireFail;return-1===xu(e,t=$u(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(Gu(t,"dropRequireFailure",this))return this;t=$u(t,this);var e=xu(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,n=this.state;function r(n){e.manager.emit(n,t)}n<8&&r(e.options.event+Zu(n)),r(e.options.event),t.additionalEvent&&r(t.additionalEvent),n>=8&&r(e.options.event+Zu(n))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=Qu},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=Wc({},t);if(!du(this.options.enable,[this,e]))return this.reset(),void(this.state=Qu);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),tl=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Mc({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,n.pCenter=!1,n._timer=null,n._input=null,n.count=0,n}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Kc]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime<n.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(i&&o&&r){if(4!==t.eventType)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<n.interval,s=!this.pCenter||wu(this.pCenter,t.center)<n.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,s&&a?this.count+=1:this.count=1,this._input=t,0===this.count%n.taps)return this.hasRequireFailures()?(this._timer=setTimeout(function(){e.state=8,e.tryEmit()},n.interval),2):8}return Qu},n.failTimeout=function(){var t=this;return this._timer=setTimeout(function(){t.state=Qu},this.options.interval),Qu},n.reset=function(){clearTimeout(this._timer)},n.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(Ku),el=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({pointers:1},e))||this}Lc(e,t);var n=e.prototype;return n.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},n.process=function(t){var e=this.state,n=t.eventType,r=6&e,i=this.attrTest(t);return r&&(8&n||!i)?16|e:r||i?4&n?8|e:2&e?4|e:2:Qu},e}(Ku);function nl(t){return t===uu?"down":8===t?"up":2===t?"left":4===t?"right":""}var rl=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Mc({event:"pan",threshold:10,pointers:1,direction:30},e))||this).pX=null,n.pY=null,n}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){var t=this.options.direction,e=[];return 6&t&&e.push(nu),t&lu&&e.push(eu),e},n.directionTest=function(t){var e=this.options,n=!0,r=t.distance,i=t.direction,o=t.deltaX,a=t.deltaY;return i&e.direction||(6&e.direction?(i=0===o?1:o<0?2:4,n=o!==this.pX,r=Math.abs(t.deltaX)):(i=0===a?1:a<0?8:uu,n=a!==this.pY,r=Math.abs(t.deltaY))),t.direction=i,n&&r>e.threshold&&i&e.direction},n.attrTest=function(t){return el.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},n.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var n=nl(e.direction);n&&(e.additionalEvent=this.options.event+n),t.prototype.emit.call(this,e)},e}(el),il=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},e))||this}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return rl.prototype.getTouchAction.call(this)},n.attrTest=function(e){var n,r=this.options.direction;return 30&r?n=e.overallVelocity:6&r?n=e.overallVelocityX:r&lu&&(n=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&r&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Yc(n)>this.options.velocity&&4&e.eventType},n.emit=function(t){var e=nl(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(el),ol=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({event:"pinch",threshold:0,pointers:2},e))||this}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[tu]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},n.emit=function(e){if(1!==e.scale){var n=e.scale<1?"in":"out";e.additionalEvent=this.options.event+n}t.prototype.emit.call(this,e)},e}(el),al=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({event:"rotate",threshold:0,pointers:2},e))||this}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[tu]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(el),sl=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Mc({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,n._input=null,n}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Zc]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime>n.time;if(this._input=t,!i||!r||12&t.eventType&&!o)this.reset();else if(1&t.eventType)this.reset(),this._timer=setTimeout(function(){e.state=8,e.tryEmit()},n.time);else if(4&t.eventType)return 8;return Qu},n.reset=function(){clearTimeout(this._timer)},n.emit=function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Xc(),this.manager.emit(this.options.event,this._input)))},e}(Ku),cl={domEvents:!1,touchAction:$c,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},ul=[[al,{enable:!1}],[ol,{enable:!1},["rotate"]],[il,{direction:6}],[rl,{direction:6},["swipe"]],[tl],[tl,{event:"doubletap",taps:2},["tap"]],[sl]];function ll(t,e){var n,r=t.element;r.style&&(pu(t.options.cssProps,function(i,o){n=Gc(r.style,o),e?(t.oldCssProps[n]=r.style[n],r.style[n]=i):r.style[n]=t.oldCssProps[n]||""}),e||(t.oldCssProps={}))}var hl=function(){function t(t,e){var n,r=this;this.options=Wc({},cl,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((n=this).options.inputClass||(ou?Iu:au?zu:iu?Xu:Vu))(n,Tu),this.touchAction=new gu(this,this.options.touchAction),ll(this,!0),pu(this.options.recognizers,function(t){var e=r.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}var e=t.prototype;return e.set=function(t){return Wc(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var r=this.recognizers,i=e.curRecognizer;(!i||i&&8&i.state)&&(e.curRecognizer=null,i=null);for(var o=0;o<r.length;)n=r[o],2===e.stopped||i&&n!==i&&!n.canRecognizeWith(i)?n.reset():n.recognize(t),!i&&14&n.state&&(e.curRecognizer=n,i=n),o++}},e.get=function(t){if(t instanceof Ku)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event===t)return e[n];return null},e.add=function(t){if(Gu(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(Gu(t,"remove",this))return this;var e=this.get(t);if(t){var n=this.recognizers,r=xu(n,e);-1!==r&&(n.splice(r,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var n=this.handlers;return pu(Fu(t),function(t){n[t]=n[t]||[],n[t].push(e)}),this},e.off=function(t,e){if(void 0===t)return this;var n=this.handlers;return pu(Fu(t),function(t){e?n[t]&&n[t].splice(xu(n[t],e),1):delete n[t]}),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var n=document.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var r=0;r<n.length;)n[r](e),r++}},e.destroy=function(){this.element&&ll(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),fl={touchstart:1,touchmove:2,touchend:4,touchcancel:8},pl=function(t){function e(){var n,r=e.prototype;return r.evTarget="touchstart",r.evWin="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).started=!1,n}return Lc(e,t),e.prototype.handler=function(t){var e=fl[t.type];if(1===e&&(this.started=!0),this.started){var n=dl.call(this,t,e);12&e&&n[0].length-n[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:su,srcEvent:t})}},e}(Pu);function dl(t,e){var n=Bu(t.touches),r=Bu(t.changedTouches);return 12&e&&(n=Mu(n.concat(r),"identifier",!0)),[n,r]}function vl(t,e,n){var r="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",i=window.console&&(window.console.warn||window.console.log);return i&&i.call(window.console,r,n),t.apply(this,arguments)}}var gl=vl(function(t,e,n){for(var r=Object.keys(e),i=0;i<r.length;)(!n||n&&void 0===t[r[i]])&&(t[r[i]]=e[r[i]]),i++;return t},"extend","Use `assign`."),ml=vl(function(t,e){return gl(t,e,!0)},"merge","Use `assign`.");function yl(t,e,n){var r,i=e.prototype;(r=t.prototype=Object.create(i)).constructor=t,r._super=i,n&&Wc(r,n)}function bl(t,e){return function(){return t.apply(e,arguments)}}var wl=function(){var t=function(t,e){return void 0===e&&(e={}),new hl(t,Mc({recognizers:ul.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=30,t.DIRECTION_DOWN=uu,t.DIRECTION_LEFT=2,t.DIRECTION_RIGHT=4,t.DIRECTION_UP=8,t.DIRECTION_HORIZONTAL=6,t.DIRECTION_VERTICAL=lu,t.DIRECTION_NONE=1,t.DIRECTION_DOWN=uu,t.INPUT_START=1,t.INPUT_MOVE=2,t.INPUT_END=4,t.INPUT_CANCEL=8,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=Qu,t.Manager=hl,t.Input=Pu,t.TouchAction=gu,t.TouchInput=zu,t.MouseInput=Vu,t.PointerEventInput=Iu,t.TouchMouseInput=Xu,t.SingleTouchInput=pl,t.Recognizer=Ku,t.AttrRecognizer=el,t.Tap=tl,t.Pan=rl,t.Swipe=il,t.Pinch=ol,t.Rotate=al,t.Press=sl,t.on=_u,t.off=Su,t.each=pu,t.merge=ml,t.extend=gl,t.bindFn=bl,t.assign=Wc,t.inherit=yl,t.bindFn=bl,t.prefixed=Gc,t.toArray=Bu,t.inArray=xu,t.uniqueArray=Mu,t.splitStr=Fu,t.boolOrFn=du,t.hasParent=mu,t.addEventListeners=_u,t.removeEventListeners=Su,t.defaults=Wc({},cl,{preset:ul}),t}();wl.defaults;const Cl="undefined"!=typeof window?window.Hammer||wl:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function El(t){var e;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push(()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)});const n=Cl(this._dom.overlay);n.on("tap",Us(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push(()=>{n.destroy()});const r=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];sc(r).call(r,t=>{n.on(t,t=>{t.srcEvent.stopPropagation()})}),document&&document.body&&(this._onClick=e=>{(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push(()=>{document.body.removeEventListener("click",this._onClick)})),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}Bc(El.prototype),El.current=null,El.prototype.destroy=function(){this.deactivate();for(const n of Tc(t=jc(e=this._cleanupQueue).call(e,0)).call(t)){var t,e;n()}},El.prototype.activate=function(){El.current&&El.current.deactivate(),El.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},El.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},El.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var kl,Ol,Tl,Fl,_l,Sl,Dl,Pl,xl,Al,jl,Rl,Nl,Il={};function Bl(){if(Ol)return kl;Ol=1;var t=Cn(),e=Mn(),n=kt(),r=RangeError;return kl=function(i){var o=e(n(this)),a="",s=t(i);if(s<0||s===1/0)throw new r("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(o+=o))1&s&&(a+=o);return a}}function Ml(){if(Fl)return Tl;Fl=1;var t=O(),e=En(),n=Mn(),r=Bl(),i=kt(),o=t(r),a=t("".slice),s=Math.ceil,c=function(t){return function(r,c,u){var l,h,f=n(i(r)),p=e(c),d=f.length,v=void 0===u?" ":n(u);return p<=d||""===v?f:((h=o(v,s((l=p-d)/v.length))).length>l&&(h=a(h,0,l)),t?f+h:h+f)}};return Tl={start:c(!1),end:c(!0)}}function Ll(){if(Sl)return _l;Sl=1;var t=O(),e=C(),n=Ml().start,r=RangeError,i=isFinite,o=Math.abs,a=Date.prototype,s=a.toISOString,c=t(a.getTime),u=t(a.getUTCDate),l=t(a.getUTCFullYear),h=t(a.getUTCHours),f=t(a.getUTCMilliseconds),p=t(a.getUTCMinutes),d=t(a.getUTCMonth),v=t(a.getUTCSeconds);return _l=e(function(){return"0385-07-25T07:06:39.999Z"!==s.call(new Date(-50000000000001))})||!e(function(){s.call(new Date(NaN))})?function(){if(!i(c(this)))throw new r("Invalid time value");var t=this,e=l(t),a=f(t),s=e<0?"-":e>9999?"+":"";return s+n(o(e),s?6:4,0)+"-"+n(d(t)+1,2,0)+"-"+n(u(t),2,0)+"T"+n(h(t),2,0)+":"+n(p(t),2,0)+":"+n(v(t),2,0)+"."+n(a,3,0)+"Z"}:s}function zl(){if(xl)return Pl;xl=1,function(){if(Dl)return Il;Dl=1;var t=yn(),e=R(),n=me(),r=Ce(),i=Ll(),o=T();t({target:"Date",proto:!0,forced:C()(function(){return null!==new Date(NaN).toJSON()||1!==e(Date.prototype.toJSON,{toISOString:function(){return 1}})})},{toJSON:function(t){var a=n(this),s=r(a,"number");return"number"!=typeof s||isFinite(s)?"toISOString"in a||"Date"!==o(a)?a.toISOString():e(i,a):null}})}(),gi();var t=Ft(),e=k();return t.JSON||(t.JSON={stringify:JSON.stringify}),Pl=function(n,r,i){return e(t.JSON.stringify,null,arguments)},Pl}function Hl(){return jl?Al:(jl=1,Al=zl())}var Wl,Vl,ql,Ul,Yl,Xl,Gl,Ql,Jl,$l=n(Nl?Rl:(Nl=1,Rl=Hl())),Zl={};function Kl(){if(Vl)return Wl;Vl=1;var t=j(),e=O(),n=R(),r=C(),i=lr(),o=Pr(),a=bt(),s=me(),c=Ct(),u=Object.assign,l=Object.defineProperty,h=e([].concat);return Wl=!u||r(function(){if(t&&1!==u({b:1},u(l({},"a",{enumerable:!0,get:function(){l(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},n={},r=Symbol("assign detection"),o="abcdefghijklmnopqrst";return e[r]=7,o.split("").forEach(function(t){n[t]=t}),7!==u({},e)[r]||i(u({},n)).join("")!==o})?function(e,r){for(var u=s(e),l=arguments.length,f=1,p=o.f,d=a.f;l>f;)for(var v,g=c(arguments[f++]),m=p?h(i(g),p(g)):i(g),y=m.length,b=0;y>b;)v=m[b++],t&&!n(d,g,v)||(u[v]=g[v]);return u}:u,Wl}function th(){return Yl?Ul:(Yl=1,function(){if(ql)return Zl;ql=1;var t=yn(),e=Kl();t({target:"Object",stat:!0,arity:2,forced:Object.assign!==e},{assign:e})}(),Ul=Ft().Object.assign)}function eh(){return Gl?Xl:(Gl=1,Xl=th())}var nh,rh,ih,oh,ah,sh,ch,uh=n(Jl?Ql:(Jl=1,Ql=eh())),lh={},hh={};function fh(){if(rh)return nh;rh=1;var t=w(),e=Dt(),n=T(),r=function(t){return e.slice(0,t.length)===t};return nh=r("Bun/")?"BUN":r("Cloudflare-Workers")?"CLOUDFLARE":r("Deno/")?"DENO":r("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===n(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function ph(){if(oh)return ih;oh=1;var t=TypeError;return ih=function(e,n){if(e<n)throw new t("Not enough arguments");return e}}function dh(){if(sh)return ah;sh=1;var t,e=w(),n=k(),r=_(),i=fh(),o=Dt(),a=Er(),s=ph(),c=e.Function,u=/MSIE .\./.test(o)||"BUN"===i&&((t=e.Bun.version.split(".")).length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2]));return ah=function(t,e){var i=e?2:1;return u?function(o,u){var l=s(arguments.length,1)>i,h=r(o)?o:c(o),f=l?a(arguments,i):[],p=l?function(){n(h,this,f)}:h;return e?t(p,u):t(p)}:t},ah}var vh,gh,mh,yh,bh,wh,Ch={};function Eh(){return gh||(gh=1,function(){if(ch)return hh;ch=1;var t=yn(),e=w(),n=dh()(e.setInterval,!0);t({global:!0,bind:!0,forced:e.setInterval!==n},{setInterval:n})}(),function(){if(vh)return Ch;vh=1;var t=yn(),e=w(),n=dh()(e.setTimeout,!0);t({global:!0,bind:!0,forced:e.setTimeout!==n},{setTimeout:n})}()),lh}function kh(){return yh?mh:(yh=1,Eh(),mh=Ft().setTimeout)}var Oh,Th,Fh,_h,Sh,Dh,Ph,xh,Ah,jh,Rh,Nh=n(wh?bh:(wh=1,bh=kh())),Ih={};function Bh(){if(Th)return Oh;Th=1;var t=me(),e=or(),n=kn();return Oh=function(r){for(var i=t(this),o=n(i),a=arguments.length,s=e(a>1?arguments[1]:void 0,o),c=a>2?arguments[2]:void 0,u=void 0===c?o:e(c,o);u>s;)i[s++]=r;return i},Oh}function Mh(){return Sh?_h:(Sh=1,function(){if(Fh)return Ih;Fh=1;var t=yn(),e=Bh(),n=Fo();t({target:"Array",proto:!0},{fill:e}),n("fill")}(),_h=ea()("Array","fill"))}function Lh(){if(Ph)return Dh;Ph=1;var t=St(),e=Mh(),n=Array.prototype;return Dh=function(r){var i=r.fill;return r===n||t(n,r)&&i===n.fill?e:i}}function zh(){return Ah?xh:(Ah=1,xh=Lh())}var Hh,Wh,Vh,qh=n(Rh?jh:(Rh=1,jh=zh())),Uh={};function Yh(){return Vh?Wh:(Vh=1,function(){if(Hh)return Uh;Hh=1;var t=yn(),e=ar().includes,n=C(),r=Fo();t({target:"Array",proto:!0,forced:n(function(){return!Array(1).includes()})},{includes:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}}),r("includes")}(),Wh=ea()("Array","includes"))}var Xh,Gh,Qh,Jh,$h,Zh,Kh,tf,ef,nf,rf,of,af,sf,cf,uf={};function lf(){if(Gh)return Xh;Gh=1;var t=Tt(),e=T(),n=we()("match");return Xh=function(r){var i;return t(r)&&(void 0!==(i=r[n])?!!i:"RegExp"===e(r))}}function hf(){if(Jh)return Qh;Jh=1;var t=lf(),e=TypeError;return Qh=function(n){if(t(n))throw new e("The method doesn't accept regular expressions");return n}}function ff(){if(Zh)return $h;Zh=1;var t=we()("match");return $h=function(e){var n=/./;try{"/./"[e](n)}catch(r){try{return n[t]=!1,"/./"[e](n)}catch(t){}}return!1}}function pf(){return ef?tf:(ef=1,function(){if(Kh)return uf;Kh=1;var t=yn(),e=O(),n=hf(),r=kt(),i=Mn(),o=ff(),a=e("".indexOf);t({target:"String",proto:!0,forced:!o("includes")},{includes:function(t){return!!~a(i(r(this)),i(n(t)),arguments.length>1?arguments[1]:void 0)}})}(),tf=ea()("String","includes"))}function df(){if(rf)return nf;rf=1;var t=St(),e=Yh(),n=pf(),r=Array.prototype,i=String.prototype;return nf=function(o){var a=o.includes;return o===r||t(r,o)&&a===r.includes?e:"string"==typeof o||o===i||t(i,o)&&a===i.includes?n:a}}function vf(){return af?of:(af=1,of=df())}var gf,mf,yf,bf,wf,Cf,Ef,kf=n(cf?sf:(cf=1,sf=vf())),Of={};function Tf(){return yf?mf:(yf=1,function(){if(gf)return Of;gf=1;var t=yn(),e=C(),n=me(),r=Po(),i=Do();t({target:"Object",stat:!0,forced:e(function(){r(1)}),sham:!i},{getPrototypeOf:function(t){return r(n(t))}})}(),mf=Ft().Object.getPrototypeOf)}function Ff(){return wf?bf:(wf=1,bf=Tf())}var _f,Sf,Df,Pf,xf,Af,jf,Rf,Nf=n(Ef?Cf:(Ef=1,Cf=Ff()));function If(){return Sf?_f:(Sf=1,jn(),_f=ea()("Array","concat"))}function Bf(){if(Pf)return Df;Pf=1;var t=St(),e=If(),n=Array.prototype;return Df=function(r){var i=r.concat;return r===n||t(n,r)&&i===n.concat?e:i}}function Mf(){return Af?xf:(Af=1,xf=Bf())}var Lf,zf,Hf,Wf,Vf,qf,Uf,Yf,Xf,Gf=n(Rf?jf:(Rf=1,jf=Mf())),Qf={};function Jf(){return Hf?zf:(Hf=1,function(){if(Lf)return Qf;Lf=1;var t=yn(),e=ri().filter;t({target:"Array",proto:!0,forced:!An()("filter")},{filter:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),zf=ea()("Array","filter"))}function $f(){if(Vf)return Wf;Vf=1;var t=St(),e=Jf(),n=Array.prototype;return Wf=function(r){var i=r.filter;return r===n||t(n,r)&&i===n.filter?e:i}}function Zf(){return Uf?qf:(Uf=1,qf=$f())}var Kf,tp,ep,np,rp,ip,op,ap,sp,cp=n(Xf?Yf:(Xf=1,Yf=Zf())),up={};function lp(){if(tp)return Kf;tp=1;var t=j(),e=C(),n=O(),r=Po(),i=lr(),o=Ot(),a=n(bt().f),s=n([].push),c=t&&e(function(){var t=Object.create(null);return t[2]=2,!a(t,2)}),u=function(e){return function(n){for(var u,l=o(n),h=i(l),f=c&&null===r(l),p=h.length,d=0,v=[];p>d;)u=h[d++],t&&!(f?u in l:a(l,u))||s(v,e?[u,l[u]]:l[u]);return v}};return Kf={entries:u(!0),values:u(!1)}}function hp(){return rp?np:(rp=1,function(){if(ep)return up;ep=1;var t=yn(),e=lp().values;t({target:"Object",stat:!0},{values:function(t){return e(t)}})}(),np=Ft().Object.values)}function fp(){return op?ip:(op=1,ip=hp())}var pp,dp,vp,gp,mp,yp,bp,wp,Cp,Ep,kp,Op,Tp,Fp=n(sp?ap:(sp=1,ap=fp())),_p={};function Sp(){return dp?pp:(dp=1,pp="\t\n\v\f\r                　\u2028\u2029\ufeff")}function Dp(){if(gp)return vp;gp=1;var t=O(),e=kt(),n=Mn(),r=Sp(),i=t("".replace),o=RegExp("^["+r+"]+"),a=RegExp("(^|[^"+r+"])["+r+"]+$"),s=function(t){return function(r){var s=n(e(r));return 1&t&&(s=i(s,o,"")),2&t&&(s=i(s,a,"$1")),s}};return vp={start:s(1),end:s(2),trim:s(3)}}function Pp(){if(yp)return mp;yp=1;var t=w(),e=C(),n=O(),r=Mn(),i=Dp().trim,o=Sp(),a=t.parseInt,s=t.Symbol,c=s&&s.iterator,u=/^[+-]?0x/i,l=n(u.exec),h=8!==a(o+"08")||22!==a(o+"0x16")||c&&!e(function(){a(Object(c))});return mp=h?function(t,e){var n=i(r(t));return a(n,e>>>0||(l(u,n)?16:10))}:a}function xp(){return Cp?wp:(Cp=1,function(){if(bp)return _p;bp=1;var t=yn(),e=Pp();t({global:!0,forced:parseInt!==e},{parseInt:e})}(),wp=Ft().parseInt)}function Ap(){return kp?Ep:(kp=1,Ep=xp())}var jp,Rp,Np,Ip,Bp,Mp,Lp,zp,Hp,Wp=n(Tp?Op:(Tp=1,Op=Ap())),Vp={};function qp(){return Np?Rp:(Np=1,function(){if(jp)return Vp;jp=1;var t=yn(),e=F(),n=ar().indexOf,r=Xs(),i=e([].indexOf),o=!!i&&1/i([1],1,-0)<0;t({target:"Array",proto:!0,forced:o||!r("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return o?i(this,t,e)||0:n(this,t,e)}})}(),Rp=ea()("Array","indexOf"))}function Up(){if(Bp)return Ip;Bp=1;var t=St(),e=qp(),n=Array.prototype;return Ip=function(r){var i=r.indexOf;return r===n||t(n,r)&&i===n.indexOf?e:i}}function Yp(){return Lp?Mp:(Lp=1,Mp=Up())}var Xp,Gp,Qp,Jp,$p,Zp,Kp,td=n(Hp?zp:(Hp=1,zp=Yp())),ed={};function nd(){return Qp?Gp:(Qp=1,function(){if(Xp)return ed;Xp=1;var t=yn(),e=lp().entries;t({target:"Object",stat:!0},{entries:function(t){return e(t)}})}(),Gp=Ft().Object.entries)}function rd(){return $p?Jp:($p=1,Jp=nd())}var id,od,ad,sd,cd,ud,ld,hd=n(Kp?Zp:(Kp=1,Zp=rd()));function fd(){if(ad)return od;ad=1,id||(id=1,yn()({target:"Object",stat:!0,sham:!j()},{create:dr()}));var t=Ft().Object;return od=function(e,n){return t.create(e,n)}}function pd(){return cd?sd:(cd=1,sd=fd())}var dd=n(ld?ud:(ld=1,ud=pd()));const vd=/^\/?Date\((-?\d+)/i,gd=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,md=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,yd=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,bd=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function wd(t){return t instanceof Number||"number"==typeof t}function Cd(t){return t instanceof String||"string"==typeof t}function Ed(t){return"object"==typeof t&&null!==t}function kd(t,e,n,r){let i=!1;!0===r&&(i=null===e[n]&&void 0!==t[n]),i?delete t[n]:t[n]=e[n]}const Od=uh;function Td(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];for(const o in e)if(Object.prototype.hasOwnProperty.call(e,o)||!0===n)if("object"==typeof e[o]&&null!==e[o]&&Nf(e[o])===Object.prototype)void 0===t[o]?t[o]=Td({},e[o],n):"object"==typeof t[o]&&null!==t[o]&&Nf(t[o])===Object.prototype?Td(t[o],e[o],n):kd(t,e,o,r);else if(Ba(e[o])){var i;t[o]=da(i=e[o]).call(i)}else kd(t,e,o,r);return t}function Fd(t,e){return[...t,e]}function _d(t){return da(t).call(t)}const Sd=Fp;const Dd={asBoolean:(t,e)=>("function"==typeof t&&(t=t()),null!=t?0!=t:e||null),asNumber:(t,e)=>("function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null),asString:(t,e)=>("function"==typeof t&&(t=t()),null!=t?String(t):e||null),asSize:(t,e)=>("function"==typeof t&&(t=t()),Cd(t)?t:wd(t)?t+"px":e||null),asElement:(t,e)=>("function"==typeof t&&(t=t()),t||e||null)};function Pd(t){let e;switch(t.length){case 3:case 4:return e=md.exec(t),e?{r:Wp(e[1]+e[1],16),g:Wp(e[2]+e[2],16),b:Wp(e[3]+e[3],16)}:null;case 6:case 7:return e=gd.exec(t),e?{r:Wp(e[1],16),g:Wp(e[2],16),b:Wp(e[3],16)}:null;default:return null}}function xd(t,e,n){var r;return"#"+da(r=((1<<24)+(t<<16)+(e<<8)+n).toString(16)).call(r,1)}function Ad(t,e,n){t/=255,e/=255,n/=255;const r=Math.min(t,Math.min(e,n)),i=Math.max(t,Math.max(e,n));if(r===i)return{h:0,s:0,v:r};return{h:60*((t===r?3:n===r?1:5)-(t===r?e-n:n===r?t-e:n-t)/(i-r))/360,s:(i-r)/i,v:i}}function jd(t){const e=document.createElement("div"),n={};e.style.cssText=t;for(let t=0;t<e.style.length;++t)n[e.style[t]]=e.style.getPropertyValue(e.style[t]);return n}function Rd(t,e,n){let r,i,o;const a=Math.floor(6*t),s=6*t-a,c=n*(1-e),u=n*(1-s*e),l=n*(1-(1-s)*e);switch(a%6){case 0:r=n,i=l,o=c;break;case 1:r=u,i=n,o=c;break;case 2:r=c,i=n,o=l;break;case 3:r=c,i=u,o=n;break;case 4:r=l,i=c,o=n;break;case 5:r=n,i=c,o=u}return{r:Math.floor(255*r),g:Math.floor(255*i),b:Math.floor(255*o)}}function Nd(t,e,n){const r=Rd(t,e,n);return xd(r.r,r.g,r.b)}function Id(t){const e=Pd(t);if(!e)throw new TypeError("'".concat(t,"' is not a valid color."));return Ad(e.r,e.g,e.b)}function Bd(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function Md(t){return yd.test(t)}function Ld(t){return bd.test(t)}function zd(t){if(null===t||"object"!=typeof t)return null;if(t instanceof Element)return t;const e=dd(t);for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&"object"==typeof t[n]&&(e[n]=zd(t[n]));return e}const Hd={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t};const Wd={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};let Vd=class{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=()=>{},this.closeCallback=()=>{},this._create()}insertTo(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}setUpdateCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}setCloseCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}_isColorString(t){if("string"==typeof t)return Wd[t]}setColor(t){let e,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("none"===t)return;const r=this._isColorString(t);if(void 0!==r&&(t=r),!0===Cd(t)){if(!0===Md(t)){const n=t.substr(4).substr(0,t.length-5).split(",");e={r:n[0],g:n[1],b:n[2],a:1}}else if(!0===Ld(t)){const n=t.substr(5).substr(0,t.length-6).split(",");e={r:n[0],g:n[1],b:n[2],a:n[3]}}else if(!0===Bd(t)){const n=Pd(t);e={r:n.r,g:n.g,b:n.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){const n=void 0!==t.a?t.a:"1.0";e={r:t.r,g:t.g,b:t.b,a:n}}if(void 0===e)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+$l(t));this._setColor(e,n)}show(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}_hide(){!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.previousColor=uh({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",Nh(()=>{void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0)},0)}_save(){this.updateCallback(this.color),this.applied=!1,this._hide()}_apply(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}_loadLast(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}_setColor(t){!0===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(this.initialColor=uh({},t)),this.color=t;const e=Ad(t.r,t.g,t.b),n=2*Math.PI,r=this.r*e.s,i=this.centerCoordinates.x+r*Math.sin(n*e.h),o=this.centerCoordinates.y+r*Math.cos(n*e.h);this.colorPickerSelector.style.left=i-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=o-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}_setOpacity(t){this.color.a=t/100,this._updatePicker(this.color)}_setBrightness(t){const e=Ad(this.color.r,this.color.g,this.color.b);e.v=t/100;const n=Rd(e.h,e.s,e.v);n.a=this.color.a,this.color=n,this._updatePicker()}_updatePicker(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.color;const e=Ad(t.r,t.g,t.b),n=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(n.webkitBackingStorePixelRatio||n.mozBackingStorePixelRatio||n.msBackingStorePixelRatio||n.oBackingStorePixelRatio||n.backingStorePixelRatio||1)),n.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const r=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;n.clearRect(0,0,r,i),n.putImageData(this.hueCircle,0,0),n.fillStyle="rgba(0,0,0,"+(1-e.v)+")",n.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),qh(n).call(n),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}_setSize(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}_create(){var t,e,n,r;if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){const t=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);const i=this;this.opacityRange.onchange=function(){i._setOpacity(this.value)},this.opacityRange.oninput=function(){i._setOpacity(this.value)},this.brightnessRange.onchange=function(){i._setBrightness(this.value)},this.brightnessRange.oninput=function(){i._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=Us(t=this._hide).call(t,this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=Us(e=this._apply).call(e,this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=Us(n=this._save).call(n,this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=Us(r=this._loadLast).call(r,this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}_bindHammer(){this.drag={},this.pinch={},this.hammer=new Cl(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",t=>{t.isFirst&&this._moveSelector(t)}),this.hammer.on("tap",t=>{this._moveSelector(t)}),this.hammer.on("panstart",t=>{this._moveSelector(t)}),this.hammer.on("panmove",t=>{this._moveSelector(t)}),this.hammer.on("panend",t=>{this._moveSelector(t)})}_generateHueCircle(){if(!1===this.generated){const t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const e=this.colorPickerCanvas.clientWidth,n=this.colorPickerCanvas.clientHeight;let r,i,o,a;t.clearRect(0,0,e,n),this.centerCoordinates={x:.5*e,y:.5*n},this.r=.49*e;const s=2*Math.PI/360,c=1/360,u=1/this.r;let l;for(o=0;o<360;o++)for(a=0;a<this.r;a++)r=this.centerCoordinates.x+a*Math.sin(s*o),i=this.centerCoordinates.y+a*Math.cos(s*o),l=Rd(o*c,a*u,1),t.fillStyle="rgb("+l.r+","+l.g+","+l.b+")",t.fillRect(r-.5,i-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,e,n)}this.generated=!0}_moveSelector(t){const e=this.colorPickerDiv.getBoundingClientRect(),n=t.center.x-e.left,r=t.center.y-e.top,i=.5*this.colorPickerDiv.clientHeight,o=.5*this.colorPickerDiv.clientWidth,a=n-o,s=r-i,c=Math.atan2(a,s),u=.98*Math.min(Math.sqrt(a*a+s*s),o),l=Math.cos(c)*u+i,h=Math.sin(c)*u+o;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=h-.5*this.colorPickerSelector.clientWidth+"px";let f=c/(2*Math.PI);f=f<0?f+1:f;const p=u/this.r,d=Ad(this.color.r,this.color.g,this.color.b);d.h=f,d.s=p;const v=Rd(d.h,d.s,d.v);v.a=this.color.a,this.color=v,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}};function qd(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<1)throw new TypeError("Invalid arguments.");if(1===e.length)return document.createTextNode(e[0]);{const t=document.createElement(e[0]);return t.appendChild(qd(...da(e).call(e,1))),t}}let Ud,Yd=!1;const Xd="background: #FFeeee; color: #dd0000";const Gd=El,Qd=Vd,Jd=class{constructor(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>!1;this.parent=t,this.changedOptions=[],this.container=e,this.allowCreation=!1,this.hideOption=i,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},uh(this.options,this.defaultOptions),this.configureOptions=n,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new Vd(r),this.wrapper=void 0}setOptions(t){if(void 0!==t){this.popupHistory={},this._removePopup();let e=!0;if("string"==typeof t)this.options.filter=t;else if(Ba(t))this.options.filter=t.join();else if("object"==typeof t){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==cp(t)&&(this.options.filter=cp(t)),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===cp(this.options)&&(e=!1),this.options.enabled=e}this._clean()}setModuleOptions(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}_create(){this._clean(),this.changedOptions=[];const t=cp(this.options);let e=0,n=!1;for(const r in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,r)&&(this.allowCreation=!1,n=!1,"function"==typeof t?(n=t(r,[]),n=n||this._handleObject(this.configureOptions[r],[r],!0)):!0!==t&&-1===td(t).call(t,r)||(n=!0),!1!==n&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(r),this._handleObject(this.configureOptions[r],[r])),e++);this._makeButton(),this._push()}_push(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(let t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}_clean(){for(let t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}_getValue(t){let e=this.moduleOptions;for(let n=0;n<t.length;n++){if(void 0===e[t[n]]){e=void 0;break}e=e[t[n]]}return e}_makeItem(t){if(!0===this.allowCreation){const i=document.createElement("div");i.className="vis-configuration vis-config-item vis-config-s"+t.length;for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return sc(n).call(n,t=>{i.appendChild(t)}),this.domElements.push(i),this.domElements.length}return 0}_makeHeader(t){const e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}_makeLabel(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=document.createElement("div");if(r.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===n){for(;r.firstChild;)r.removeChild(r.firstChild);r.appendChild(qd("i","b",t))}else r.innerText=t+":";return r}_makeDropdown(t,e,n){const r=document.createElement("select");r.className="vis-configuration vis-config-select";let i=0;void 0!==e&&-1!==td(t).call(t,e)&&(i=td(t).call(t,e));for(let e=0;e<t.length;e++){const n=document.createElement("option");n.value=t[e],e===i&&(n.selected="selected"),n.innerText=t[e],r.appendChild(n)}const o=this;r.onchange=function(){o._update(this.value,n)};const a=this._makeLabel(n[n.length-1],n);this._makeItem(n,a,r)}_makeRange(t,e,n){const r=t[0],i=t[1],o=t[2],a=t[3],s=document.createElement("input");s.className="vis-configuration vis-config-range";try{s.type="range",s.min=i,s.max=o}catch(t){}s.step=a;let c="",u=0;if(void 0!==e){const t=1.2;e<0&&e*t<i?(s.min=Math.ceil(e*t),u=s.min,c="range increased"):e/t<i&&(s.min=Math.ceil(e/t),u=s.min,c="range increased"),e*t>o&&1!==o&&(s.max=Math.ceil(e*t),u=s.max,c="range increased"),s.value=e}else s.value=r;const l=document.createElement("input");l.className="vis-configuration vis-config-rangeinput",l.value=s.value;const h=this;s.onchange=function(){l.value=this.value,h._update(Number(this.value),n)},s.oninput=function(){l.value=this.value};const f=this._makeLabel(n[n.length-1],n),p=this._makeItem(n,f,s,l);""!==c&&this.popupHistory[p]!==u&&(this.popupHistory[p]=u,this._setupPopup(c,p))}_makeButton(){if(!0===this.options.showButton){const t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerText="generate options",t.onclick=()=>{this._printOptions()},t.onmouseover=()=>{t.className="vis-configuration vis-config-button hover"},t.onmouseout=()=>{t.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(t)}}_setupPopup(t,e){if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){const n=document.createElement("div");n.id="vis-configuration-popup",n.className="vis-configuration-popup",n.innerText=t,n.onclick=()=>{this._removePopup()},this.popupCounter+=1,this.popupDiv={html:n,index:e}}}_removePopup(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}_showPopupIfNeeded(){if(void 0!==this.popupDiv.html){const t=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=t.left+"px",this.popupDiv.html.style.top=t.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=Nh(()=>{this.popupDiv.html.style.opacity=0},1500),this.popupDiv.deleteTimeout=Nh(()=>{this._removePopup()},1800)}}_makeCheckbox(t,e,n){const r=document.createElement("input");r.type="checkbox",r.className="vis-configuration vis-config-checkbox",r.checked=t,void 0!==e&&(r.checked=e,e!==t&&("object"==typeof t?e!==t.enabled&&this.changedOptions.push({path:n,value:e}):this.changedOptions.push({path:n,value:e})));const i=this;r.onchange=function(){i._update(this.checked,n)};const o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}_makeTextInput(t,e,n){const r=document.createElement("input");r.type="text",r.className="vis-configuration vis-config-text",r.value=e,e!==t&&this.changedOptions.push({path:n,value:e});const i=this;r.onchange=function(){i._update(this.value,n)};const o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}_makeColorField(t,e,n){const r=t[1],i=document.createElement("div");"none"!==(e=void 0===e?r:e)?(i.className="vis-configuration vis-config-colorBlock",i.style.backgroundColor=e):i.className="vis-configuration vis-config-colorBlock none",e=void 0===e?r:e,i.onclick=()=>{this._showColorPicker(e,i,n)};const o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,i)}_showColorPicker(t,e,n){e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback(t=>{const r="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=r,this._update(r,n)}),this.colorPicker.setCloseCallback(()=>{e.onclick=()=>{this._showColorPicker(t,e,n)}})}_handleObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!1;const i=cp(this.options);let o=!1;for(const a in t)if(Object.prototype.hasOwnProperty.call(t,a)){r=!0;const s=t[a],c=Fd(e,a);if("function"==typeof i&&(r=i(a,e),!1===r&&!Ba(s)&&"string"!=typeof s&&"boolean"!=typeof s&&s instanceof Object&&(this.allowCreation=!1,r=this._handleObject(s,c,!0),this.allowCreation=!1===n)),!1!==r){o=!0;const t=this._getValue(c);if(Ba(s))this._handleArray(s,t,c);else if("string"==typeof s)this._makeTextInput(s,t,c);else if("boolean"==typeof s)this._makeCheckbox(s,t,c);else if(s instanceof Object){if(!this.hideOption(e,a,this.moduleOptions))if(void 0!==s.enabled){const t=Fd(c,"enabled"),e=this._getValue(t);if(!0===e){const t=this._makeLabel(a,c,!0);this._makeItem(c,t),o=this._handleObject(s,c)||o}else this._makeCheckbox(s,e,c)}else{const t=this._makeLabel(a,c,!0);this._makeItem(c,t),o=this._handleObject(s,c)||o}}else console.error("dont know how to handle",s,a,c)}}return o}_handleArray(t,e,n){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,n),t[1]!==e&&this.changedOptions.push({path:n,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:Number(e)}))}_update(t,e){const n=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",n),this.initialized=!0,this.parent.setOptions(n)}_constructOptions(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n;t="false"!==(t="true"===t||t)&&t;for(let n=0;n<e.length;n++)"global"!==e[n]&&(void 0===r[e[n]]&&(r[e[n]]={}),n!==e.length-1?r=r[e[n]]:r[e[n]]=t);return n}_printOptions(){const t=this.getOptions();for(;this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(qd("pre","const options = "+$l(t,null,2)))}getOptions(){const t={};for(let e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}},$d=Cl,Zd=class{constructor(t,e){this.container=t,this.overflowMethod=e||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}setPosition(t,e){this.x=Wp(t),this.y=Wp(e)}setText(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}show(t){if(void 0===t&&(t=!0),!0===t){const t=this.frame.clientHeight,e=this.frame.clientWidth,n=this.frame.parentNode.clientHeight,r=this.frame.parentNode.clientWidth;let i=0,o=0;if("flip"==this.overflowMethod){let n=!1,a=!0;this.y-t<this.padding&&(a=!1),this.x+e>r-this.padding&&(n=!0),i=n?this.x-e:this.x,o=a?this.y-t:this.y}else o=this.y-t,o+t+this.padding>n&&(o=n-t-this.padding),o<this.padding&&(o=this.padding),i=this.x,i+e+this.padding>r&&(i=r-e-this.padding),i<this.padding&&(i=this.padding);this.frame.style.left=i+"px",this.frame.style.top=o+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}hide(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}destroy(){this.frame.parentNode.removeChild(this.frame)}},Kd=Xd,tv=class t{static validate(e,n,r){Yd=!1,Ud=n;let i=n;return void 0!==r&&(i=n[r]),t.parse(e,i,[]),Yd}static parse(e,n,r){for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.check(i,e,n,r)}static check(e,n,r,i){if(void 0===r[e]&&void 0===r.__any__)return void t.getSuggestion(e,r,i);let o=e,a=!0;void 0===r[e]&&void 0!==r.__any__&&(o="__any__",a="object"===t.getType(n[e]));let s=r[o];a&&void 0!==s.__type__&&(s=s.__type__),t.checkFields(e,n,r,o,s,i)}static checkFields(e,n,r,i,o,a){const s=function(n){console.error("%c"+n+t.printLocation(a,e),Xd)},c=t.getType(n[e]),u=o[c];void 0!==u?"array"===t.getType(u)&&-1===td(u).call(u,n[e])?(s('Invalid option detected in "'+e+'". Allowed values are:'+t.print(u)+' not "'+n[e]+'". '),Yd=!0):"object"===c&&"__any__"!==i&&(a=Fd(a,e),t.parse(n[e],r[i],a)):void 0===o.any&&(s('Invalid type received for "'+e+'". Expected: '+t.print(Ka(o))+". Received ["+c+'] "'+n[e]+'"'),Yd=!0)}static getType(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Ba(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}static getSuggestion(e,n,r){const i=t.findInOptions(e,n,r,!1),o=t.findInOptions(e,Ud,[],!0);let a;a=void 0!==i.indexMatch?" in "+t.printLocation(i.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+i.indexMatch+'"?\n\n':o.distance<=4&&i.distance>o.distance?" in "+t.printLocation(i.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(o.path,o.closestMatch,""):i.distance<=8?'. Did you mean "'+i.closestMatch+'"?'+t.printLocation(i.path,e):". Did you mean one of these: "+t.print(Ka(n))+t.printLocation(r,e),console.error('%cUnknown option detected: "'+e+'"'+a,Xd),Yd=!0}static findInOptions(e,n,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=1e9,a="",s=[];const c=e.toLowerCase();let u;for(const h in n){let f;if(void 0!==n[h].__type__&&!0===i){const i=t.findInOptions(e,n[h],Fd(r,h));o>i.distance&&(a=i.closestMatch,s=i.path,o=i.distance,u=i.indexMatch)}else{var l;-1!==td(l=h.toLowerCase()).call(l,c)&&(u=h),f=t.levenshteinDistance(e,h),o>f&&(a=h,s=_d(r),o=f)}}return{closestMatch:a,path:s,distance:o,indexMatch:u}}static printLocation(t,e){let n="\n\n"+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Problem value found at: \n")+"options = {\n";for(let e=0;e<t.length;e++){for(let t=0;t<e+1;t++)n+="  ";n+=t[e]+": {\n"}for(let e=0;e<t.length+1;e++)n+="  ";n+=e+"\n";for(let e=0;e<t.length+1;e++){for(let r=0;r<t.length-e;r++)n+="  ";n+="}\n"}return n+"\n\n"}static print(t){return $l(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}static levenshteinDistance(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const n=[];let r,i;for(r=0;r<=e.length;r++)n[r]=[r];for(i=0;i<=t.length;i++)n[0][i]=i;for(r=1;r<=e.length;r++)for(i=1;i<=t.length;i++)e.charAt(r-1)==t.charAt(i-1)?n[r][i]=n[r-1][i-1]:n[r][i]=Math.min(n[r-1][i-1]+1,Math.min(n[r][i-1]+1,n[r-1][i]+1));return n[e.length][t.length]}};t.Activator=Gd,t.Alea=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){let[e,n,r]=function(){const t=function(){let t=4022871197;return function(e){const n=e.toString();for(let e=0;e<n.length;e++){t+=n.charCodeAt(e);let r=.02519603282416938*t;t=r>>>0,r-=t,r*=t,t=r>>>0,r-=t,t+=4294967296*r}return 2.3283064365386963e-10*(t>>>0)}}();let e=t(" "),n=t(" "),r=t(" ");for(let i=0;i<arguments.length;i++)e-=t(i<0||arguments.length<=i?void 0:arguments[i]),e<0&&(e+=1),n-=t(i<0||arguments.length<=i?void 0:arguments[i]),n<0&&(n+=1),r-=t(i<0||arguments.length<=i?void 0:arguments[i]),r<0&&(r+=1);return[e,n,r]}(t),i=1;const o=()=>{const t=2091639*e+2.3283064365386963e-10*i;return e=n,n=r,r=t-(i=0|t)};return o.uint32=()=>4294967296*o(),o.fract53=()=>o()+11102230246251565e-32*(2097152*o()|0),o.algorithm="Alea",o.seed=t,o.version="0.9",o}(e.length?e:[vs()])},t.ColorPicker=Qd,t.Configurator=Jd,t.DELETE=ts,t.HSVToHex=Nd,t.HSVToRGB=Rd,t.Hammer=$d,t.Popup=Zd,t.RGBToHSV=Ad,t.RGBToHex=xd,t.VALIDATOR_PRINT_STYLE=Kd,t.Validator=tv,t.addClassName=function(t,e){let n=t.className.split(" ");const r=e.split(" ");n=Gf(n).call(n,cp(r).call(r,function(t){return!kf(n).call(n,t)})),t.className=n.join(" ")},t.addCssText=function(t,e){const n=jd(e);for(const[e,r]of hd(n))t.style.setProperty(e,r)},t.binarySearchCustom=function(t,e,n,r){let i=0,o=0,a=t.length-1;for(;o<=a&&i<1e4;){const s=Math.floor((o+a)/2),c=t[s],u=e(void 0===r?c[n]:c[n][r]);if(0==u)return s;-1==u?o=s+1:a=s-1,i++}return-1},t.binarySearchValue=function(t,e,n,r,i){let o,a,s,c,u=0,l=0,h=t.length-1;for(i=null!=i?i:function(t,e){return t==e?0:t<e?-1:1};l<=h&&u<1e4;){if(c=Math.floor(.5*(h+l)),o=t[Math.max(0,c-1)][n],a=t[c][n],s=t[Math.min(t.length-1,c+1)][n],0==i(a,e))return c;if(i(o,e)<0&&i(a,e)>0)return"before"==r?Math.max(0,c-1):c;if(i(a,e)<0&&i(s,e)>0)return"before"==r?c:Math.min(t.length-1,c+1);i(a,e)<0?l=c+1:h=c-1,u++}return-1},t.bridgeObject=zd,t.copyAndExtendArray=Fd,t.copyArray=_d,t.deepExtend=Td,t.deepObjectAssign=es,t.easingFunctions=Hd,t.equalArray=function(t,e){if(t.length!==e.length)return!1;for(let n=0,r=t.length;n<r;n++)if(t[n]!=e[n])return!1;return!0},t.extend=Od,t.fillIfDefined=function t(e,n){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(const i in e)if(void 0!==n[i])if(null===n[i]||"object"!=typeof n[i])kd(e,n,i,r);else{const o=e[i],a=n[i];Ed(o)&&Ed(a)&&t(o,a,r)}},t.forEach=function(t,e){if(Ba(t)){const n=t.length;for(let r=0;r<n;r++)e(t[r],r,t)}else for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)},t.getAbsoluteLeft=function(t){return t.getBoundingClientRect().left},t.getAbsoluteRight=function(t){return t.getBoundingClientRect().right},t.getAbsoluteTop=function(t){return t.getBoundingClientRect().top},t.getScrollBarWidth=function(){const t=document.createElement("p");t.style.width="100%",t.style.height="200px";const e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(t),document.body.appendChild(e);const n=t.offsetWidth;e.style.overflow="scroll";let r=t.offsetWidth;return n==r&&(r=e.clientWidth),document.body.removeChild(e),n-r},t.getTarget=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.event,e=null;return t&&(t.target?e=t.target:t.srcElement&&(e=t.srcElement)),e instanceof Element&&(null==e.nodeType||3!=e.nodeType||(e=e.parentNode,e instanceof Element))?e:null},t.getType=function(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Ba(t)?"Array":t instanceof Date?"Date":"Object":"number"===e?"Number":"boolean"===e?"Boolean":"string"===e?"String":void 0===e?"undefined":e},t.hasParent=function(t,e){let n=t;for(;n;){if(n===e)return!0;if(!n.parentNode)return!1;n=n.parentNode}return!1},t.hexToHSV=Id,t.hexToRGB=Pd,t.insertSort=function(t,e){for(let n=0;n<t.length;n++){const r=t[n];let i;for(i=n;i>0&&e(r,t[i-1])<0;i--)t[i]=t[i-1];t[i]=r}return t},t.isDate=function(t){if(t instanceof Date)return!0;if(Cd(t)){if(vd.exec(t))return!0;if(!isNaN(Date.parse(t)))return!0}return!1},t.isNumber=wd,t.isObject=Ed,t.isString=Cd,t.isValidHex=Bd,t.isValidRGB=Md,t.isValidRGBA=Ld,t.mergeOptions=function(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const i=function(t){return null!=t},o=function(t){return null!==t&&"object"==typeof t};if(!o(t))throw new Error("Parameter mergeTarget must be an object");if(!o(e))throw new Error("Parameter options must be an object");if(!i(n))throw new Error("Parameter option must have a value");if(!o(r))throw new Error("Parameter globalOptions must be an object");const a=e[n],s=o(r)&&!function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(r)?r[n]:void 0,c=s?s.enabled:void 0;if(void 0===a)return;if("boolean"==typeof a)return o(t[n])||(t[n]={}),void(t[n].enabled=a);if(null===a&&!o(t[n])){if(!i(s))return;t[n]=dd(s)}if(!o(a))return;let u=!0;void 0!==a.enabled?u=a.enabled:void 0!==c&&(u=s.enabled),function(t,e,n){o(t[n])||(t[n]={});const r=e[n],i=t[n];for(const t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}(t,e,n),t[n].enabled=u},t.option=Dd,t.overrideOpacity=function(t,e){if(kf(t).call(t,"rgba"))return t;if(kf(t).call(t,"rgb")){const n=t.substr(td(t).call(t,"(")+1).replace(")","").split(",");return"rgba("+n[0]+","+n[1]+","+n[2]+","+e+")"}{const n=Pd(t);return null==n?t:"rgba("+n.r+","+n.g+","+n.b+","+e+")"}},t.parseColor=function(t,e){if(Cd(t)){let e=t;if(Md(e)){var n;const t=Qa(n=e.substr(4).substr(0,e.length-5).split(",")).call(n,function(t){return Wp(t)});e=xd(t[0],t[1],t[2])}if(!0===Bd(e)){const t=Id(e),n={h:t.h,s:.8*t.s,v:Math.min(1,1.02*t.v)},r={h:t.h,s:Math.min(1,1.25*t.s),v:.8*t.v},i=Nd(r.h,r.s,r.v),o=Nd(n.h,n.s,n.v);return{background:e,border:i,highlight:{background:o,border:i},hover:{background:o,border:i}}}return{background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}if(e){return{background:t.background||e.background,border:t.border||e.border,highlight:Cd(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:Cd(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}}return{background:t.background||void 0,border:t.border||void 0,highlight:Cd(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:Cd(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}},t.preventDefault=function(t){t||(t=window.event),t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},t.pureDeepObjectAssign=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return es({},t,...n)},t.recursiveDOMDelete=function t(e){if(e)for(;!0===e.hasChildNodes();){const n=e.firstChild;n&&(t(n),e.removeChild(n))}},t.removeClassName=function(t,e){let n=t.className.split(" ");const r=e.split(" ");n=cp(n).call(n,function(t){return!kf(r).call(r,t)}),t.className=n.join(" ")},t.removeCssText=function(t,e){const n=jd(e);for(const e of Ka(n))t.style.removeProperty(e)},t.selectiveBridgeObject=function(t,e){if(null!==e&&"object"==typeof e){const n=dd(e);for(let r=0;r<t.length;r++)Object.prototype.hasOwnProperty.call(e,t[r])&&"object"==typeof e[t[r]]&&(n[t[r]]=zd(e[t[r]]));return n}return null},t.selectiveDeepExtend=function(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Ba(n))throw new TypeError("Arrays are not supported by deepExtend");for(let i=0;i<t.length;i++){const o=t[i];if(Object.prototype.hasOwnProperty.call(n,o))if(n[o]&&n[o].constructor===Object)void 0===e[o]&&(e[o]={}),e[o].constructor===Object?Td(e[o],n[o],!1,r):kd(e,n,o,r);else{if(Ba(n[o]))throw new TypeError("Arrays are not supported by deepExtend");kd(e,n,o,r)}}return e},t.selectiveExtend=function(t,e){if(!Ba(t))throw new Error("Array with property names expected as first argument");for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];for(const n of r)for(let r=0;r<t.length;r++){const i=t[r];n&&Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},t.selectiveNotDeepExtend=function(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Ba(n))throw new TypeError("Arrays are not supported by deepExtend");for(const i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&!kf(t).call(t,i))if(n[i]&&n[i].constructor===Object)void 0===e[i]&&(e[i]={}),e[i].constructor===Object?Td(e[i],n[i]):kd(e,n,i,r);else if(Ba(n[i])){e[i]=[];for(let t=0;t<n[i].length;t++)e[i].push(n[i][t])}else kd(e,n,i,r);return e},t.throttle=function(t){let e=!1;return()=>{e||(e=!0,requestAnimationFrame(()=>{e=!1,t()}))}},t.toArray=Sd,t.topMost=function(t,e){let n;Ba(e)||(e=[e]);for(const r of t)if(r){n=r[e[0]];for(let t=1;t<e.length;t++)n&&(n=n[e[t]]);if(void 0!==n)break}return n},t.updateProperty=function(t,e,n){return t[e]!==n&&(t[e]=n,!0)}});
//# sourceMappingURL=vis-util.min.js.map
