{"version": 3, "file": "util.d.ts", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": "AAeA;;GAEG;AACH,MAAM,WAAW,GAAG;IAClB;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,WAAW,GAAG;IAClB;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,WAAW,IAAI;IACnB;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;CACX;AAED;;;;GAIG;AACH,wBAAgB,QAAQ,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,MAAM,CAExD;AAED;;;GAGG;AACH,wBAAgB,kBAAkB,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI,CAU3E;AAED;;;;GAIG;AACH,wBAAgB,QAAQ,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,MAAM,CAExD;AAED;;;;GAIG;AACH,wBAAgB,QAAQ,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,MAAM,CAExD;AAED;;;;GAIG;AACH,wBAAgB,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,MAAM,CAc7D;AA8BD;;;;;;;;GAQG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,MAAM,EAC5C,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EACb,aAAa,UAAQ,GACpB,IAAI,CAiBN;AAED;;;;;;GAMG;AACH,eAAO,MAAM,MAAM;;;;;CAAgB,CAAC;AAEpC;;;;;;;;GAQG;AACH,wBAAgB,eAAe,CAC7B,KAAK,EAAE,MAAM,EAAE,EACf,CAAC,EAAE,GAAG,EACN,GAAG,MAAM,EAAE,GAAG,EAAE,GACf,GAAG,CAcL;AAED;;;;;;;;;;;;GAYG;AACH,wBAAgB,mBAAmB,CACjC,KAAK,EAAE,MAAM,EAAE,EACf,CAAC,EAAE,GAAG,EACN,CAAC,EAAE,GAAG,EACN,aAAa,UAAQ,GACpB,GAAG,CA0BL;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,sBAAsB,CACpC,cAAc,EAAE,MAAM,EAAE,EACxB,CAAC,EAAE,GAAG,EACN,CAAC,EAAE,GAAG,EACN,aAAa,UAAQ,GACpB,GAAG,CAmCL;AAED;;;;;;;;;GASG;AACH,wBAAgB,UAAU,CACxB,CAAC,EAAE,GAAG,EACN,CAAC,EAAE,GAAG,EACN,WAAW,UAAQ,EACnB,aAAa,UAAQ,GACpB,GAAG,CA2BL;AAED;;;;;GAKG;AACH,wBAAgB,UAAU,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,GAAG,OAAO,CAY9D;AAED;;;;GAIG;AACH,wBAAgB,OAAO,CAAC,MAAM,EAAE,OAAO,GAAG,MAAM,CAuC/C;AAED,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAC/E,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,CAAC,EACrC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,EACrB,QAAQ,EAAE,CAAC,GACV,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AAcb;;;;GAIG;AACH,wBAAgB,SAAS,CAAC,CAAC,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAEvD;AAED;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,IAAI,EAAE,OAAO,GAAG,MAAM,CAErD;AAED;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,IAAI,EAAE,OAAO,GAAG,MAAM,CAEtD;AAED;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,IAAI,EAAE,OAAO,GAAG,MAAM,CAEpD;AAED;;;;GAIG;AACH,wBAAgB,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,GAAG,IAAI,CASpE;AAED;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,GAAG,IAAI,CAOvE;AAED,wBAAgB,OAAO,CAAC,CAAC,EACvB,KAAK,EAAE,SAAS,GAAG,IAAI,GAAG,CAAC,EAAE,EAC7B,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,IAAI,GACvD,IAAI,CAAC;AACR,wBAAgB,OAAO,CAAC,CAAC,SAAS,MAAM,EACtC,MAAM,EAAE,SAAS,GAAG,IAAI,GAAG,CAAC,EAC5B,QAAQ,EAAE,CAAC,GAAG,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,KAAK,IAAI,GAC1E,IAAI,CAAC;AAyBR;;;;GAIG;AACH,eAAO,MAAM,OAAO;;;;;CAAgB,CAAC;AAErC;;;;;;GAMG;AACH,wBAAgB,cAAc,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,EAChD,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACpB,GAAG,EAAE,CAAC,EACN,KAAK,EAAE,CAAC,GACP,OAAO,CAOT;AAED;;;;GAIG;AACH,wBAAgB,QAAQ,CAAC,EAAE,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI,CAYnD;AAED;;;GAGG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,KAAK,GAAG,SAAS,GAAG,IAAI,CAa7D;AAED;;;;GAIG;AACH,wBAAgB,SAAS,CACvB,KAAK,GAAE,KAAK,GAAG,SAAwB,GACtC,OAAO,GAAG,IAAI,CA0BhB;AAED;;;;;GAKG;AACH,wBAAgB,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,GAAG,OAAO,CAcpE;AAED,eAAO,MAAM,MAAM;IACjB;;;;;OAKG;qBACc,OAAO,iBAAiB,OAAO,GAAG,OAAO,GAAG,IAAI;IAYjE;;;;;OAKG;oBACa,OAAO,iBAAiB,MAAM,GAAG,MAAM,GAAG,IAAI;IAY9D;;;;;OAKG;oBACa,OAAO,iBAAiB,MAAM,GAAG,MAAM,GAAG,IAAI;IAY9D;;;;;OAKG;kBACW,OAAO,iBAAiB,MAAM,GAAG,MAAM,GAAG,IAAI;IAc5D;;;;;OAKG;cACO,CAAC,SAAS,IAAI,SACf,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,gBAC9B,CAAC,GACd,CAAC,GAAG,IAAI;CAOZ,CAAC;AAEF;;;;;;GAMG;AACH,wBAAgB,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,GAAG,IAAI,CA0BhD;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAiBtE;AAED;;;;;;GAMG;AACH,wBAAgB,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,CAIzE;AAED,MAAM,WAAW,WAAW;IAC1B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EACF,MAAM,GACN;QACE,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC;IACN,SAAS,CAAC,EACN,MAAM,GACN;QACE,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC;CACP;AACD,MAAM,WAAW,eAAe;IAC9B,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE;QACL,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,SAAS,EAAE;QACT,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;CACH;AAED,wBAAgB,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,eAAe,CAAC;AAChE,wBAAgB,UAAU,CAAC,UAAU,EAAE,eAAe,GAAG,eAAe,CAAC;AACzE,wBAAgB,UAAU,CAAC,UAAU,EAAE,WAAW,GAAG,WAAW,CAAC;AACjE,wBAAgB,UAAU,CACxB,UAAU,EAAE,WAAW,EACvB,YAAY,EAAE,eAAe,GAC5B,eAAe,CAAC;AA0InB;;;;;;;;GAQG;AACH,wBAAgB,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,GAAG,CAoBtE;AA2BD;;;;GAIG;AACH,wBAAgB,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,CAKtE;AAED;;;;GAIG;AACH,wBAAgB,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,CAKzE;AAED;;;;;;;;GAQG;AACH,wBAAgB,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,CAqC7D;AAED;;;;;;GAMG;AACH,wBAAgB,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,CAGhE;AAED;;;;GAIG;AACH,wBAAgB,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAMzC;AAED;;;;GAIG;AACH,wBAAgB,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAG/C;AAED;;;;GAIG;AACH,wBAAgB,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAE/C;AAED;;;;GAIG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAEjD;AAED;;;;;;GAMG;AACH,wBAAgB,qBAAqB,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,EACvD,MAAM,EAAE,CAAC,EAAE,EACX,eAAe,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAC5B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAerB;AAED,wBAAgB,YAAY,CAAC,CAAC,SAAS,MAAM,EAAE,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC;AACtE,wBAAgB,YAAY,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,GAAG,IAAI,CAAC;AA+B1D;;;;;GAKG;AACH,wBAAgB,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM,GAAG,CAAC,EAAE,CAU1E;AAED;;;;;;;;;;;GAWG;AACH,wBAAgB,YAAY,CAC1B,WAAW,EAAE,GAAG,EAChB,OAAO,EAAE,GAAG,EACZ,MAAM,EAAE,MAAM,EACd,aAAa,GAAE,GAAQ,GACtB,IAAI,CA2GN;AAED,wBAAgB,kBAAkB,CAChC,CAAC,SAAS,MAAM,EAChB,EAAE,SAAS,MAAM,CAAC,EAClB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAEtB,YAAY,EAAE,CAAC,EAAE,EACjB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EACxC,KAAK,EAAE,EAAE,EACT,MAAM,EAAE,EAAE,GACT,MAAM,CAAC;AACV,wBAAgB,kBAAkB,CAAC,CAAC,SAAS,MAAM,EAAE,EAAE,SAAS,MAAM,CAAC,EACrE,YAAY,EAAE,CAAC,EAAE,EACjB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EACpC,KAAK,EAAE,EAAE,GACR,MAAM,CAAC;AA6CV;;;;;;;;;;;GAWG;AACH,wBAAgB,iBAAiB,CAAC,CAAC,SAAS,MAAM,EAChD,YAAY,EAAE;KAAG,CAAC,IAAI,CAAC,GAAG,MAAM;CAAE,EAAE,EACpC,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,CAAC,EACR,cAAc,EAAE,QAAQ,GAAG,OAAO,EAClC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAChD,MAAM,CAyDR;AASD,eAAO,MAAM,eAAe;IAC1B;;;;OAIG;cACO,MAAM,GAAG,MAAM;IAIzB;;;;OAIG;kBACW,MAAM,GAAG,MAAM;IAI7B;;;;OAIG;mBACY,MAAM,GAAG,MAAM;IAI9B;;;;OAIG;qBACc,MAAM,GAAG,MAAM;IAIhC;;;;OAIG;mBACY,MAAM,GAAG,MAAM;IAI9B;;;;OAIG;oBACa,MAAM,GAAG,MAAM;IAI/B;;;;OAIG;sBACe,MAAM,GAAG,MAAM;IAIjC;;;;OAIG;mBACY,MAAM,GAAG,MAAM;IAI9B;;;;OAIG;oBACa,MAAM,GAAG,MAAM;IAI/B;;;;OAIG;sBACe,MAAM,GAAG,MAAM;IAIjC;;;;OAIG;mBACY,MAAM,GAAG,MAAM;IAI9B;;;;OAIG;oBACa,MAAM,GAAG,MAAM;IAI/B;;;;OAIG;sBACe,MAAM,GAAG,MAAM;CAGlC,CAAC;AAEF;;;GAGG;AACH,wBAAgB,iBAAiB,IAAI,MAAM,CA0B1C;AAiBD;;;;;;GAMG;AACH,wBAAgB,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,GAAG,GAAG,CAmBtD"}