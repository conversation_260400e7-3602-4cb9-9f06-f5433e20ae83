{"version": 3, "file": "vis-util.min.cjs", "sources": ["../../../src/deep-object-assign.ts", "../../src/shared/hammer.js", "../../src/shared/activator.js", "../../../src/util.ts", "../../src/shared/color-picker.js", "../../src/shared/configurator.js", "../../src/shared/validator.js", "../../../src/shared/index.ts", "../../src/shared/popup.js", "../../../src/random/alea.ts"], "sourcesContent": [null, "import RealHammer from \"@egjs/hammerjs\";\n\n/**\n * Setup a mock hammer.js object, for unit testing.\n *\n * Inspiration: https://github.com/uber/deck.gl/pull/658\n * @returns {{on: noop, off: noop, destroy: noop, emit: noop, get: get}}\n */\nfunction hammerMock() {\n  const noop = () => {};\n\n  return {\n    on: noop,\n    off: noop,\n    destroy: noop,\n    emit: noop,\n\n    get() {\n      return {\n        set: noop,\n      };\n    },\n  };\n}\n\nconst Hammer =\n  typeof window !== \"undefined\"\n    ? window.Hammer || RealHammer\n    : function () {\n        // hammer.js is only available in a browser, not in node.js. Replacing it with a mock object.\n        return hammerMock();\n      };\n\nexport { Hammer };\n", "import Emitter from \"component-emitter\";\nimport { <PERSON> } from \"./hammer.js\";\n\n/**\n * Turn an element into an clickToUse element.\n * When not active, the element has a transparent overlay. When the overlay is\n * clicked, the mode is changed to active.\n * When active, the element is displayed with a blue border around it, and\n * the interactive contents of the element can be used. When clicked outside\n * the element, the elements mode is changed to inactive.\n * @param {Element} container\n * @class Activator\n */\nexport function Activator(container) {\n  this._cleanupQueue = [];\n\n  this.active = false;\n\n  this._dom = {\n    container,\n    overlay: document.createElement(\"div\"),\n  };\n\n  this._dom.overlay.classList.add(\"vis-overlay\");\n\n  this._dom.container.appendChild(this._dom.overlay);\n  this._cleanupQueue.push(() => {\n    this._dom.overlay.parentNode.removeChild(this._dom.overlay);\n  });\n\n  const hammer = Hammer(this._dom.overlay);\n  hammer.on(\"tap\", this._onTapOverlay.bind(this));\n  this._cleanupQueue.push(() => {\n    hammer.destroy();\n    // FIXME: cleaning up hammer instances doesn't work (Timeline not removed\n    // from memory)\n  });\n\n  // block all touch events (except tap)\n  const events = [\n    \"tap\",\n    \"doubletap\",\n    \"press\",\n    \"pinch\",\n    \"pan\",\n    \"panstart\",\n    \"panmove\",\n    \"panend\",\n  ];\n  events.forEach((event) => {\n    hammer.on(event, (event) => {\n      event.srcEvent.stopPropagation();\n    });\n  });\n\n  // attach a click event to the window, in order to deactivate when clicking outside the timeline\n  if (document && document.body) {\n    this._onClick = (event) => {\n      if (!_hasParent(event.target, container)) {\n        this.deactivate();\n      }\n    };\n    document.body.addEventListener(\"click\", this._onClick);\n    this._cleanupQueue.push(() => {\n      document.body.removeEventListener(\"click\", this._onClick);\n    });\n  }\n\n  // prepare escape key listener for deactivating when active\n  this._escListener = (event) => {\n    if (\n      \"key\" in event\n        ? event.key === \"Escape\"\n        : event.keyCode === 27 /* the keyCode is for IE11 */\n    ) {\n      this.deactivate();\n    }\n  };\n}\n\n// turn into an event emitter\nEmitter(Activator.prototype);\n\n// The currently active activator\nActivator.current = null;\n\n/**\n * Destroy the activator. Cleans up all created DOM and event listeners\n */\nActivator.prototype.destroy = function () {\n  this.deactivate();\n\n  for (const callback of this._cleanupQueue.splice(0).reverse()) {\n    callback();\n  }\n};\n\n/**\n * Activate the element\n * Overlay is hidden, element is decorated with a blue shadow border\n */\nActivator.prototype.activate = function () {\n  // we allow only one active activator at a time\n  if (Activator.current) {\n    Activator.current.deactivate();\n  }\n  Activator.current = this;\n\n  this.active = true;\n  this._dom.overlay.style.display = \"none\";\n  this._dom.container.classList.add(\"vis-active\");\n\n  this.emit(\"change\");\n  this.emit(\"activate\");\n\n  // ugly hack: bind ESC after emitting the events, as the Network rebinds all\n  // keyboard events on a 'change' event\n  document.body.addEventListener(\"keydown\", this._escListener);\n};\n\n/**\n * Deactivate the element\n * Overlay is displayed on top of the element\n */\nActivator.prototype.deactivate = function () {\n  this.active = false;\n  this._dom.overlay.style.display = \"block\";\n  this._dom.container.classList.remove(\"vis-active\");\n  document.body.removeEventListener(\"keydown\", this._escListener);\n\n  this.emit(\"change\");\n  this.emit(\"deactivate\");\n};\n\n/**\n * Handle a tap event: activate the container\n * @param {Event}  event   The event\n * @private\n */\nActivator.prototype._onTapOverlay = function (event) {\n  // activate the container\n  this.activate();\n  event.srcEvent.stopPropagation();\n};\n\n/**\n * Test whether the element has the requested parent element somewhere in\n * its chain of parent nodes.\n * @param {HTMLElement} element\n * @param {HTMLElement} parent\n * @returns {boolean} Returns true when the parent is found somewhere in the\n *                    chain of parent nodes.\n * @private\n */\nfunction _hasParent(element, parent) {\n  while (element) {\n    if (element === parent) {\n      return true;\n    }\n    element = element.parentNode;\n  }\n  return false;\n}\n", null, "import { Hammer } from \"./hammer.js\";\nimport {\n  HSVToRGB,\n  RGBToHSV,\n  hexToRGB,\n  isString,\n  isValidHex,\n  isValidRGB,\n  isValidRGBA,\n} from \"../util.ts\";\n\nconst htmlColors = {\n  black: \"#000000\",\n  navy: \"#000080\",\n  darkblue: \"#00008B\",\n  mediumblue: \"#0000CD\",\n  blue: \"#0000FF\",\n  darkgreen: \"#006400\",\n  green: \"#008000\",\n  teal: \"#008080\",\n  darkcyan: \"#008B8B\",\n  deepskyblue: \"#00BFFF\",\n  darkturquoise: \"#00CED1\",\n  mediumspringgreen: \"#00FA9A\",\n  lime: \"#00FF00\",\n  springgreen: \"#00FF7F\",\n  aqua: \"#00FFFF\",\n  cyan: \"#00FFFF\",\n  midnightblue: \"#191970\",\n  dodgerblue: \"#1E90FF\",\n  lightseagreen: \"#20B2AA\",\n  forestgreen: \"#228B22\",\n  seagreen: \"#2E8B57\",\n  darkslategray: \"#2F4F4F\",\n  limegreen: \"#32CD32\",\n  mediumseagreen: \"#3CB371\",\n  turquoise: \"#40E0D0\",\n  royalblue: \"#4169E1\",\n  steelblue: \"#4682B4\",\n  darkslateblue: \"#483D8B\",\n  mediumturquoise: \"#48D1CC\",\n  indigo: \"#4B0082\",\n  darkolivegreen: \"#556B2F\",\n  cadetblue: \"#5F9EA0\",\n  cornflowerblue: \"#6495ED\",\n  mediumaquamarine: \"#66CDAA\",\n  dimgray: \"#696969\",\n  slateblue: \"#6A5ACD\",\n  olivedrab: \"#6B8E23\",\n  slategray: \"#708090\",\n  lightslategray: \"#778899\",\n  mediumslateblue: \"#7B68EE\",\n  lawngreen: \"#7CFC00\",\n  chartreuse: \"#7FFF00\",\n  aquamarine: \"#7FFFD4\",\n  maroon: \"#800000\",\n  purple: \"#800080\",\n  olive: \"#808000\",\n  gray: \"#808080\",\n  skyblue: \"#87CEEB\",\n  lightskyblue: \"#87CEFA\",\n  blueviolet: \"#8A2BE2\",\n  darkred: \"#8B0000\",\n  darkmagenta: \"#8B008B\",\n  saddlebrown: \"#8B4513\",\n  darkseagreen: \"#8FBC8F\",\n  lightgreen: \"#90EE90\",\n  mediumpurple: \"#9370D8\",\n  darkviolet: \"#9400D3\",\n  palegreen: \"#98FB98\",\n  darkorchid: \"#9932CC\",\n  yellowgreen: \"#9ACD32\",\n  sienna: \"#A0522D\",\n  brown: \"#A52A2A\",\n  darkgray: \"#A9A9A9\",\n  lightblue: \"#ADD8E6\",\n  greenyellow: \"#ADFF2F\",\n  paleturquoise: \"#AFEEEE\",\n  lightsteelblue: \"#B0C4DE\",\n  powderblue: \"#B0E0E6\",\n  firebrick: \"#B22222\",\n  darkgoldenrod: \"#B8860B\",\n  mediumorchid: \"#BA55D3\",\n  rosybrown: \"#BC8F8F\",\n  darkkhaki: \"#BDB76B\",\n  silver: \"#C0C0C0\",\n  mediumvioletred: \"#C71585\",\n  indianred: \"#CD5C5C\",\n  peru: \"#CD853F\",\n  chocolate: \"#D2691E\",\n  tan: \"#D2B48C\",\n  lightgrey: \"#D3D3D3\",\n  palevioletred: \"#D87093\",\n  thistle: \"#D8BFD8\",\n  orchid: \"#DA70D6\",\n  goldenrod: \"#DAA520\",\n  crimson: \"#DC143C\",\n  gainsboro: \"#DCDCDC\",\n  plum: \"#DDA0DD\",\n  burlywood: \"#DEB887\",\n  lightcyan: \"#E0FFFF\",\n  lavender: \"#E6E6FA\",\n  darksalmon: \"#E9967A\",\n  violet: \"#EE82EE\",\n  palegoldenrod: \"#EEE8AA\",\n  lightcoral: \"#F08080\",\n  khaki: \"#F0E68C\",\n  aliceblue: \"#F0F8FF\",\n  honeydew: \"#F0FFF0\",\n  azure: \"#F0FFFF\",\n  sandybrown: \"#F4A460\",\n  wheat: \"#F5DEB3\",\n  beige: \"#F5F5DC\",\n  whitesmoke: \"#F5F5F5\",\n  mintcream: \"#F5FFFA\",\n  ghostwhite: \"#F8F8FF\",\n  salmon: \"#FA8072\",\n  antiquewhite: \"#FAEBD7\",\n  linen: \"#FAF0E6\",\n  lightgoldenrodyellow: \"#FAFAD2\",\n  oldlace: \"#FDF5E6\",\n  red: \"#FF0000\",\n  fuchsia: \"#FF00FF\",\n  magenta: \"#FF00FF\",\n  deeppink: \"#FF1493\",\n  orangered: \"#FF4500\",\n  tomato: \"#FF6347\",\n  hotpink: \"#FF69B4\",\n  coral: \"#FF7F50\",\n  darkorange: \"#FF8C00\",\n  lightsalmon: \"#FFA07A\",\n  orange: \"#FFA500\",\n  lightpink: \"#FFB6C1\",\n  pink: \"#FFC0CB\",\n  gold: \"#FFD700\",\n  peachpuff: \"#FFDAB9\",\n  navajowhite: \"#FFDEAD\",\n  moccasin: \"#FFE4B5\",\n  bisque: \"#FFE4C4\",\n  mistyrose: \"#FFE4E1\",\n  blanchedalmond: \"#FFEBCD\",\n  papayawhip: \"#FFEFD5\",\n  lavenderblush: \"#FFF0F5\",\n  seashell: \"#FFF5EE\",\n  cornsilk: \"#FFF8DC\",\n  lemonchiffon: \"#FFFACD\",\n  floralwhite: \"#FFFAF0\",\n  snow: \"#FFFAFA\",\n  yellow: \"#FFFF00\",\n  lightyellow: \"#FFFFE0\",\n  ivory: \"#FFFFF0\",\n  white: \"#FFFFFF\",\n};\n\n/**\n * @param {number} [pixelRatio=1]\n */\nexport class ColorPicker {\n  /**\n   * @param {number} [pixelRatio]\n   */\n  constructor(pixelRatio = 1) {\n    this.pixelRatio = pixelRatio;\n    this.generated = false;\n    this.centerCoordinates = { x: 289 / 2, y: 289 / 2 };\n    this.r = 289 * 0.49;\n    this.color = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.hueCircle = undefined;\n    this.initialColor = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.previousColor = undefined;\n    this.applied = false;\n\n    // bound by\n    this.updateCallback = () => {};\n    this.closeCallback = () => {};\n\n    // create all DOM elements\n    this._create();\n  }\n\n  /**\n   * this inserts the colorPicker into a div from the DOM\n   * @param {Element} container\n   */\n  insertTo(container) {\n    if (this.hammer !== undefined) {\n      this.hammer.destroy();\n      this.hammer = undefined;\n    }\n    this.container = container;\n    this.container.appendChild(this.frame);\n    this._bindHammer();\n\n    this._setSize();\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   * @param {Function} callback\n   */\n  setUpdateCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.updateCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker update callback is not a function.\",\n      );\n    }\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   * @param {Function} callback\n   */\n  setCloseCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.closeCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker closing callback is not a function.\",\n      );\n    }\n  }\n\n  /**\n   *\n   * @param {string} color\n   * @returns {string}\n   * @private\n   */\n  _isColorString(color) {\n    if (typeof color === \"string\") {\n      return htmlColors[color];\n    }\n  }\n\n  /**\n   * Set the color of the colorPicker\n   * Supported formats:\n   * 'red'                   --> HTML color string\n   * '#ffffff'               --> hex string\n   * 'rgb(255,255,255)'      --> rgb string\n   * 'rgba(255,255,255,1.0)' --> rgba string\n   * {r:255,g:255,b:255}     --> rgb object\n   * {r:255,g:255,b:255,a:1.0} --> rgba object\n   * @param {string | object} color\n   * @param {boolean} [setInitial]\n   */\n  setColor(color, setInitial = true) {\n    if (color === \"none\") {\n      return;\n    }\n\n    let rgba;\n\n    // if a html color shorthand is used, convert to hex\n    const htmlColor = this._isColorString(color);\n    if (htmlColor !== undefined) {\n      color = htmlColor;\n    }\n\n    // check format\n    if (isString(color) === true) {\n      if (isValidRGB(color) === true) {\n        const rgbaArray = color\n          .substr(4)\n          .substr(0, color.length - 5)\n          .split(\",\");\n        rgba = { r: rgbaArray[0], g: rgbaArray[1], b: rgbaArray[2], a: 1.0 };\n      } else if (isValidRGBA(color) === true) {\n        const rgbaArray = color\n          .substr(5)\n          .substr(0, color.length - 6)\n          .split(\",\");\n        rgba = {\n          r: rgbaArray[0],\n          g: rgbaArray[1],\n          b: rgbaArray[2],\n          a: rgbaArray[3],\n        };\n      } else if (isValidHex(color) === true) {\n        const rgbObj = hexToRGB(color);\n        rgba = { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b, a: 1.0 };\n      }\n    } else {\n      if (color instanceof Object) {\n        if (\n          color.r !== undefined &&\n          color.g !== undefined &&\n          color.b !== undefined\n        ) {\n          const alpha = color.a !== undefined ? color.a : \"1.0\";\n          rgba = { r: color.r, g: color.g, b: color.b, a: alpha };\n        }\n      }\n    }\n\n    // set color\n    if (rgba === undefined) {\n      throw new Error(\n        \"Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: \" +\n          JSON.stringify(color),\n      );\n    } else {\n      this._setColor(rgba, setInitial);\n    }\n  }\n\n  /**\n   * this shows the color picker.\n   * The hue circle is constructed once and stored.\n   */\n  show() {\n    if (this.closeCallback !== undefined) {\n      this.closeCallback();\n      this.closeCallback = undefined;\n    }\n\n    this.applied = false;\n    this.frame.style.display = \"block\";\n    this._generateHueCircle();\n  }\n\n  // ------------------------------------------ PRIVATE ----------------------------- //\n\n  /**\n   * Hide the picker. Is called by the cancel button.\n   * Optional boolean to store the previous color for easy access later on.\n   * @param {boolean} [storePrevious]\n   * @private\n   */\n  _hide(storePrevious = true) {\n    // store the previous color for next time;\n    if (storePrevious === true) {\n      this.previousColor = Object.assign({}, this.color);\n    }\n\n    if (this.applied === true) {\n      this.updateCallback(this.initialColor);\n    }\n\n    this.frame.style.display = \"none\";\n\n    // call the closing callback, restoring the onclick method.\n    // this is in a setTimeout because it will trigger the show again before the click is done.\n    setTimeout(() => {\n      if (this.closeCallback !== undefined) {\n        this.closeCallback();\n        this.closeCallback = undefined;\n      }\n    }, 0);\n  }\n\n  /**\n   * bound to the save button. Saves and hides.\n   * @private\n   */\n  _save() {\n    this.updateCallback(this.color);\n    this.applied = false;\n    this._hide();\n  }\n\n  /**\n   * Bound to apply button. Saves but does not close. Is undone by the cancel button.\n   * @private\n   */\n  _apply() {\n    this.applied = true;\n    this.updateCallback(this.color);\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * load the color from the previous session.\n   * @private\n   */\n  _loadLast() {\n    if (this.previousColor !== undefined) {\n      this.setColor(this.previousColor, false);\n    } else {\n      alert(\"There is no last color to load...\");\n    }\n  }\n\n  /**\n   * set the color, place the picker\n   * @param {object} rgba\n   * @param {boolean} [setInitial]\n   * @private\n   */\n  _setColor(rgba, setInitial = true) {\n    // store the initial color\n    if (setInitial === true) {\n      this.initialColor = Object.assign({}, rgba);\n    }\n\n    this.color = rgba;\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n\n    const angleConvert = 2 * Math.PI;\n    const radius = this.r * hsv.s;\n    const x =\n      this.centerCoordinates.x + radius * Math.sin(angleConvert * hsv.h);\n    const y =\n      this.centerCoordinates.y + radius * Math.cos(angleConvert * hsv.h);\n\n    this.colorPickerSelector.style.left =\n      x - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n    this.colorPickerSelector.style.top =\n      y - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n\n    this._updatePicker(rgba);\n  }\n\n  /**\n   * bound to opacity control\n   * @param {number} value\n   * @private\n   */\n  _setOpacity(value) {\n    this.color.a = value / 100;\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * bound to brightness control\n   * @param {number} value\n   * @private\n   */\n  _setBrightness(value) {\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.v = value / 100;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n    this._updatePicker();\n  }\n\n  /**\n   * update the color picker. A black circle overlays the hue circle to mimic the brightness decreasing.\n   * @param {object} rgba\n   * @private\n   */\n  _updatePicker(rgba = this.color) {\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n    const ctx = this.colorPickerCanvas.getContext(\"2d\");\n    if (this.pixelRation === undefined) {\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n    }\n    ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n    // clear the canvas\n    const w = this.colorPickerCanvas.clientWidth;\n    const h = this.colorPickerCanvas.clientHeight;\n    ctx.clearRect(0, 0, w, h);\n\n    ctx.putImageData(this.hueCircle, 0, 0);\n    ctx.fillStyle = \"rgba(0,0,0,\" + (1 - hsv.v) + \")\";\n    ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n    ctx.fill();\n\n    this.brightnessRange.value = 100 * hsv.v;\n    this.opacityRange.value = 100 * rgba.a;\n\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n\n  /**\n   * used by create to set the size of the canvas.\n   * @private\n   */\n  _setSize() {\n    this.colorPickerCanvas.style.width = \"100%\";\n    this.colorPickerCanvas.style.height = \"100%\";\n\n    this.colorPickerCanvas.width = 289 * this.pixelRatio;\n    this.colorPickerCanvas.height = 289 * this.pixelRatio;\n  }\n\n  /**\n   * create all dom elements\n   * TODO: cleanup, lots of similar dom elements\n   * @private\n   */\n  _create() {\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-color-picker\";\n\n    this.colorPickerDiv = document.createElement(\"div\");\n    this.colorPickerSelector = document.createElement(\"div\");\n    this.colorPickerSelector.className = \"vis-selector\";\n    this.colorPickerDiv.appendChild(this.colorPickerSelector);\n\n    this.colorPickerCanvas = document.createElement(\"canvas\");\n    this.colorPickerDiv.appendChild(this.colorPickerCanvas);\n\n    if (!this.colorPickerCanvas.getContext) {\n      const noCanvas = document.createElement(\"DIV\");\n      noCanvas.style.color = \"red\";\n      noCanvas.style.fontWeight = \"bold\";\n      noCanvas.style.padding = \"10px\";\n      noCanvas.innerText = \"Error: your browser does not support HTML canvas\";\n      this.colorPickerCanvas.appendChild(noCanvas);\n    } else {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n      this.colorPickerCanvas\n        .getContext(\"2d\")\n        .setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n    }\n\n    this.colorPickerDiv.className = \"vis-color\";\n\n    this.opacityDiv = document.createElement(\"div\");\n    this.opacityDiv.className = \"vis-opacity\";\n\n    this.brightnessDiv = document.createElement(\"div\");\n    this.brightnessDiv.className = \"vis-brightness\";\n\n    this.arrowDiv = document.createElement(\"div\");\n    this.arrowDiv.className = \"vis-arrow\";\n\n    this.opacityRange = document.createElement(\"input\");\n    try {\n      this.opacityRange.type = \"range\"; // Not supported on IE9\n      this.opacityRange.min = \"0\";\n      this.opacityRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.opacityRange.value = \"100\";\n    this.opacityRange.className = \"vis-range\";\n\n    this.brightnessRange = document.createElement(\"input\");\n    try {\n      this.brightnessRange.type = \"range\"; // Not supported on IE9\n      this.brightnessRange.min = \"0\";\n      this.brightnessRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.brightnessRange.value = \"100\";\n    this.brightnessRange.className = \"vis-range\";\n\n    this.opacityDiv.appendChild(this.opacityRange);\n    this.brightnessDiv.appendChild(this.brightnessRange);\n\n    const me = this;\n    this.opacityRange.onchange = function () {\n      me._setOpacity(this.value);\n    };\n    this.opacityRange.oninput = function () {\n      me._setOpacity(this.value);\n    };\n    this.brightnessRange.onchange = function () {\n      me._setBrightness(this.value);\n    };\n    this.brightnessRange.oninput = function () {\n      me._setBrightness(this.value);\n    };\n\n    this.brightnessLabel = document.createElement(\"div\");\n    this.brightnessLabel.className = \"vis-label vis-brightness\";\n    this.brightnessLabel.innerText = \"brightness:\";\n\n    this.opacityLabel = document.createElement(\"div\");\n    this.opacityLabel.className = \"vis-label vis-opacity\";\n    this.opacityLabel.innerText = \"opacity:\";\n\n    this.newColorDiv = document.createElement(\"div\");\n    this.newColorDiv.className = \"vis-new-color\";\n    this.newColorDiv.innerText = \"new\";\n\n    this.initialColorDiv = document.createElement(\"div\");\n    this.initialColorDiv.className = \"vis-initial-color\";\n    this.initialColorDiv.innerText = \"initial\";\n\n    this.cancelButton = document.createElement(\"div\");\n    this.cancelButton.className = \"vis-button vis-cancel\";\n    this.cancelButton.innerText = \"cancel\";\n    this.cancelButton.onclick = this._hide.bind(this, false);\n\n    this.applyButton = document.createElement(\"div\");\n    this.applyButton.className = \"vis-button vis-apply\";\n    this.applyButton.innerText = \"apply\";\n    this.applyButton.onclick = this._apply.bind(this);\n\n    this.saveButton = document.createElement(\"div\");\n    this.saveButton.className = \"vis-button vis-save\";\n    this.saveButton.innerText = \"save\";\n    this.saveButton.onclick = this._save.bind(this);\n\n    this.loadButton = document.createElement(\"div\");\n    this.loadButton.className = \"vis-button vis-load\";\n    this.loadButton.innerText = \"load last\";\n    this.loadButton.onclick = this._loadLast.bind(this);\n\n    this.frame.appendChild(this.colorPickerDiv);\n    this.frame.appendChild(this.arrowDiv);\n    this.frame.appendChild(this.brightnessLabel);\n    this.frame.appendChild(this.brightnessDiv);\n    this.frame.appendChild(this.opacityLabel);\n    this.frame.appendChild(this.opacityDiv);\n    this.frame.appendChild(this.newColorDiv);\n    this.frame.appendChild(this.initialColorDiv);\n\n    this.frame.appendChild(this.cancelButton);\n    this.frame.appendChild(this.applyButton);\n    this.frame.appendChild(this.saveButton);\n    this.frame.appendChild(this.loadButton);\n  }\n\n  /**\n   * bind hammer to the color picker\n   * @private\n   */\n  _bindHammer() {\n    this.drag = {};\n    this.pinch = {};\n    this.hammer = new Hammer(this.colorPickerCanvas);\n    this.hammer.get(\"pinch\").set({ enable: true });\n\n    this.hammer.on(\"hammer.input\", (event) => {\n      if (event.isFirst) {\n        this._moveSelector(event);\n      }\n    });\n    this.hammer.on(\"tap\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panstart\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panmove\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panend\", (event) => {\n      this._moveSelector(event);\n    });\n  }\n\n  /**\n   * generate the hue circle. This is relatively heavy (200ms) and is done only once on the first time it is shown.\n   * @private\n   */\n  _generateHueCircle() {\n    if (this.generated === false) {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      if (this.pixelRation === undefined) {\n        this.pixelRatio =\n          (window.devicePixelRatio || 1) /\n          (ctx.webkitBackingStorePixelRatio ||\n            ctx.mozBackingStorePixelRatio ||\n            ctx.msBackingStorePixelRatio ||\n            ctx.oBackingStorePixelRatio ||\n            ctx.backingStorePixelRatio ||\n            1);\n      }\n      ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n      // clear the canvas\n      const w = this.colorPickerCanvas.clientWidth;\n      const h = this.colorPickerCanvas.clientHeight;\n      ctx.clearRect(0, 0, w, h);\n\n      // draw hue circle\n      let x, y, hue, sat;\n      this.centerCoordinates = { x: w * 0.5, y: h * 0.5 };\n      this.r = 0.49 * w;\n      const angleConvert = (2 * Math.PI) / 360;\n      const hfac = 1 / 360;\n      const sfac = 1 / this.r;\n      let rgb;\n      for (hue = 0; hue < 360; hue++) {\n        for (sat = 0; sat < this.r; sat++) {\n          x = this.centerCoordinates.x + sat * Math.sin(angleConvert * hue);\n          y = this.centerCoordinates.y + sat * Math.cos(angleConvert * hue);\n          rgb = HSVToRGB(hue * hfac, sat * sfac, 1);\n          ctx.fillStyle = \"rgb(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \")\";\n          ctx.fillRect(x - 0.5, y - 0.5, 2, 2);\n        }\n      }\n      ctx.strokeStyle = \"rgba(0,0,0,1)\";\n      ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n      ctx.stroke();\n\n      this.hueCircle = ctx.getImageData(0, 0, w, h);\n    }\n    this.generated = true;\n  }\n\n  /**\n   * move the selector. This is called by hammer functions.\n   * @param {Event}  event   The event\n   * @private\n   */\n  _moveSelector(event) {\n    const rect = this.colorPickerDiv.getBoundingClientRect();\n    const left = event.center.x - rect.left;\n    const top = event.center.y - rect.top;\n\n    const centerY = 0.5 * this.colorPickerDiv.clientHeight;\n    const centerX = 0.5 * this.colorPickerDiv.clientWidth;\n\n    const x = left - centerX;\n    const y = top - centerY;\n\n    const angle = Math.atan2(x, y);\n    const radius = 0.98 * Math.min(Math.sqrt(x * x + y * y), centerX);\n\n    const newTop = Math.cos(angle) * radius + centerY;\n    const newLeft = Math.sin(angle) * radius + centerX;\n\n    this.colorPickerSelector.style.top =\n      newTop - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n    this.colorPickerSelector.style.left =\n      newLeft - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n\n    // set color\n    let h = angle / (2 * Math.PI);\n    h = h < 0 ? h + 1 : h;\n    const s = radius / this.r;\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.h = h;\n    hsv.s = s;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n\n    // update previews\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n}\n", "import { copyAndExtendArray } from \"../util.ts\";\n\nimport { ColorPicker } from \"./color-picker.js\";\n\n/**\n * Wrap given text (last argument) in HTML elements (all preceding arguments).\n * @param {...any} rest - List of tag names followed by inner text.\n * @returns An element or a text node.\n */\nfunction wrapInTag(...rest) {\n  if (rest.length < 1) {\n    throw new TypeError(\"Invalid arguments.\");\n  } else if (rest.length === 1) {\n    return document.createTextNode(rest[0]);\n  } else {\n    const element = document.createElement(rest[0]);\n    element.appendChild(wrapInTag(...rest.slice(1)));\n    return element;\n  }\n}\n\n/**\n * The way this works is for all properties of this.possible options, you can supply the property name in any form to list the options.\n * Boolean options are recognised as Boolean\n * Number options should be written as array: [default value, min value, max value, stepsize]\n * Colors should be written as array: ['color', '#ffffff']\n * Strings with should be written as array: [option1, option2, option3, ..]\n *\n * The options are matched with their counterparts in each of the modules and the values used in the configuration are\n */\nexport class Configurator {\n  /**\n   * @param {object} parentModule        | the location where parentModule.setOptions() can be called\n   * @param {object} defaultContainer    | the default container of the module\n   * @param {object} configureOptions    | the fully configured and predefined options set found in allOptions.js\n   * @param {number} pixelRatio          | canvas pixel ratio\n   * @param {Function} hideOption        | custom logic to dynamically hide options\n   */\n  constructor(\n    parentModule,\n    defaultContainer,\n    configureOptions,\n    pixelRatio = 1,\n    hideOption = () => false,\n  ) {\n    this.parent = parentModule;\n    this.changedOptions = [];\n    this.container = defaultContainer;\n    this.allowCreation = false;\n    this.hideOption = hideOption;\n\n    this.options = {};\n    this.initialized = false;\n    this.popupCounter = 0;\n    this.defaultOptions = {\n      enabled: false,\n      filter: true,\n      container: undefined,\n      showButton: true,\n    };\n    Object.assign(this.options, this.defaultOptions);\n\n    this.configureOptions = configureOptions;\n    this.moduleOptions = {};\n    this.domElements = [];\n    this.popupDiv = {};\n    this.popupLimit = 5;\n    this.popupHistory = {};\n    this.colorPicker = new ColorPicker(pixelRatio);\n    this.wrapper = undefined;\n  }\n\n  /**\n   * refresh all options.\n   * Because all modules parse their options by themselves, we just use their options. We copy them here.\n   * @param {object} options\n   */\n  setOptions(options) {\n    if (options !== undefined) {\n      // reset the popup history because the indices may have been changed.\n      this.popupHistory = {};\n      this._removePopup();\n\n      let enabled = true;\n      if (typeof options === \"string\") {\n        this.options.filter = options;\n      } else if (Array.isArray(options)) {\n        this.options.filter = options.join();\n      } else if (typeof options === \"object\") {\n        if (options == null) {\n          throw new TypeError(\"options cannot be null\");\n        }\n        if (options.container !== undefined) {\n          this.options.container = options.container;\n        }\n        if (options.filter !== undefined) {\n          this.options.filter = options.filter;\n        }\n        if (options.showButton !== undefined) {\n          this.options.showButton = options.showButton;\n        }\n        if (options.enabled !== undefined) {\n          enabled = options.enabled;\n        }\n      } else if (typeof options === \"boolean\") {\n        this.options.filter = true;\n        enabled = options;\n      } else if (typeof options === \"function\") {\n        this.options.filter = options;\n        enabled = true;\n      }\n      if (this.options.filter === false) {\n        enabled = false;\n      }\n\n      this.options.enabled = enabled;\n    }\n    this._clean();\n  }\n\n  /**\n   *\n   * @param {object} moduleOptions\n   */\n  setModuleOptions(moduleOptions) {\n    this.moduleOptions = moduleOptions;\n    if (this.options.enabled === true) {\n      this._clean();\n      if (this.options.container !== undefined) {\n        this.container = this.options.container;\n      }\n      this._create();\n    }\n  }\n\n  /**\n   * Create all DOM elements\n   * @private\n   */\n  _create() {\n    this._clean();\n    this.changedOptions = [];\n\n    const filter = this.options.filter;\n    let counter = 0;\n    let show = false;\n    for (const option in this.configureOptions) {\n      if (Object.prototype.hasOwnProperty.call(this.configureOptions, option)) {\n        this.allowCreation = false;\n        show = false;\n        if (typeof filter === \"function\") {\n          show = filter(option, []);\n          show =\n            show ||\n            this._handleObject(this.configureOptions[option], [option], true);\n        } else if (filter === true || filter.indexOf(option) !== -1) {\n          show = true;\n        }\n\n        if (show !== false) {\n          this.allowCreation = true;\n\n          // linebreak between categories\n          if (counter > 0) {\n            this._makeItem([]);\n          }\n          // a header for the category\n          this._makeHeader(option);\n\n          // get the sub options\n          this._handleObject(this.configureOptions[option], [option]);\n        }\n        counter++;\n      }\n    }\n    this._makeButton();\n    this._push();\n    //~ this.colorPicker.insertTo(this.container);\n  }\n\n  /**\n   * draw all DOM elements on the screen\n   * @private\n   */\n  _push() {\n    this.wrapper = document.createElement(\"div\");\n    this.wrapper.className = \"vis-configuration-wrapper\";\n    this.container.appendChild(this.wrapper);\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.appendChild(this.domElements[i]);\n    }\n\n    this._showPopupIfNeeded();\n  }\n\n  /**\n   * delete all DOM elements\n   * @private\n   */\n  _clean() {\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.removeChild(this.domElements[i]);\n    }\n\n    if (this.wrapper !== undefined) {\n      this.container.removeChild(this.wrapper);\n      this.wrapper = undefined;\n    }\n    this.domElements = [];\n\n    this._removePopup();\n  }\n\n  /**\n   * get the value from the actualOptions if it exists\n   * @param {Array} path    | where to look for the actual option\n   * @returns {*}\n   * @private\n   */\n  _getValue(path) {\n    let base = this.moduleOptions;\n    for (let i = 0; i < path.length; i++) {\n      if (base[path[i]] !== undefined) {\n        base = base[path[i]];\n      } else {\n        base = undefined;\n        break;\n      }\n    }\n    return base;\n  }\n\n  /**\n   * all option elements are wrapped in an item\n   * @param {Array} path    | where to look for the actual option\n   * @param {Array.<Element>} domElements\n   * @returns {number}\n   * @private\n   */\n  _makeItem(path, ...domElements) {\n    if (this.allowCreation === true) {\n      const item = document.createElement(\"div\");\n      item.className =\n        \"vis-configuration vis-config-item vis-config-s\" + path.length;\n      domElements.forEach((element) => {\n        item.appendChild(element);\n      });\n      this.domElements.push(item);\n      return this.domElements.length;\n    }\n    return 0;\n  }\n\n  /**\n   * header for major subjects\n   * @param {string} name\n   * @private\n   */\n  _makeHeader(name) {\n    const div = document.createElement(\"div\");\n    div.className = \"vis-configuration vis-config-header\";\n    div.innerText = name;\n    this._makeItem([], div);\n  }\n\n  /**\n   * make a label, if it is an object label, it gets different styling.\n   * @param {string} name\n   * @param {Array} path    | where to look for the actual option\n   * @param {string} objectLabel\n   * @returns {HTMLElement}\n   * @private\n   */\n  _makeLabel(name, path, objectLabel = false) {\n    const div = document.createElement(\"div\");\n    div.className =\n      \"vis-configuration vis-config-label vis-config-s\" + path.length;\n    if (objectLabel === true) {\n      while (div.firstChild) {\n        div.removeChild(div.firstChild);\n      }\n      div.appendChild(wrapInTag(\"i\", \"b\", name));\n    } else {\n      div.innerText = name + \":\";\n    }\n    return div;\n  }\n\n  /**\n   * make a dropdown list for multiple possible string optoins\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeDropdown(arr, value, path) {\n    const select = document.createElement(\"select\");\n    select.className = \"vis-configuration vis-config-select\";\n    let selectedValue = 0;\n    if (value !== undefined) {\n      if (arr.indexOf(value) !== -1) {\n        selectedValue = arr.indexOf(value);\n      }\n    }\n\n    for (let i = 0; i < arr.length; i++) {\n      const option = document.createElement(\"option\");\n      option.value = arr[i];\n      if (i === selectedValue) {\n        option.selected = \"selected\";\n      }\n      option.innerText = arr[i];\n      select.appendChild(option);\n    }\n\n    const me = this;\n    select.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, select);\n  }\n\n  /**\n   * make a range object for numeric options\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeRange(arr, value, path) {\n    const defaultValue = arr[0];\n    const min = arr[1];\n    const max = arr[2];\n    const step = arr[3];\n    const range = document.createElement(\"input\");\n    range.className = \"vis-configuration vis-config-range\";\n    try {\n      range.type = \"range\"; // not supported on IE9\n      range.min = min;\n      range.max = max;\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    range.step = step;\n\n    // set up the popup settings in case they are needed.\n    let popupString = \"\";\n    let popupValue = 0;\n\n    if (value !== undefined) {\n      const factor = 1.2;\n      if (value < 0 && value * factor < min) {\n        range.min = Math.ceil(value * factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      } else if (value / factor < min) {\n        range.min = Math.ceil(value / factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      }\n      if (value * factor > max && max !== 1) {\n        range.max = Math.ceil(value * factor);\n        popupValue = range.max;\n        popupString = \"range increased\";\n      }\n      range.value = value;\n    } else {\n      range.value = defaultValue;\n    }\n\n    const input = document.createElement(\"input\");\n    input.className = \"vis-configuration vis-config-rangeinput\";\n    input.value = range.value;\n\n    const me = this;\n    range.onchange = function () {\n      input.value = this.value;\n      me._update(Number(this.value), path);\n    };\n    range.oninput = function () {\n      input.value = this.value;\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    const itemIndex = this._makeItem(path, label, range, input);\n\n    // if a popup is needed AND it has not been shown for this value, show it.\n    if (popupString !== \"\" && this.popupHistory[itemIndex] !== popupValue) {\n      this.popupHistory[itemIndex] = popupValue;\n      this._setupPopup(popupString, itemIndex);\n    }\n  }\n\n  /**\n   * make a button object\n   * @private\n   */\n  _makeButton() {\n    if (this.options.showButton === true) {\n      const generateButton = document.createElement(\"div\");\n      generateButton.className = \"vis-configuration vis-config-button\";\n      generateButton.innerText = \"generate options\";\n      generateButton.onclick = () => {\n        this._printOptions();\n      };\n      generateButton.onmouseover = () => {\n        generateButton.className = \"vis-configuration vis-config-button hover\";\n      };\n      generateButton.onmouseout = () => {\n        generateButton.className = \"vis-configuration vis-config-button\";\n      };\n\n      this.optionsContainer = document.createElement(\"div\");\n      this.optionsContainer.className =\n        \"vis-configuration vis-config-option-container\";\n\n      this.domElements.push(this.optionsContainer);\n      this.domElements.push(generateButton);\n    }\n  }\n\n  /**\n   * prepare the popup\n   * @param {string} string\n   * @param {number} index\n   * @private\n   */\n  _setupPopup(string, index) {\n    if (\n      this.initialized === true &&\n      this.allowCreation === true &&\n      this.popupCounter < this.popupLimit\n    ) {\n      const div = document.createElement(\"div\");\n      div.id = \"vis-configuration-popup\";\n      div.className = \"vis-configuration-popup\";\n      div.innerText = string;\n      div.onclick = () => {\n        this._removePopup();\n      };\n      this.popupCounter += 1;\n      this.popupDiv = { html: div, index: index };\n    }\n  }\n\n  /**\n   * remove the popup from the dom\n   * @private\n   */\n  _removePopup() {\n    if (this.popupDiv.html !== undefined) {\n      this.popupDiv.html.parentNode.removeChild(this.popupDiv.html);\n      clearTimeout(this.popupDiv.hideTimeout);\n      clearTimeout(this.popupDiv.deleteTimeout);\n      this.popupDiv = {};\n    }\n  }\n\n  /**\n   * Show the popup if it is needed.\n   * @private\n   */\n  _showPopupIfNeeded() {\n    if (this.popupDiv.html !== undefined) {\n      const correspondingElement = this.domElements[this.popupDiv.index];\n      const rect = correspondingElement.getBoundingClientRect();\n      this.popupDiv.html.style.left = rect.left + \"px\";\n      this.popupDiv.html.style.top = rect.top - 30 + \"px\"; // 30 is the height;\n      document.body.appendChild(this.popupDiv.html);\n      this.popupDiv.hideTimeout = setTimeout(() => {\n        this.popupDiv.html.style.opacity = 0;\n      }, 1500);\n      this.popupDiv.deleteTimeout = setTimeout(() => {\n        this._removePopup();\n      }, 1800);\n    }\n  }\n\n  /**\n   * make a checkbox for boolean options.\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeCheckbox(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"checkbox\";\n    checkbox.className = \"vis-configuration vis-config-checkbox\";\n    checkbox.checked = defaultValue;\n    if (value !== undefined) {\n      checkbox.checked = value;\n      if (value !== defaultValue) {\n        if (typeof defaultValue === \"object\") {\n          if (value !== defaultValue.enabled) {\n            this.changedOptions.push({ path: path, value: value });\n          }\n        } else {\n          this.changedOptions.push({ path: path, value: value });\n        }\n      }\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.checked, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a text input field for string options.\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeTextInput(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"text\";\n    checkbox.className = \"vis-configuration vis-config-text\";\n    checkbox.value = value;\n    if (value !== defaultValue) {\n      this.changedOptions.push({ path: path, value: value });\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a color field with a color picker for color fields\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeColorField(arr, value, path) {\n    const defaultColor = arr[1];\n    const div = document.createElement(\"div\");\n    value = value === undefined ? defaultColor : value;\n\n    if (value !== \"none\") {\n      div.className = \"vis-configuration vis-config-colorBlock\";\n      div.style.backgroundColor = value;\n    } else {\n      div.className = \"vis-configuration vis-config-colorBlock none\";\n    }\n\n    value = value === undefined ? defaultColor : value;\n    div.onclick = () => {\n      this._showColorPicker(value, div, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, div);\n  }\n\n  /**\n   * used by the color buttons to call the color picker.\n   * @param {number} value\n   * @param {HTMLElement} div\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _showColorPicker(value, div, path) {\n    // clear the callback from this div\n    div.onclick = function () {};\n\n    this.colorPicker.insertTo(div);\n    this.colorPicker.show();\n\n    this.colorPicker.setColor(value);\n    this.colorPicker.setUpdateCallback((color) => {\n      const colorString =\n        \"rgba(\" + color.r + \",\" + color.g + \",\" + color.b + \",\" + color.a + \")\";\n      div.style.backgroundColor = colorString;\n      this._update(colorString, path);\n    });\n\n    // on close of the colorpicker, restore the callback.\n    this.colorPicker.setCloseCallback(() => {\n      div.onclick = () => {\n        this._showColorPicker(value, div, path);\n      };\n    });\n  }\n\n  /**\n   * parse an object and draw the correct items\n   * @param {object} obj\n   * @param {Array} [path]    | where to look for the actual option\n   * @param {boolean} [checkOnly]\n   * @returns {boolean}\n   * @private\n   */\n  _handleObject(obj, path = [], checkOnly = false) {\n    let show = false;\n    const filter = this.options.filter;\n    let visibleInSet = false;\n    for (const subObj in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, subObj)) {\n        show = true;\n        const item = obj[subObj];\n        const newPath = copyAndExtendArray(path, subObj);\n        if (typeof filter === \"function\") {\n          show = filter(subObj, path);\n\n          // if needed we must go deeper into the object.\n          if (show === false) {\n            if (\n              !Array.isArray(item) &&\n              typeof item !== \"string\" &&\n              typeof item !== \"boolean\" &&\n              item instanceof Object\n            ) {\n              this.allowCreation = false;\n              show = this._handleObject(item, newPath, true);\n              this.allowCreation = checkOnly === false;\n            }\n          }\n        }\n\n        if (show !== false) {\n          visibleInSet = true;\n          const value = this._getValue(newPath);\n\n          if (Array.isArray(item)) {\n            this._handleArray(item, value, newPath);\n          } else if (typeof item === \"string\") {\n            this._makeTextInput(item, value, newPath);\n          } else if (typeof item === \"boolean\") {\n            this._makeCheckbox(item, value, newPath);\n          } else if (item instanceof Object) {\n            // skip the options that are not enabled\n            if (!this.hideOption(path, subObj, this.moduleOptions)) {\n              // initially collapse options with an disabled enabled option.\n              if (item.enabled !== undefined) {\n                const enabledPath = copyAndExtendArray(newPath, \"enabled\");\n                const enabledValue = this._getValue(enabledPath);\n                if (enabledValue === true) {\n                  const label = this._makeLabel(subObj, newPath, true);\n                  this._makeItem(newPath, label);\n                  visibleInSet =\n                    this._handleObject(item, newPath) || visibleInSet;\n                } else {\n                  this._makeCheckbox(item, enabledValue, newPath);\n                }\n              } else {\n                const label = this._makeLabel(subObj, newPath, true);\n                this._makeItem(newPath, label);\n                visibleInSet =\n                  this._handleObject(item, newPath) || visibleInSet;\n              }\n            }\n          } else {\n            console.error(\"dont know how to handle\", item, subObj, newPath);\n          }\n        }\n      }\n    }\n    return visibleInSet;\n  }\n\n  /**\n   * handle the array type of option\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _handleArray(arr, value, path) {\n    if (typeof arr[0] === \"string\" && arr[0] === \"color\") {\n      this._makeColorField(arr, value, path);\n      if (arr[1] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"string\") {\n      this._makeDropdown(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"number\") {\n      this._makeRange(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: Number(value) });\n      }\n    }\n  }\n\n  /**\n   * called to update the network with the new settings.\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _update(value, path) {\n    const options = this._constructOptions(value, path);\n\n    if (\n      this.parent.body &&\n      this.parent.body.emitter &&\n      this.parent.body.emitter.emit\n    ) {\n      this.parent.body.emitter.emit(\"configChange\", options);\n    }\n    this.initialized = true;\n    this.parent.setOptions(options);\n  }\n\n  /**\n   *\n   * @param {string | boolean} value\n   * @param {Array.<string>} path\n   * @param {{}} optionsObj\n   * @returns {{}}\n   * @private\n   */\n  _constructOptions(value, path, optionsObj = {}) {\n    let pointer = optionsObj;\n\n    // when dropdown boxes can be string or boolean, we typecast it into correct types\n    value = value === \"true\" ? true : value;\n    value = value === \"false\" ? false : value;\n\n    for (let i = 0; i < path.length; i++) {\n      if (path[i] !== \"global\") {\n        if (pointer[path[i]] === undefined) {\n          pointer[path[i]] = {};\n        }\n        if (i !== path.length - 1) {\n          pointer = pointer[path[i]];\n        } else {\n          pointer[path[i]] = value;\n        }\n      }\n    }\n    return optionsObj;\n  }\n\n  /**\n   * @private\n   */\n  _printOptions() {\n    const options = this.getOptions();\n\n    while (this.optionsContainer.firstChild) {\n      this.optionsContainer.removeChild(this.optionsContainer.firstChild);\n    }\n    this.optionsContainer.appendChild(\n      wrapInTag(\"pre\", \"const options = \" + JSON.stringify(options, null, 2)),\n    );\n  }\n\n  /**\n   *\n   * @returns {{}} options\n   */\n  getOptions() {\n    const options = {};\n    for (let i = 0; i < this.changedOptions.length; i++) {\n      this._constructOptions(\n        this.changedOptions[i].value,\n        this.changedOptions[i].path,\n        options,\n      );\n    }\n    return options;\n  }\n}\n", "import { copyAndExtendArray, copyArray } from \"../util.ts\";\n\nlet errorFound = false;\nlet allOptions;\n\nexport const VALIDATOR_PRINT_STYLE = \"background: #FFeeee; color: #dd0000\";\n\n/**\n *  Used to validate options.\n */\nexport class Validator {\n  /**\n   * Main function to be called\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {object} subObject\n   * @returns {boolean}\n   * @static\n   */\n  static validate(options, referenceOptions, subObject) {\n    errorFound = false;\n    allOptions = referenceOptions;\n    let usedOptions = referenceOptions;\n    if (subObject !== undefined) {\n      usedOptions = referenceOptions[subObject];\n    }\n    Validator.parse(options, usedOptions, []);\n    return errorFound;\n  }\n\n  /**\n   * Will traverse an object recursively and check every value\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static parse(options, referenceOptions, path) {\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option)) {\n        Validator.check(option, options, referenceOptions, path);\n      }\n    }\n  }\n\n  /**\n   * Check every value. If the value is an object, call the parse function on that object.\n   * @param {string} option\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static check(option, options, referenceOptions, path) {\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ === undefined\n    ) {\n      Validator.getSuggestion(option, referenceOptions, path);\n      return;\n    }\n\n    let referenceOption = option;\n    let is_object = true;\n\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ !== undefined\n    ) {\n      // NOTE: This only triggers if the __any__ is in the top level of the options object.\n      //       THAT'S A REALLY BAD PLACE TO ALLOW IT!!!!\n      // TODO: Examine if needed, remove if possible\n\n      // __any__ is a wildcard. Any value is accepted and will be further analysed by reference.\n      referenceOption = \"__any__\";\n\n      // if the any-subgroup is not a predefined object in the configurator,\n      // we do not look deeper into the object.\n      is_object = Validator.getType(options[option]) === \"object\";\n    } else {\n      // Since all options in the reference are objects, we can check whether\n      // they are supposed to be the object to look for the __type__ field.\n      // if this is an object, we check if the correct type has been supplied to account for shorthand options.\n    }\n\n    let refOptionObj = referenceOptions[referenceOption];\n    if (is_object && refOptionObj.__type__ !== undefined) {\n      refOptionObj = refOptionObj.__type__;\n    }\n\n    Validator.checkFields(\n      option,\n      options,\n      referenceOptions,\n      referenceOption,\n      refOptionObj,\n      path,\n    );\n  }\n\n  /**\n   *\n   * @param {string}  option           | the option property\n   * @param {object}  options          | The supplied options object\n   * @param {object}  referenceOptions | The reference options containing all options and their allowed formats\n   * @param {string}  referenceOption  | Usually this is the same as option, except when handling an __any__ tag.\n   * @param {string}  refOptionObj     | This is the type object from the reference options\n   * @param {Array}   path             | where in the object is the option\n   * @static\n   */\n  static checkFields(\n    option,\n    options,\n    referenceOptions,\n    referenceOption,\n    refOptionObj,\n    path,\n  ) {\n    const log = function (message) {\n      console.error(\n        \"%c\" + message + Validator.printLocation(path, option),\n        VALIDATOR_PRINT_STYLE,\n      );\n    };\n\n    const optionType = Validator.getType(options[option]);\n    const refOptionType = refOptionObj[optionType];\n\n    if (refOptionType !== undefined) {\n      // if the type is correct, we check if it is supposed to be one of a few select values\n      if (\n        Validator.getType(refOptionType) === \"array\" &&\n        refOptionType.indexOf(options[option]) === -1\n      ) {\n        log(\n          'Invalid option detected in \"' +\n            option +\n            '\".' +\n            \" Allowed values are:\" +\n            Validator.print(refOptionType) +\n            ' not \"' +\n            options[option] +\n            '\". ',\n        );\n        errorFound = true;\n      } else if (optionType === \"object\" && referenceOption !== \"__any__\") {\n        path = copyAndExtendArray(path, option);\n        Validator.parse(\n          options[option],\n          referenceOptions[referenceOption],\n          path,\n        );\n      }\n    } else if (refOptionObj[\"any\"] === undefined) {\n      // type of the field is incorrect and the field cannot be any\n      log(\n        'Invalid type received for \"' +\n          option +\n          '\". Expected: ' +\n          Validator.print(Object.keys(refOptionObj)) +\n          \". Received [\" +\n          optionType +\n          '] \"' +\n          options[option] +\n          '\"',\n      );\n      errorFound = true;\n    }\n  }\n\n  /**\n   *\n   * @param {object | boolean | number | string | Array.<number> | Date | Node | Moment | undefined | null} object\n   * @returns {string}\n   * @static\n   */\n  static getType(object) {\n    const type = typeof object;\n\n    if (type === \"object\") {\n      if (object === null) {\n        return \"null\";\n      }\n      if (object instanceof Boolean) {\n        return \"boolean\";\n      }\n      if (object instanceof Number) {\n        return \"number\";\n      }\n      if (object instanceof String) {\n        return \"string\";\n      }\n      if (Array.isArray(object)) {\n        return \"array\";\n      }\n      if (object instanceof Date) {\n        return \"date\";\n      }\n      if (object.nodeType !== undefined) {\n        return \"dom\";\n      }\n      if (object._isAMomentObject === true) {\n        return \"moment\";\n      }\n      return \"object\";\n    } else if (type === \"number\") {\n      return \"number\";\n    } else if (type === \"boolean\") {\n      return \"boolean\";\n    } else if (type === \"string\") {\n      return \"string\";\n    } else if (type === undefined) {\n      return \"undefined\";\n    }\n    return type;\n  }\n\n  /**\n   * @param {string} option\n   * @param {object} options\n   * @param {Array.<string>} path\n   * @static\n   */\n  static getSuggestion(option, options, path) {\n    const localSearch = Validator.findInOptions(option, options, path, false);\n    const globalSearch = Validator.findInOptions(option, allOptions, [], true);\n\n    const localSearchThreshold = 8;\n    const globalSearchThreshold = 4;\n\n    let msg;\n    if (localSearch.indexMatch !== undefined) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        'Perhaps it was incomplete? Did you mean: \"' +\n        localSearch.indexMatch +\n        '\"?\\n\\n';\n    } else if (\n      globalSearch.distance <= globalSearchThreshold &&\n      localSearch.distance > globalSearch.distance\n    ) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        \"Perhaps it was misplaced? Matching option found at: \" +\n        Validator.printLocation(\n          globalSearch.path,\n          globalSearch.closestMatch,\n          \"\",\n        );\n    } else if (localSearch.distance <= localSearchThreshold) {\n      msg =\n        '. Did you mean \"' +\n        localSearch.closestMatch +\n        '\"?' +\n        Validator.printLocation(localSearch.path, option);\n    } else {\n      msg =\n        \". Did you mean one of these: \" +\n        Validator.print(Object.keys(options)) +\n        Validator.printLocation(path, option);\n    }\n\n    console.error(\n      '%cUnknown option detected: \"' + option + '\"' + msg,\n      VALIDATOR_PRINT_STYLE,\n    );\n    errorFound = true;\n  }\n\n  /**\n   * traverse the options in search for a match.\n   * @param {string} option\n   * @param {object} options\n   * @param {Array} path    | where to look for the actual option\n   * @param {boolean} [recursive]\n   * @returns {{closestMatch: string, path: Array, distance: number}}\n   * @static\n   */\n  static findInOptions(option, options, path, recursive = false) {\n    let min = 1e9;\n    let closestMatch = \"\";\n    let closestMatchPath = [];\n    const lowerCaseOption = option.toLowerCase();\n    let indexMatch = undefined;\n    for (const op in options) {\n      let distance;\n      if (options[op].__type__ !== undefined && recursive === true) {\n        const result = Validator.findInOptions(\n          option,\n          options[op],\n          copyAndExtendArray(path, op),\n        );\n        if (min > result.distance) {\n          closestMatch = result.closestMatch;\n          closestMatchPath = result.path;\n          min = result.distance;\n          indexMatch = result.indexMatch;\n        }\n      } else {\n        if (op.toLowerCase().indexOf(lowerCaseOption) !== -1) {\n          indexMatch = op;\n        }\n        distance = Validator.levenshteinDistance(option, op);\n        if (min > distance) {\n          closestMatch = op;\n          closestMatchPath = copyArray(path);\n          min = distance;\n        }\n      }\n    }\n    return {\n      closestMatch: closestMatch,\n      path: closestMatchPath,\n      distance: min,\n      indexMatch: indexMatch,\n    };\n  }\n\n  /**\n   * @param {Array.<string>} path\n   * @param {object} option\n   * @param {string} prefix\n   * @returns {string}\n   * @static\n   */\n  static printLocation(path, option, prefix = \"Problem value found at: \\n\") {\n    let str = \"\\n\\n\" + prefix + \"options = {\\n\";\n    for (let i = 0; i < path.length; i++) {\n      for (let j = 0; j < i + 1; j++) {\n        str += \"  \";\n      }\n      str += path[i] + \": {\\n\";\n    }\n    for (let j = 0; j < path.length + 1; j++) {\n      str += \"  \";\n    }\n    str += option + \"\\n\";\n    for (let i = 0; i < path.length + 1; i++) {\n      for (let j = 0; j < path.length - i; j++) {\n        str += \"  \";\n      }\n      str += \"}\\n\";\n    }\n    return str + \"\\n\\n\";\n  }\n\n  /**\n   * @param {object} options\n   * @returns {string}\n   * @static\n   */\n  static print(options) {\n    return JSON.stringify(options)\n      .replace(/(\")|(\\[)|(\\])|(,\"__type__\")/g, \"\")\n      .replace(/(,)/g, \", \");\n  }\n\n  /**\n   *  Compute the edit distance between the two given strings\n   *  http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#JavaScript\n   *\n   *  Copyright (c) 2011 Andrei Mackenzie\n   *\n   *  Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n   *\n   *  The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n   *\n   *  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n   * @param {string} a\n   * @param {string} b\n   * @returns {Array.<Array.<number>>}}\n   * @static\n   */\n  static levenshteinDistance(a, b) {\n    if (a.length === 0) return b.length;\n    if (b.length === 0) return a.length;\n\n    const matrix = [];\n\n    // increment along the first column of each row\n    let i;\n    for (i = 0; i <= b.length; i++) {\n      matrix[i] = [i];\n    }\n\n    // increment each column in the first row\n    let j;\n    for (j = 0; j <= a.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    // Fill in the rest of the matrix\n    for (i = 1; i <= b.length; i++) {\n      for (j = 1; j <= a.length; j++) {\n        if (b.charAt(i - 1) == a.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // substitution\n            Math.min(\n              matrix[i][j - 1] + 1, // insertion\n              matrix[i - 1][j] + 1,\n            ),\n          ); // deletion\n        }\n      }\n    }\n\n    return matrix[b.length][a.length];\n  }\n}\n", null, "/**\n * Popup is a class to create a popup window with some text\n */\nexport class Popup {\n  /**\n   * @param {Element} container       The container object.\n   * @param {string}  overflowMethod  How the popup should act to overflowing ('flip' or 'cap')\n   */\n  constructor(container, overflowMethod) {\n    this.container = container;\n    this.overflowMethod = overflowMethod || \"cap\";\n\n    this.x = 0;\n    this.y = 0;\n    this.padding = 5;\n    this.hidden = false;\n\n    // create the frame\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-tooltip\";\n    this.container.appendChild(this.frame);\n  }\n\n  /**\n   * @param {number} x   Horizontal position of the popup window\n   * @param {number} y   Vertical position of the popup window\n   */\n  setPosition(x, y) {\n    this.x = parseInt(x);\n    this.y = parseInt(y);\n  }\n\n  /**\n   * Set the content for the popup window. This can be HTML code or text.\n   * @param {string | Element} content\n   */\n  setText(content) {\n    if (content instanceof Element) {\n      while (this.frame.firstChild) {\n        this.frame.removeChild(this.frame.firstChild);\n      }\n      this.frame.appendChild(content);\n    } else {\n      // String containing literal text, element has to be used for HTML due to\n      // XSS risks associated with innerHTML (i.e. prevent XSS by accident).\n      this.frame.innerText = content;\n    }\n  }\n\n  /**\n   * Show the popup window\n   * @param {boolean} [doShow]    Show or hide the window\n   */\n  show(doShow) {\n    if (doShow === undefined) {\n      doShow = true;\n    }\n\n    if (doShow === true) {\n      const height = this.frame.clientHeight;\n      const width = this.frame.clientWidth;\n      const maxHeight = this.frame.parentNode.clientHeight;\n      const maxWidth = this.frame.parentNode.clientWidth;\n\n      let left = 0,\n        top = 0;\n\n      if (this.overflowMethod == \"flip\") {\n        let isLeft = false,\n          isTop = true; // Where around the position it's located\n\n        if (this.y - height < this.padding) {\n          isTop = false;\n        }\n\n        if (this.x + width > maxWidth - this.padding) {\n          isLeft = true;\n        }\n\n        if (isLeft) {\n          left = this.x - width;\n        } else {\n          left = this.x;\n        }\n\n        if (isTop) {\n          top = this.y - height;\n        } else {\n          top = this.y;\n        }\n      } else {\n        top = this.y - height;\n        if (top + height + this.padding > maxHeight) {\n          top = maxHeight - height - this.padding;\n        }\n        if (top < this.padding) {\n          top = this.padding;\n        }\n\n        left = this.x;\n        if (left + width + this.padding > maxWidth) {\n          left = maxWidth - width - this.padding;\n        }\n        if (left < this.padding) {\n          left = this.padding;\n        }\n      }\n\n      this.frame.style.left = left + \"px\";\n      this.frame.style.top = top + \"px\";\n      this.frame.style.visibility = \"visible\";\n      this.hidden = false;\n    } else {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the popup window\n   */\n  hide() {\n    this.hidden = true;\n    this.frame.style.left = \"0\";\n    this.frame.style.top = \"0\";\n    this.frame.style.visibility = \"hidden\";\n  }\n\n  /**\n   * Remove the popup window\n   */\n  destroy() {\n    this.frame.parentNode.removeChild(this.frame); // Remove element from DOM\n  }\n}\n", null], "names": ["DELETE", "Symbol", "deepObjectAssign", "values", "merged", "deepObjectAssignNonentry", "stripDelete", "length", "slice", "a", "b", "Date", "setTime", "getTime", "prop", "Reflect", "ownKeys", "Object", "prototype", "propertyIsEnumerable", "call", "Array", "isArray", "clone", "map", "value", "keys", "Hammer", "window", "RealHammer", "noop", "on", "off", "destroy", "emit", "get", "set", "hammerMock", "Activator", "container", "this", "_cleanupQueue", "active", "_dom", "overlay", "document", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hammer", "_onTapOverlay", "bind", "for<PERSON>ach", "event", "srcEvent", "stopPropagation", "body", "_onClick", "element", "parent", "_hasParent", "target", "deactivate", "addEventListener", "removeEventListener", "_escListener", "key", "keyCode", "Emitter", "current", "callback", "splice", "reverse", "activate", "style", "display", "remove", "ASPDateRegex", "fullHexRE", "shortHexRE", "rgbRE", "rgbaRE", "isNumber", "Number", "isString", "String", "isObject", "copyOrDelete", "allowDeletion", "doDeletion", "undefined", "extend", "assign", "deepExtend", "protoExtend", "hasOwnProperty", "getPrototypeOf", "copyAndExtendArray", "arr", "newValue", "copyArray", "toArray", "option", "asBoolean", "defaultValue", "asNumber", "asString", "asSize", "asElement", "hexToRGB", "hex", "result", "exec", "r", "parseInt", "g", "RGBToHex", "red", "green", "blue", "toString", "RGBToHSV", "minRGB", "Math", "min", "maxRGB", "max", "h", "s", "v", "splitCSSText", "cssText", "tmpEllement", "styles", "i", "getPropertyValue", "HSVToRGB", "floor", "f", "p", "q", "t", "HSVToHex", "rgb", "hexToHSV", "TypeError", "isValidHex", "test", "isValidRGB", "isValidRGBA", "rgba", "bridgeObject", "referenceObject", "Element", "objectTo", "create", "easingFunctions", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "htmlColors", "black", "navy", "darkblue", "mediumblue", "darkgreen", "teal", "dark<PERSON>an", "deepskyblue", "darkturquoise", "mediumspringgreen", "lime", "springgreen", "aqua", "cyan", "midnightblue", "dodgerblue", "lightseagreen", "forestgreen", "seagreen", "darkslategray", "limegreen", "mediumseagreen", "turquoise", "royalblue", "steelblue", "darkslateblue", "mediumturquoise", "indigo", "darkolivegreen", "cadetblue", "cornflowerblue", "mediumaquamarine", "dimgray", "slateblue", "<PERSON><PERSON><PERSON>", "slategray", "lightslategray", "mediumslateblue", "lawngreen", "chartreuse", "aquamarine", "maroon", "purple", "olive", "gray", "skyblue", "lightskyblue", "blueviolet", "darkred", "darkmagenta", "saddlebrown", "darkseagreen", "lightgreen", "mediumpurple", "darkviolet", "palegreen", "darkorchid", "yellowgreen", "sienna", "brown", "darkgray", "lightblue", "greenyellow", "paleturquoise", "lightsteelblue", "powderblue", "firebrick", "darkgoldenrod", "mediumorchid", "rosybrown", "<PERSON><PERSON><PERSON>", "silver", "mediumvioletred", "indianred", "peru", "chocolate", "tan", "<PERSON><PERSON>rey", "palevioletred", "thistle", "orchid", "goldenrod", "crimson", "gainsboro", "plum", "burlywood", "lightcyan", "lavender", "<PERSON><PERSON><PERSON>", "violet", "palegoldenrod", "lightcoral", "khaki", "aliceblue", "honeydew", "azure", "sandybrown", "wheat", "beige", "whitesmoke", "mintcream", "ghostwhite", "salmon", "antiquewhite", "linen", "lightgoldenrodyellow", "oldlace", "fuchsia", "magenta", "deeppink", "orangered", "tomato", "hotpink", "coral", "darkorange", "<PERSON><PERSON><PERSON>", "orange", "lightpink", "pink", "gold", "peachpuff", "navajowhite", "moccasin", "bisque", "mistyrose", "blanche<PERSON><PERSON>", "papayawhip", "lavenderblush", "seashell", "cornsilk", "lemon<PERSON>ffon", "<PERSON><PERSON><PERSON><PERSON>", "snow", "yellow", "lightyellow", "ivory", "white", "ColorPicker$1", "constructor", "pixelRatio", "generated", "centerCoordinates", "x", "y", "color", "hueCircle", "initialColor", "previousColor", "applied", "updateCallback", "closeCallback", "_create", "insertTo", "frame", "_<PERSON><PERSON><PERSON><PERSON>", "_setSize", "setUpdateCallback", "Error", "setCloseCallback", "_isColorString", "setColor", "setInitial", "htmlColor", "rgbaArray", "substr", "split", "rgbObj", "alpha", "JSON", "stringify", "_setColor", "show", "_generateHueCircle", "_hide", "storePrevious", "setTimeout", "_save", "_apply", "_updatePicker", "_loadLast", "alert", "hsv", "angleConvert", "PI", "radius", "sin", "cos", "colorPickerSelector", "left", "clientWidth", "top", "clientHeight", "_setOpacity", "_setBrightness", "ctx", "colorPickerCanvas", "getContext", "pixelRation", "devicePixelRatio", "webkitBackingStorePixelRatio", "mozBackingStorePixelRatio", "msBackingStorePixelRatio", "oBackingStorePixelRatio", "backingStorePixelRatio", "setTransform", "w", "clearRect", "putImageData", "fillStyle", "circle", "fill", "brightnessRange", "opacityRange", "initialColorDiv", "backgroundColor", "newColorDiv", "width", "height", "className", "colorPickerDiv", "noCanvas", "fontWeight", "padding", "innerText", "opacityDiv", "brightnessDiv", "arrowDiv", "type", "err", "me", "onchange", "oninput", "brightnessLabel", "opacityLabel", "cancelButton", "onclick", "applyButton", "saveButton", "loadButton", "drag", "pinch", "enable", "<PERSON><PERSON><PERSON><PERSON>", "_moveSelector", "hue", "sat", "hfac", "sfac", "fillRect", "strokeStyle", "stroke", "getImageData", "rect", "getBoundingClientRect", "center", "centerY", "centerX", "angle", "atan2", "sqrt", "newTop", "newLeft", "wrapInTag", "rest", "createTextNode", "allOptions", "errorFound", "VALIDATOR_PRINT_STYLE", "ActivatorJS", "ColorPicker", "ColorPickerJS", "Configurator", "parentModule", "defaultContainer", "configureOptions", "hideOption", "changedOptions", "allowCreation", "options", "initialized", "popup<PERSON><PERSON>nter", "defaultOptions", "enabled", "filter", "showButton", "moduleOptions", "dom<PERSON><PERSON>s", "popupDiv", "popupLimit", "popupHistory", "colorPicker", "wrapper", "setOptions", "_removePopup", "join", "_clean", "setModuleOptions", "counter", "_handleObject", "indexOf", "_makeItem", "_makeHeader", "_makeButton", "_push", "_showPopupIfNeeded", "_getValue", "path", "base", "item", "name", "div", "_make<PERSON><PERSON>l", "objectLabel", "<PERSON><PERSON><PERSON><PERSON>", "_makeDropdown", "select", "selected<PERSON><PERSON><PERSON>", "selected", "_update", "label", "_makeRange", "step", "range", "popupString", "popupValue", "factor", "ceil", "input", "itemIndex", "_setupPopup", "generateButton", "_printOptions", "on<PERSON><PERSON>ver", "onmouseout", "optionsContainer", "string", "index", "id", "html", "clearTimeout", "hideTimeout", "deleteTimeout", "opacity", "_makeCheckbox", "checkbox", "checked", "_makeTextInput", "_makeColorField", "defaultColor", "_showColorPicker", "colorString", "obj", "checkOnly", "visibleInSet", "subObj", "newPath", "_handleArray", "enabledPath", "enabledValue", "console", "error", "_constructOptions", "emitter", "optionsObj", "pointer", "getOptions", "HammerJS", "Popup", "overflowMethod", "hidden", "setPosition", "setText", "content", "doShow", "maxHeight", "max<PERSON><PERSON><PERSON>", "isLeft", "isTop", "visibility", "hide", "VALIDATOR_PRINT_STYLE_JS", "Validator", "validate", "referenceOptions", "subObject", "usedOptions", "parse", "check", "__any__", "getSuggestion", "referenceOption", "is_object", "getType", "refOptionObj", "__type__", "checkFields", "log", "message", "printLocation", "optionType", "refOptionType", "print", "object", "Boolean", "nodeType", "_isAMomentObject", "localSearch", "findInOptions", "globalSearch", "msg", "indexMatch", "distance", "closestMatch", "recursive", "closestMatchPath", "lowerCaseOption", "toLowerCase", "op", "levenshteinDistance", "prefix", "str", "j", "replace", "matrix", "char<PERSON>t", "seed", "s0", "s1", "s2", "mash", "n", "data", "charCodeAt", "<PERSON><PERSON>", "mash<PERSON><PERSON>", "c", "random", "uint32", "fract53", "algorithm", "version", "AleaImplementation", "now", "elem", "classNames", "classes", "newClasses", "concat", "includes", "cssStyle", "entries", "setProperty", "orderedItems", "comparator", "field", "field2", "iteration", "low", "high", "middle", "searchResult", "sidePreference", "prevValue", "nextValue", "len", "fillIfDefined", "aProp", "bProp", "right", "inner", "outer", "position", "overflow", "w1", "offsetWidth", "w2", "srcElement", "compare", "k", "isNaN", "mergeTarget", "globalOptions", "isPresent", "srcOption", "globalOption", "isEmpty", "globalEnabled", "src", "dst", "doMerge", "inputColor", "colorStr", "lighterColorHSV", "darkerColorHSV", "darkerColorHex", "lighterColorHex", "background", "border", "highlight", "hover", "preventDefault", "returnValue", "updates", "recursiveDOMDelete", "DOMobject", "hasChildNodes", "child", "oldClasses", "removeProperty", "fields", "props", "others", "other", "propsToExclude", "fn", "scheduled", "requestAnimationFrame", "pile", "accessors", "candidate", "member"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;6VAGaA,EAASC,OAAO,UA6CvB,SAAUC,KAAoBC,GAClC,MAAMC,EAASC,KAA4BF,GAE3C,OADAG,EAAYF,GACLA,CACT,CASA,SAASC,KAA4BF,GACnC,GAAIA,EAAOI,OAAS,EAClB,OAAOJ,EAAO,GACT,GAAIA,EAAOI,OAAS,EACzB,OAAOF,EACLH,EAAiBC,EAAO,GAAIA,EAAO,OAChCA,EAAOK,MAAM,IAIpB,MAAMC,EAAIN,EAAO,GACXO,EAAIP,EAAO,GAEjB,GAAIM,aAAaE,MAAQD,aAAaC,KAEpC,OADAF,EAAEG,QAAQF,EAAEG,WACLJ,EAGT,IAAK,MAAMK,KAAQC,QAAQC,QAAQN,GAC5BO,OAAOC,UAAUC,qBAAqBC,KAAKV,EAAGI,KAExCJ,EAAEI,KAAUd,SACdS,EAAEK,GAEG,OAAZL,EAAEK,IACU,OAAZJ,EAAEI,IACiB,iBAAZL,EAAEK,IACU,iBAAZJ,EAAEI,IACRO,MAAMC,QAAQb,EAAEK,KAChBO,MAAMC,QAAQZ,EAAEI,IAIjBL,EAAEK,GAAQS,EAAMb,EAAEI,IAFlBL,EAAEK,GAAQT,EAAyBI,EAAEK,GAAOJ,EAAEI,KAMlD,OAAOL,CACT,CAOA,SAASc,EAAMd,GACb,OAAIY,MAAMC,QAAQb,GACTA,EAAEe,IAAKC,GAAoBF,EAAME,IAClB,iBAANhB,GAAwB,OAANA,EAC9BA,aAAaE,KACR,IAAIA,KAAKF,EAAEI,WAEbR,EAAyB,CAAA,EAAII,GAE7BA,CAEX,CAMA,SAASH,EAAYG,GACnB,IAAK,MAAMK,KAAQG,OAAOS,KAAKjB,GACzBA,EAAEK,KAAUd,SACPS,EAAEK,GACmB,iBAAZL,EAAEK,IAAkC,OAAZL,EAAEK,IAC1CR,EAAYG,EAAEK,GAGpB,CC1GA,MAAMa,EACc,oBAAXC,OACHA,OAAOD,QAAUE,EACjB,WAEE,OAtBR,WACE,MAAMC,EAAO,OAEb,MAAO,CACLC,GAAID,EACJE,IAAKF,EACLG,QAASH,EACTI,KAAMJ,EAENK,IAAG,KACM,CACLC,IAAKN,IAIb,CAOeO,EACT,EClBC,SAASC,EAAUC,GACxBC,KAAKC,cAAgB,GAErBD,KAAKE,QAAS,EAEdF,KAAKG,KAAO,CACVJ,YACAK,QAASC,SAASC,cAAc,QAGlCN,KAAKG,KAAKC,QAAQG,UAAUC,IAAI,eAEhCR,KAAKG,KAAKJ,UAAUU,YAAYT,KAAKG,KAAKC,SAC1CJ,KAAKC,cAAcS,KAAK,KACtBV,KAAKG,KAAKC,QAAQO,WAAWC,YAAYZ,KAAKG,KAAKC,WAGrD,MAAMS,EAAS1B,EAAOa,KAAKG,KAAKC,SAChCS,EAAOtB,GAAG,MAAOS,KAAKc,cAAcC,KAAKf,OACzCA,KAAKC,cAAcS,KAAK,KACtBG,EAAOpB,YAMM,CACb,MACA,YACA,QACA,QACA,MACA,WACA,UACA,UAEKuB,QAASC,IACdJ,EAAOtB,GAAG0B,EAAQA,IAChBA,EAAMC,SAASC,sBAKfd,UAAYA,SAASe,OACvBpB,KAAKqB,SAAYJ,KAiGrB,SAAoBK,EAASC,GAC3B,KAAOD,GAAS,CACd,GAAIA,IAAYC,EACd,OAAO,EAETD,EAAUA,EAAQX,UACpB,CACA,OAAO,CACT,EAxGWa,CAAWP,EAAMQ,OAAQ1B,IAC5BC,KAAK0B,cAGTrB,SAASe,KAAKO,iBAAiB,QAAS3B,KAAKqB,UAC7CrB,KAAKC,cAAcS,KAAK,KACtBL,SAASe,KAAKQ,oBAAoB,QAAS5B,KAAKqB,aAKpDrB,KAAK6B,aAAgBZ,KAEjB,QAASA,EACS,WAAdA,EAAMa,IACY,KAAlBb,EAAMc,UAEV/B,KAAK0B,aAGX,CAGAM,EAAQlC,EAAUpB,WAGlBoB,EAAUmC,QAAU,KAKpBnC,EAAUpB,UAAUe,QAAU,WAC5BO,KAAK0B,aAEL,IAAK,MAAMQ,KAAYlC,KAAKC,cAAckC,OAAO,GAAGC,UAClDF,GAEJ,EAMApC,EAAUpB,UAAU2D,SAAW,WAEzBvC,EAAUmC,SACZnC,EAAUmC,QAAQP,aAEpB5B,EAAUmC,QAAUjC,KAEpBA,KAAKE,QAAS,EACdF,KAAKG,KAAKC,QAAQkC,MAAMC,QAAU,OAClCvC,KAAKG,KAAKJ,UAAUQ,UAAUC,IAAI,cAElCR,KAAKN,KAAK,UACVM,KAAKN,KAAK,YAIVW,SAASe,KAAKO,iBAAiB,UAAW3B,KAAK6B,aACjD,EAMA/B,EAAUpB,UAAUgD,WAAa,WAC/B1B,KAAKE,QAAS,EACdF,KAAKG,KAAKC,QAAQkC,MAAMC,QAAU,QAClCvC,KAAKG,KAAKJ,UAAUQ,UAAUiC,OAAO,cACrCnC,SAASe,KAAKQ,oBAAoB,UAAW5B,KAAK6B,cAElD7B,KAAKN,KAAK,UACVM,KAAKN,KAAK,aACZ,EAOAI,EAAUpB,UAAUoC,cAAgB,SAAUG,GAE5CjB,KAAKqC,WACLpB,EAAMC,SAASC,iBACjB,EC1IA,MAAMsB,EAAe,qBAGfC,EAAY,4CACZC,EAAa,mCACbC,EACJ,+GACIC,EACJ,mIAiEI,SAAUC,EAAS7D,GACvB,OAAOA,aAAiB8D,QAA2B,iBAAV9D,CAC3C,CAuBM,SAAU+D,EAAS/D,GACvB,OAAOA,aAAiBgE,QAA2B,iBAAVhE,CAC3C,CAOM,SAAUiE,EAASjE,GACvB,MAAwB,iBAAVA,GAAgC,OAAVA,CACtC,CAiCA,SAASkE,EACPlF,EACAC,EACAI,EACA8E,GAEA,IAAIC,GAAa,GACK,IAAlBD,IACFC,EAAyB,OAAZnF,EAAEI,SAA8BgF,IAAZrF,EAAEK,IAGjC+E,SACKpF,EAAEK,GAETL,EAAEK,GAAQJ,EAAEI,EAEhB,CAyCO,MAAMiF,EAAS9E,OAAO+E,OA+IvB,SAAUC,EACdxF,EACAC,EACAwF,GAAc,EACdN,GAAgB,GAEhB,IAAK,MAAM9E,KAAQJ,GACbO,OAAOC,UAAUiF,eAAe/E,KAAKV,EAAGI,KAAyB,IAAhBoF,KAE9B,iBAAZxF,EAAEI,IACG,OAAZJ,EAAEI,IACFG,OAAOmF,eAAe1F,EAAEI,MAAWG,OAAOC,eAE1B4E,IAAZrF,EAAEK,GACJL,EAAEK,GAAQmF,EAAW,CAAA,EAAIvF,EAAEI,GAAOoF,GAEf,iBAAZzF,EAAEK,IACG,OAAZL,EAAEK,IACFG,OAAOmF,eAAe3F,EAAEK,MAAWG,OAAOC,UAE1C+E,EAAWxF,EAAEK,GAAOJ,EAAEI,GAAOoF,GAE7BP,EAAalF,EAAGC,EAAGI,EAAM8E,GAElBvE,MAAMC,QAAQZ,EAAEI,IACzBL,EAAEK,GAAQJ,EAAEI,GAAMN,QAElBmF,EAAalF,EAAGC,EAAGI,EAAM8E,IAI/B,OAAOnF,CACT,CA+EM,SAAU4F,EACdC,EACAC,GAEA,MAAO,IAAID,EAAKC,EAClB,CAOM,SAAUC,EAAaF,GAC3B,OAAOA,EAAI9F,OACb,CAgGO,MAAMiG,EAAUxF,OAAOd,OAqHvB,MAAMuG,EAAS,CAOpBC,UAAS,CAAClF,EAAgBmF,KACJ,mBAATnF,IACTA,EAAQA,KAGG,MAATA,EACc,GAATA,EAGFmF,GAAgB,MASzBC,SAAQ,CAACpF,EAAgBmF,KACH,mBAATnF,IACTA,EAAQA,KAGG,MAATA,EACK8D,OAAO9D,IAAUmF,GAAgB,KAGnCA,GAAgB,MASzBE,SAAQ,CAACrF,EAAgBmF,KACH,mBAATnF,IACTA,EAAQA,KAGG,MAATA,EACKgE,OAAOhE,GAGTmF,GAAgB,MASzBG,OAAM,CAACtF,EAAgBmF,KACD,mBAATnF,IACTA,EAAQA,KAGN+D,EAAS/D,GACJA,EACE6D,EAAS7D,GACXA,EAAQ,KAERmF,GAAgB,MAU3BI,UAAS,CACPvF,EACAmF,KAEoB,mBAATnF,IACTA,EAAQA,KAGHA,GAASmF,GAAgB,OAW9B,SAAUK,EAASC,GACvB,IAAIC,EACJ,OAAQD,EAAI3G,QACV,KAAK,EACL,KAAK,EAEH,OADA4G,EAAShC,EAAWiC,KAAKF,GAClBC,EACH,CACEE,EAAGC,SAASH,EAAO,GAAKA,EAAO,GAAI,IACnCI,EAAGD,SAASH,EAAO,GAAKA,EAAO,GAAI,IACnCzG,EAAG4G,SAASH,EAAO,GAAKA,EAAO,GAAI,KAErC,KACN,KAAK,EACL,KAAK,EAEH,OADAA,EAASjC,EAAUkC,KAAKF,GACjBC,EACH,CACEE,EAAGC,SAASH,EAAO,GAAI,IACvBI,EAAGD,SAASH,EAAO,GAAI,IACvBzG,EAAG4G,SAASH,EAAO,GAAI,KAEzB,KACN,QACE,OAAO,KAEb,UAkCgBK,EAASC,EAAaC,EAAeC,GACnD,MACE,MAAQ,GAAK,KAAOF,GAAO,KAAOC,GAAS,GAAKC,GAAMC,SAAS,IAAIpH,MAAM,EAE7E,UAwLgBqH,EAASJ,EAAaC,EAAeC,GACnDF,GAAY,IACZC,GAAgB,IAChBC,GAAc,IACd,MAAMG,EAASC,KAAKC,IAAIP,EAAKM,KAAKC,IAAIN,EAAOC,IACvCM,EAASF,KAAKG,IAAIT,EAAKM,KAAKG,IAAIR,EAAOC,IAG7C,GAAIG,IAAWG,EACb,MAAO,CAAEE,EAAG,EAAGC,EAAG,EAAGC,EAAGP,GAU1B,MAAO,CAAEK,EAHI,KADHV,IAAQK,EAAS,EAAIH,IAASG,EAAS,EAAI,IADnDL,IAAQK,EAASJ,EAAQC,EAAOA,IAASG,EAASL,EAAMC,EAAQC,EAAOF,IAE7CQ,EAASH,IAAY,IAGhCM,GAFGH,EAASH,GAAUG,EAEPI,EADlBJ,EAEhB,CAWA,SAASK,EAAaC,GACpB,MAAMC,EAAc3F,SAASC,cAAc,OAErC2F,EAAoB,CAAA,EAE1BD,EAAY1D,MAAMyD,QAAUA,EAE5B,IAAK,IAAIG,EAAI,EAAGA,EAAIF,EAAY1D,MAAMvE,SAAUmI,EAC9CD,EAAOD,EAAY1D,MAAM4D,IAAMF,EAAY1D,MAAM6D,iBAC/CH,EAAY1D,MAAM4D,IAItB,OAAOD,CACT,UAmCgBG,EAAST,EAAWC,EAAWC,GAC7C,IAAIhB,EACAE,EACA7G,EAEJ,MAAMgI,EAAIX,KAAKc,MAAU,EAAJV,GACfW,EAAQ,EAAJX,EAAQO,EACZK,EAAIV,GAAK,EAAID,GACbY,EAAIX,GAAK,EAAIS,EAAIV,GACjBa,EAAIZ,GAAK,GAAK,EAAIS,GAAKV,GAE7B,OAAQM,EAAI,GACV,KAAK,EACDrB,EAAIgB,EAAKd,EAAI0B,EAAKvI,EAAIqI,EACxB,MACF,KAAK,EACD1B,EAAI2B,EAAKzB,EAAIc,EAAK3H,EAAIqI,EACxB,MACF,KAAK,EACD1B,EAAI0B,EAAKxB,EAAIc,EAAK3H,EAAIuI,EACxB,MACF,KAAK,EACD5B,EAAI0B,EAAKxB,EAAIyB,EAAKtI,EAAI2H,EACxB,MACF,KAAK,EACDhB,EAAI4B,EAAK1B,EAAIwB,EAAKrI,EAAI2H,EACxB,MACF,KAAK,EACDhB,EAAIgB,EAAKd,EAAIwB,EAAKrI,EAAIsI,EAI5B,MAAO,CACL3B,EAAGU,KAAKc,MAAsB,IAAfxB,GACfE,EAAGQ,KAAKc,MAAsB,IAAftB,GACf7G,EAAGqH,KAAKc,MAAsB,IAAfnI,GAEnB,UASgBwI,EAASf,EAAWC,EAAWC,GAC7C,MAAMc,EAAMP,EAAST,EAAGC,EAAGC,GAC3B,OAAOb,EAAS2B,EAAI9B,EAAG8B,EAAI5B,EAAG4B,EAAIzI,EACpC,CAOM,SAAU0I,EAASlC,GACvB,MAAMiC,EAAMlC,EAASC,GACrB,IAAKiC,EACH,MAAM,IAAIE,UAAU,IAAInC,4BAE1B,OAAOW,EAASsB,EAAI9B,EAAG8B,EAAI5B,EAAG4B,EAAIzI,EACpC,CAOM,SAAU4I,EAAWpC,GAEzB,MADa,qCAAqCqC,KAAKrC,EAEzD,CAOM,SAAUsC,EAAWL,GACzB,OAAO/D,EAAMmE,KAAKJ,EACpB,CAOM,SAAUM,EAAYC,GAC1B,OAAOrE,EAAOkE,KAAKG,EACrB,CAqCM,SAAUC,EACdC,GAEA,GAAwB,OAApBA,GAAuD,iBAApBA,EACrC,OAAO,KAGT,GAAIA,aAA2BC,QAE7B,OAAOD,EAGT,MAAME,EAAW7I,OAAO8I,OAAOH,GAC/B,IAAK,MAAMlB,KAAKkB,EACV3I,OAAOC,UAAUiF,eAAe/E,KAAKwI,EAAiBlB,IACd,iBAA9BkB,EAAwBlB,KAClCoB,EAASpB,GAAKiB,EAAcC,EAAwBlB,KAK1D,OAAOoB,CACT,CAiSO,MAAME,EAAkB,CAM7BC,OAAOhB,GACEA,EAQTiB,WAAWjB,GACFA,EAAIA,EAQbkB,YAAYlB,GACHA,GAAK,EAAIA,GAQlBmB,cAAcnB,GACLA,EAAI,GAAM,EAAIA,EAAIA,GAAU,EAAI,EAAIA,GAAKA,EAAnB,EAQ/BoB,YAAYpB,GACHA,EAAIA,EAAIA,EAQjBqB,aAAarB,KACFA,EAAIA,EAAIA,EAAI,EAQvBsB,eAAetB,GACNA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,GAAKA,EAAI,IAAM,EAAIA,EAAI,IAAM,EAAIA,EAAI,GAAK,EAQzEuB,YAAYvB,GACHA,EAAIA,EAAIA,EAAIA,EAQrBwB,aAAaxB,GACJ,KAAMA,EAAIA,EAAIA,EAAIA,EAQ3ByB,eAAezB,GACNA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,IAAMA,EAAIA,EAAIA,EAAIA,EAQ7D0B,YAAY1B,GACHA,EAAIA,EAAIA,EAAIA,EAAIA,EAQzB2B,aAAa3B,GACJ,IAAMA,EAAIA,EAAIA,EAAIA,EAAIA,EAQ/B4B,eAAe5B,GACNA,EAAI,GAAM,GAAKA,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,KAAOA,EAAIA,EAAIA,EAAIA,EAAIA,GCvnDzE,MAAM6B,EAAa,CACjBC,MAAO,UACPC,KAAM,UACNC,SAAU,UACVC,WAAY,UACZvD,KAAM,UACNwD,UAAW,UACXzD,MAAO,UACP0D,KAAM,UACNC,SAAU,UACVC,YAAa,UACbC,cAAe,UACfC,kBAAmB,UACnBC,KAAM,UACNC,YAAa,UACbC,KAAM,UACNC,KAAM,UACNC,aAAc,UACdC,WAAY,UACZC,cAAe,UACfC,YAAa,UACbC,SAAU,UACVC,cAAe,UACfC,UAAW,UACXC,eAAgB,UAChBC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,cAAe,UACfC,gBAAiB,UACjBC,OAAQ,UACRC,eAAgB,UAChBC,UAAW,UACXC,eAAgB,UAChBC,iBAAkB,UAClBC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,eAAgB,UAChBC,gBAAiB,UACjBC,UAAW,UACXC,WAAY,UACZC,WAAY,UACZC,OAAQ,UACRC,OAAQ,UACRC,MAAO,UACPC,KAAM,UACNC,QAAS,UACTC,aAAc,UACdC,WAAY,UACZC,QAAS,UACTC,YAAa,UACbC,YAAa,UACbC,aAAc,UACdC,WAAY,UACZC,aAAc,UACdC,WAAY,UACZC,UAAW,UACXC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,MAAO,UACPC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,eAAgB,UAChBC,WAAY,UACZC,UAAW,UACXC,cAAe,UACfC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,OAAQ,UACRC,gBAAiB,UACjBC,UAAW,UACXC,KAAM,UACNC,UAAW,UACXC,IAAK,UACLC,UAAW,UACXC,cAAe,UACfC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,QAAS,UACTC,UAAW,UACXC,KAAM,UACNC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,WAAY,UACZC,OAAQ,UACRC,cAAe,UACfC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,SAAU,UACVC,MAAO,UACPC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,WAAY,UACZC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,aAAc,UACdC,MAAO,UACPC,qBAAsB,UACtBC,QAAS,UACThK,IAAK,UACLiK,QAAS,UACTC,QAAS,UACTC,SAAU,UACVC,UAAW,UACXC,OAAQ,UACRC,QAAS,UACTC,MAAO,UACPC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,UAAW,UACXC,YAAa,UACbC,SAAU,UACVC,OAAQ,UACRC,UAAW,UACXC,eAAgB,UAChBC,WAAY,UACZC,cAAe,UACfC,SAAU,UACVC,SAAU,UACVC,aAAc,UACdC,YAAa,UACbC,KAAM,UACNC,OAAQ,UACRC,YAAa,UACbC,MAAO,UACPC,MAAO,WAMF,IAAAC,EAAA,MAIL,WAAAC,CAAYC,EAAa,GACvBlR,KAAKkR,WAAaA,EAClBlR,KAAKmR,WAAY,EACjBnR,KAAKoR,kBAAoB,CAAEC,EAAG,MAASC,EAAG,OAC1CtR,KAAK6E,EAAI,IAAM,IACf7E,KAAKuR,MAAQ,CAAE1M,EAAG,IAAKE,EAAG,IAAK7G,EAAG,IAAKD,EAAG,GAC1C+B,KAAKwR,eAAYlO,EACjBtD,KAAKyR,aAAe,CAAE5M,EAAG,IAAKE,EAAG,IAAK7G,EAAG,IAAKD,EAAG,GACjD+B,KAAK0R,mBAAgBpO,EACrBtD,KAAK2R,SAAU,EAGf3R,KAAK4R,eAAiB,OACtB5R,KAAK6R,cAAgB,OAGrB7R,KAAK8R,SACP,CAMA,QAAAC,CAAShS,QACauD,IAAhBtD,KAAKa,SACPb,KAAKa,OAAOpB,UACZO,KAAKa,YAASyC,GAEhBtD,KAAKD,UAAYA,EACjBC,KAAKD,UAAUU,YAAYT,KAAKgS,OAChChS,KAAKiS,cAELjS,KAAKkS,UACP,CAMA,iBAAAC,CAAkBjQ,GAChB,GAAwB,mBAAbA,EAGT,MAAM,IAAIkQ,MACR,+EAHFpS,KAAK4R,eAAiB1P,CAM1B,CAMA,gBAAAmQ,CAAiBnQ,GACf,GAAwB,mBAAbA,EAGT,MAAM,IAAIkQ,MACR,gFAHFpS,KAAK6R,cAAgB3P,CAMzB,CAQA,cAAAoQ,CAAef,GACb,GAAqB,iBAAVA,EACT,OAAOjJ,EAAWiJ,EAEtB,CAcA,QAAAgB,CAAShB,EAAOiB,GAAa,GAC3B,GAAc,SAAVjB,EACF,OAGF,IAAIrK,EAGJ,MAAMuL,EAAYzS,KAAKsS,eAAef,GAMtC,QALkBjO,IAAdmP,IACFlB,EAAQkB,IAIc,IAApBzP,EAASuO,IACX,IAA0B,IAAtBvK,EAAWuK,GAAiB,CAC9B,MAAMmB,EAAYnB,EACfoB,OAAO,GACPA,OAAO,EAAGpB,EAAMxT,OAAS,GACzB6U,MAAM,KACT1L,EAAO,CAAErC,EAAG6N,EAAU,GAAI3N,EAAG2N,EAAU,GAAIxU,EAAGwU,EAAU,GAAIzU,EAAG,EACjE,MAAO,IAA2B,IAAvBgJ,EAAYsK,GAAiB,CACtC,MAAMmB,EAAYnB,EACfoB,OAAO,GACPA,OAAO,EAAGpB,EAAMxT,OAAS,GACzB6U,MAAM,KACT1L,EAAO,CACLrC,EAAG6N,EAAU,GACb3N,EAAG2N,EAAU,GACbxU,EAAGwU,EAAU,GACbzU,EAAGyU,EAAU,GAEjB,MAAO,IAA0B,IAAtB5L,EAAWyK,GAAiB,CACrC,MAAMsB,EAASpO,EAAS8M,GACxBrK,EAAO,CAAErC,EAAGgO,EAAOhO,EAAGE,EAAG8N,EAAO9N,EAAG7G,EAAG2U,EAAO3U,EAAGD,EAAG,EACrD,OAEA,GAAIsT,aAAiB9S,aAEL6E,IAAZiO,EAAM1M,QACMvB,IAAZiO,EAAMxM,QACMzB,IAAZiO,EAAMrT,EACN,CACA,MAAM4U,OAAoBxP,IAAZiO,EAAMtT,EAAkBsT,EAAMtT,EAAI,MAChDiJ,EAAO,CAAErC,EAAG0M,EAAM1M,EAAGE,EAAGwM,EAAMxM,EAAG7G,EAAGqT,EAAMrT,EAAGD,EAAG6U,EAClD,CAKJ,QAAaxP,IAAT4D,EACF,MAAM,IAAIkL,MACR,gIACEW,KAAKC,UAAUzB,IAGnBvR,KAAKiT,UAAU/L,EAAMsL,EAEzB,CAMA,IAAAU,QAC6B5P,IAAvBtD,KAAK6R,gBACP7R,KAAK6R,gBACL7R,KAAK6R,mBAAgBvO,GAGvBtD,KAAK2R,SAAU,EACf3R,KAAKgS,MAAM1P,MAAMC,QAAU,QAC3BvC,KAAKmT,oBACP,CAUA,KAAAC,CAAMC,GAAgB,IAEE,IAAlBA,IACFrT,KAAK0R,cAAgBjT,OAAO+E,OAAO,CAAA,EAAIxD,KAAKuR,SAGzB,IAAjBvR,KAAK2R,SACP3R,KAAK4R,eAAe5R,KAAKyR,cAG3BzR,KAAKgS,MAAM1P,MAAMC,QAAU,OAI3B+Q,WAAW,UACkBhQ,IAAvBtD,KAAK6R,gBACP7R,KAAK6R,gBACL7R,KAAK6R,mBAAgBvO,IAEtB,EACL,CAMA,KAAAiQ,GACEvT,KAAK4R,eAAe5R,KAAKuR,OACzBvR,KAAK2R,SAAU,EACf3R,KAAKoT,OACP,CAMA,MAAAI,GACExT,KAAK2R,SAAU,EACf3R,KAAK4R,eAAe5R,KAAKuR,OACzBvR,KAAKyT,cAAczT,KAAKuR,MAC1B,CAMA,SAAAmC,QAC6BpQ,IAAvBtD,KAAK0R,cACP1R,KAAKuS,SAASvS,KAAK0R,eAAe,GAElCiC,MAAM,oCAEV,CAQA,SAAAV,CAAU/L,EAAMsL,GAAa,IAER,IAAfA,IACFxS,KAAKyR,aAAehT,OAAO+E,OAAO,CAAA,EAAI0D,IAGxClH,KAAKuR,MAAQrK,EACb,MAAM0M,EAAMvO,EAAS6B,EAAKrC,EAAGqC,EAAKnC,EAAGmC,EAAKhJ,GAEpC2V,EAAe,EAAItO,KAAKuO,GACxBC,EAAS/T,KAAK6E,EAAI+O,EAAIhO,EACtByL,EACJrR,KAAKoR,kBAAkBC,EAAI0C,EAASxO,KAAKyO,IAAIH,EAAeD,EAAIjO,GAC5D2L,EACJtR,KAAKoR,kBAAkBE,EAAIyC,EAASxO,KAAK0O,IAAIJ,EAAeD,EAAIjO,GAElE3F,KAAKkU,oBAAoB5R,MAAM6R,KAC7B9C,EAAI,GAAMrR,KAAKkU,oBAAoBE,YAAc,KACnDpU,KAAKkU,oBAAoB5R,MAAM+R,IAC7B/C,EAAI,GAAMtR,KAAKkU,oBAAoBI,aAAe,KAEpDtU,KAAKyT,cAAcvM,EACrB,CAOA,WAAAqN,CAAYtV,GACVe,KAAKuR,MAAMtT,EAAIgB,EAAQ,IACvBe,KAAKyT,cAAczT,KAAKuR,MAC1B,CAOA,cAAAiD,CAAevV,GACb,MAAM2U,EAAMvO,EAASrF,KAAKuR,MAAM1M,EAAG7E,KAAKuR,MAAMxM,EAAG/E,KAAKuR,MAAMrT,GAC5D0V,EAAI/N,EAAI5G,EAAQ,IAChB,MAAMiI,EAAOd,EAASwN,EAAIjO,EAAGiO,EAAIhO,EAAGgO,EAAI/N,GACxCqB,EAAQ,EAAIlH,KAAKuR,MAAMtT,EACvB+B,KAAKuR,MAAQrK,EACblH,KAAKyT,eACP,CAOA,aAAAA,CAAcvM,EAAOlH,KAAKuR,OACxB,MAAMqC,EAAMvO,EAAS6B,EAAKrC,EAAGqC,EAAKnC,EAAGmC,EAAKhJ,GACpCuW,EAAMzU,KAAK0U,kBAAkBC,WAAW,WACrBrR,IAArBtD,KAAK4U,cACP5U,KAAKkR,YACF9R,OAAOyV,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,IAENT,EAAIU,aAAanV,KAAKkR,WAAY,EAAG,EAAGlR,KAAKkR,WAAY,EAAG,GAG5D,MAAMkE,EAAIpV,KAAK0U,kBAAkBN,YAC3BzO,EAAI3F,KAAK0U,kBAAkBJ,aACjCG,EAAIY,UAAU,EAAG,EAAGD,EAAGzP,GAEvB8O,EAAIa,aAAatV,KAAKwR,UAAW,EAAG,GACpCiD,EAAIc,UAAY,eAAiB,EAAI3B,EAAI/N,GAAK,IAC9C4O,EAAIe,OAAOxV,KAAKoR,kBAAkBC,EAAGrR,KAAKoR,kBAAkBE,EAAGtR,KAAK6E,GACpE4P,EAAIgB,OAEJzV,KAAK0V,gBAAgBzW,MAAQ,IAAM2U,EAAI/N,EACvC7F,KAAK2V,aAAa1W,MAAQ,IAAMiI,EAAKjJ,EAErC+B,KAAK4V,gBAAgBtT,MAAMuT,gBACzB,QACA7V,KAAKyR,aAAa5M,EAClB,IACA7E,KAAKyR,aAAa1M,EAClB,IACA/E,KAAKyR,aAAavT,EAClB,IACA8B,KAAKyR,aAAaxT,EAClB,IACF+B,KAAK8V,YAAYxT,MAAMuT,gBACrB,QACA7V,KAAKuR,MAAM1M,EACX,IACA7E,KAAKuR,MAAMxM,EACX,IACA/E,KAAKuR,MAAMrT,EACX,IACA8B,KAAKuR,MAAMtT,EACX,GACJ,CAMA,QAAAiU,GACElS,KAAK0U,kBAAkBpS,MAAMyT,MAAQ,OACrC/V,KAAK0U,kBAAkBpS,MAAM0T,OAAS,OAEtChW,KAAK0U,kBAAkBqB,MAAQ,IAAM/V,KAAKkR,WAC1ClR,KAAK0U,kBAAkBsB,OAAS,IAAMhW,KAAKkR,UAC7C,CAOA,OAAAY,GAYE,GAXA9R,KAAKgS,MAAQ3R,SAASC,cAAc,OACpCN,KAAKgS,MAAMiE,UAAY,mBAEvBjW,KAAKkW,eAAiB7V,SAASC,cAAc,OAC7CN,KAAKkU,oBAAsB7T,SAASC,cAAc,OAClDN,KAAKkU,oBAAoB+B,UAAY,eACrCjW,KAAKkW,eAAezV,YAAYT,KAAKkU,qBAErClU,KAAK0U,kBAAoBrU,SAASC,cAAc,UAChDN,KAAKkW,eAAezV,YAAYT,KAAK0U,mBAEhC1U,KAAK0U,kBAAkBC,WAOrB,CACL,MAAMF,EAAMzU,KAAK0U,kBAAkBC,WAAW,MAC9C3U,KAAKkR,YACF9R,OAAOyV,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,GACJlV,KAAK0U,kBACFC,WAAW,MACXQ,aAAanV,KAAKkR,WAAY,EAAG,EAAGlR,KAAKkR,WAAY,EAAG,EAC7D,KApBwC,CACtC,MAAMiF,EAAW9V,SAASC,cAAc,OACxC6V,EAAS7T,MAAMiP,MAAQ,MACvB4E,EAAS7T,MAAM8T,WAAa,OAC5BD,EAAS7T,MAAM+T,QAAU,OACzBF,EAASG,UAAY,mDACrBtW,KAAK0U,kBAAkBjU,YAAY0V,EACrC,CAeAnW,KAAKkW,eAAeD,UAAY,YAEhCjW,KAAKuW,WAAalW,SAASC,cAAc,OACzCN,KAAKuW,WAAWN,UAAY,cAE5BjW,KAAKwW,cAAgBnW,SAASC,cAAc,OAC5CN,KAAKwW,cAAcP,UAAY,iBAE/BjW,KAAKyW,SAAWpW,SAASC,cAAc,OACvCN,KAAKyW,SAASR,UAAY,YAE1BjW,KAAK2V,aAAetV,SAASC,cAAc,SAC3C,IACEN,KAAK2V,aAAae,KAAO,QACzB1W,KAAK2V,aAAanQ,IAAM,IACxBxF,KAAK2V,aAAajQ,IAAM,KAC1B,CAAE,MAAOiR,GAET,CACA3W,KAAK2V,aAAa1W,MAAQ,MAC1Be,KAAK2V,aAAaM,UAAY,YAE9BjW,KAAK0V,gBAAkBrV,SAASC,cAAc,SAC9C,IACEN,KAAK0V,gBAAgBgB,KAAO,QAC5B1W,KAAK0V,gBAAgBlQ,IAAM,IAC3BxF,KAAK0V,gBAAgBhQ,IAAM,KAC7B,CAAE,MAAOiR,GAET,CACA3W,KAAK0V,gBAAgBzW,MAAQ,MAC7Be,KAAK0V,gBAAgBO,UAAY,YAEjCjW,KAAKuW,WAAW9V,YAAYT,KAAK2V,cACjC3V,KAAKwW,cAAc/V,YAAYT,KAAK0V,iBAEpC,MAAMkB,EAAK5W,KACXA,KAAK2V,aAAakB,SAAW,WAC3BD,EAAGrC,YAAYvU,KAAKf,MACtB,EACAe,KAAK2V,aAAamB,QAAU,WAC1BF,EAAGrC,YAAYvU,KAAKf,MACtB,EACAe,KAAK0V,gBAAgBmB,SAAW,WAC9BD,EAAGpC,eAAexU,KAAKf,MACzB,EACAe,KAAK0V,gBAAgBoB,QAAU,WAC7BF,EAAGpC,eAAexU,KAAKf,MACzB,EAEAe,KAAK+W,gBAAkB1W,SAASC,cAAc,OAC9CN,KAAK+W,gBAAgBd,UAAY,2BACjCjW,KAAK+W,gBAAgBT,UAAY,cAEjCtW,KAAKgX,aAAe3W,SAASC,cAAc,OAC3CN,KAAKgX,aAAaf,UAAY,wBAC9BjW,KAAKgX,aAAaV,UAAY,WAE9BtW,KAAK8V,YAAczV,SAASC,cAAc,OAC1CN,KAAK8V,YAAYG,UAAY,gBAC7BjW,KAAK8V,YAAYQ,UAAY,MAE7BtW,KAAK4V,gBAAkBvV,SAASC,cAAc,OAC9CN,KAAK4V,gBAAgBK,UAAY,oBACjCjW,KAAK4V,gBAAgBU,UAAY,UAEjCtW,KAAKiX,aAAe5W,SAASC,cAAc,OAC3CN,KAAKiX,aAAahB,UAAY,wBAC9BjW,KAAKiX,aAAaX,UAAY,SAC9BtW,KAAKiX,aAAaC,QAAUlX,KAAKoT,MAAMrS,KAAKf,MAAM,GAElDA,KAAKmX,YAAc9W,SAASC,cAAc,OAC1CN,KAAKmX,YAAYlB,UAAY,uBAC7BjW,KAAKmX,YAAYb,UAAY,QAC7BtW,KAAKmX,YAAYD,QAAUlX,KAAKwT,OAAOzS,KAAKf,MAE5CA,KAAKoX,WAAa/W,SAASC,cAAc,OACzCN,KAAKoX,WAAWnB,UAAY,sBAC5BjW,KAAKoX,WAAWd,UAAY,OAC5BtW,KAAKoX,WAAWF,QAAUlX,KAAKuT,MAAMxS,KAAKf,MAE1CA,KAAKqX,WAAahX,SAASC,cAAc,OACzCN,KAAKqX,WAAWpB,UAAY,sBAC5BjW,KAAKqX,WAAWf,UAAY,YAC5BtW,KAAKqX,WAAWH,QAAUlX,KAAK0T,UAAU3S,KAAKf,MAE9CA,KAAKgS,MAAMvR,YAAYT,KAAKkW,gBAC5BlW,KAAKgS,MAAMvR,YAAYT,KAAKyW,UAC5BzW,KAAKgS,MAAMvR,YAAYT,KAAK+W,iBAC5B/W,KAAKgS,MAAMvR,YAAYT,KAAKwW,eAC5BxW,KAAKgS,MAAMvR,YAAYT,KAAKgX,cAC5BhX,KAAKgS,MAAMvR,YAAYT,KAAKuW,YAC5BvW,KAAKgS,MAAMvR,YAAYT,KAAK8V,aAC5B9V,KAAKgS,MAAMvR,YAAYT,KAAK4V,iBAE5B5V,KAAKgS,MAAMvR,YAAYT,KAAKiX,cAC5BjX,KAAKgS,MAAMvR,YAAYT,KAAKmX,aAC5BnX,KAAKgS,MAAMvR,YAAYT,KAAKoX,YAC5BpX,KAAKgS,MAAMvR,YAAYT,KAAKqX,WAC9B,CAMA,WAAApF,GACEjS,KAAKsX,KAAO,CAAA,EACZtX,KAAKuX,MAAQ,CAAA,EACbvX,KAAKa,OAAS,IAAI1B,EAAOa,KAAK0U,mBAC9B1U,KAAKa,OAAOlB,IAAI,SAASC,IAAI,CAAE4X,QAAQ,IAEvCxX,KAAKa,OAAOtB,GAAG,eAAiB0B,IAC1BA,EAAMwW,SACRzX,KAAK0X,cAAczW,KAGvBjB,KAAKa,OAAOtB,GAAG,MAAQ0B,IACrBjB,KAAK0X,cAAczW,KAErBjB,KAAKa,OAAOtB,GAAG,WAAa0B,IAC1BjB,KAAK0X,cAAczW,KAErBjB,KAAKa,OAAOtB,GAAG,UAAY0B,IACzBjB,KAAK0X,cAAczW,KAErBjB,KAAKa,OAAOtB,GAAG,SAAW0B,IACxBjB,KAAK0X,cAAczW,IAEvB,CAMA,kBAAAkS,GACE,IAAuB,IAAnBnT,KAAKmR,UAAqB,CAC5B,MAAMsD,EAAMzU,KAAK0U,kBAAkBC,WAAW,WACrBrR,IAArBtD,KAAK4U,cACP5U,KAAKkR,YACF9R,OAAOyV,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,IAENT,EAAIU,aAAanV,KAAKkR,WAAY,EAAG,EAAGlR,KAAKkR,WAAY,EAAG,GAG5D,MAAMkE,EAAIpV,KAAK0U,kBAAkBN,YAC3BzO,EAAI3F,KAAK0U,kBAAkBJ,aAIjC,IAAIjD,EAAGC,EAAGqG,EAAKC,EAHfnD,EAAIY,UAAU,EAAG,EAAGD,EAAGzP,GAIvB3F,KAAKoR,kBAAoB,CAAEC,EAAO,GAAJ+D,EAAS9D,EAAO,GAAJ3L,GAC1C3F,KAAK6E,EAAI,IAAOuQ,EAChB,MAAMvB,EAAgB,EAAItO,KAAKuO,GAAM,IAC/B+D,EAAO,EAAI,IACXC,EAAO,EAAI9X,KAAK6E,EACtB,IAAI8B,EACJ,IAAKgR,EAAM,EAAGA,EAAM,IAAKA,IACvB,IAAKC,EAAM,EAAGA,EAAM5X,KAAK6E,EAAG+S,IAC1BvG,EAAIrR,KAAKoR,kBAAkBC,EAAIuG,EAAMrS,KAAKyO,IAAIH,EAAe8D,GAC7DrG,EAAItR,KAAKoR,kBAAkBE,EAAIsG,EAAMrS,KAAK0O,IAAIJ,EAAe8D,GAC7DhR,EAAMP,EAASuR,EAAME,EAAMD,EAAME,EAAM,GACvCrD,EAAIc,UAAY,OAAS5O,EAAI9B,EAAI,IAAM8B,EAAI5B,EAAI,IAAM4B,EAAIzI,EAAI,IAC7DuW,EAAIsD,SAAS1G,EAAI,GAAKC,EAAI,GAAK,EAAG,GAGtCmD,EAAIuD,YAAc,gBAClBvD,EAAIe,OAAOxV,KAAKoR,kBAAkBC,EAAGrR,KAAKoR,kBAAkBE,EAAGtR,KAAK6E,GACpE4P,EAAIwD,SAEJjY,KAAKwR,UAAYiD,EAAIyD,aAAa,EAAG,EAAG9C,EAAGzP,EAC7C,CACA3F,KAAKmR,WAAY,CACnB,CAOA,aAAAuG,CAAczW,GACZ,MAAMkX,EAAOnY,KAAKkW,eAAekC,wBAC3BjE,EAAOlT,EAAMoX,OAAOhH,EAAI8G,EAAKhE,KAC7BE,EAAMpT,EAAMoX,OAAO/G,EAAI6G,EAAK9D,IAE5BiE,EAAU,GAAMtY,KAAKkW,eAAe5B,aACpCiE,EAAU,GAAMvY,KAAKkW,eAAe9B,YAEpC/C,EAAI8C,EAAOoE,EACXjH,EAAI+C,EAAMiE,EAEVE,EAAQjT,KAAKkT,MAAMpH,EAAGC,GACtByC,EAAS,IAAOxO,KAAKC,IAAID,KAAKmT,KAAKrH,EAAIA,EAAIC,EAAIA,GAAIiH,GAEnDI,EAASpT,KAAK0O,IAAIuE,GAASzE,EAASuE,EACpCM,EAAUrT,KAAKyO,IAAIwE,GAASzE,EAASwE,EAE3CvY,KAAKkU,oBAAoB5R,MAAM+R,IAC7BsE,EAAS,GAAM3Y,KAAKkU,oBAAoBI,aAAe,KACzDtU,KAAKkU,oBAAoB5R,MAAM6R,KAC7ByE,EAAU,GAAM5Y,KAAKkU,oBAAoBE,YAAc,KAGzD,IAAIzO,EAAI6S,GAAS,EAAIjT,KAAKuO,IAC1BnO,EAAIA,EAAI,EAAIA,EAAI,EAAIA,EACpB,MAAMC,EAAImO,EAAS/T,KAAK6E,EAClB+O,EAAMvO,EAASrF,KAAKuR,MAAM1M,EAAG7E,KAAKuR,MAAMxM,EAAG/E,KAAKuR,MAAMrT,GAC5D0V,EAAIjO,EAAIA,EACRiO,EAAIhO,EAAIA,EACR,MAAMsB,EAAOd,EAASwN,EAAIjO,EAAGiO,EAAIhO,EAAGgO,EAAI/N,GACxCqB,EAAQ,EAAIlH,KAAKuR,MAAMtT,EACvB+B,KAAKuR,MAAQrK,EAGblH,KAAK4V,gBAAgBtT,MAAMuT,gBACzB,QACA7V,KAAKyR,aAAa5M,EAClB,IACA7E,KAAKyR,aAAa1M,EAClB,IACA/E,KAAKyR,aAAavT,EAClB,IACA8B,KAAKyR,aAAaxT,EAClB,IACF+B,KAAK8V,YAAYxT,MAAMuT,gBACrB,QACA7V,KAAKuR,MAAM1M,EACX,IACA7E,KAAKuR,MAAMxM,EACX,IACA/E,KAAKuR,MAAMrT,EACX,IACA8B,KAAKuR,MAAMtT,EACX,GACJ,GCvwBF,SAAS4a,KAAaC,GACpB,GAAIA,EAAK/a,OAAS,EAChB,MAAM,IAAI8I,UAAU,sBACf,GAAoB,IAAhBiS,EAAK/a,OACd,OAAOsC,SAAS0Y,eAAeD,EAAK,IAC/B,CACL,MAAMxX,EAAUjB,SAASC,cAAcwY,EAAK,IAE5C,OADAxX,EAAQb,YAAYoY,KAAaC,EAAK9a,MAAM,KACrCsD,CACT,CACF,CAWO,IC3BH0X,EADAC,GAAa,EAGV,MAAMC,EAAwB,sCCG9B,MAAMpZ,EAAiBqZ,EACjBC,EAAmBC,EACnBC,EFoBN,MAQL,WAAArI,CACEsI,EACAC,EACAC,EACAvI,EAAa,EACbwI,EAAa,KAAM,GAEnB1Z,KAAKuB,OAASgY,EACdvZ,KAAK2Z,eAAiB,GACtB3Z,KAAKD,UAAYyZ,EACjBxZ,KAAK4Z,eAAgB,EACrB5Z,KAAK0Z,WAAaA,EAElB1Z,KAAK6Z,QAAU,CAAA,EACf7Z,KAAK8Z,aAAc,EACnB9Z,KAAK+Z,aAAe,EACpB/Z,KAAKga,eAAiB,CACpBC,SAAS,EACTC,QAAQ,EACRna,eAAWuD,EACX6W,YAAY,GAEd1b,OAAO+E,OAAOxD,KAAK6Z,QAAS7Z,KAAKga,gBAEjCha,KAAKyZ,iBAAmBA,EACxBzZ,KAAKoa,cAAgB,CAAA,EACrBpa,KAAKqa,YAAc,GACnBra,KAAKsa,SAAW,CAAA,EAChBta,KAAKua,WAAa,EAClBva,KAAKwa,aAAe,CAAA,EACpBxa,KAAKya,YAAc,IAAIrB,EAAYlI,GACnClR,KAAK0a,aAAUpX,CACjB,CAOA,UAAAqX,CAAWd,GACT,QAAgBvW,IAAZuW,EAAuB,CAEzB7Z,KAAKwa,aAAe,CAAA,EACpBxa,KAAK4a,eAEL,IAAIX,GAAU,EACd,GAAuB,iBAAZJ,EACT7Z,KAAK6Z,QAAQK,OAASL,OACjB,GAAIhb,MAAMC,QAAQ+a,GACvB7Z,KAAK6Z,QAAQK,OAASL,EAAQgB,YACzB,GAAuB,iBAAZhB,EAAsB,CACtC,GAAe,MAAXA,EACF,MAAM,IAAIhT,UAAU,+BAEIvD,IAAtBuW,EAAQ9Z,YACVC,KAAK6Z,QAAQ9Z,UAAY8Z,EAAQ9Z,gBAEZuD,IAAnBuW,EAAQK,SACVla,KAAK6Z,QAAQK,OAASL,EAAQK,aAEL5W,IAAvBuW,EAAQM,aACVna,KAAK6Z,QAAQM,WAAaN,EAAQM,iBAEZ7W,IAApBuW,EAAQI,UACVA,EAAUJ,EAAQI,QAEtB,KAA8B,kBAAZJ,GAChB7Z,KAAK6Z,QAAQK,QAAS,EACtBD,EAAUJ,GACkB,mBAAZA,IAChB7Z,KAAK6Z,QAAQK,OAASL,EACtBI,GAAU,IAEgB,IAAxBja,KAAK6Z,QAAQK,SACfD,GAAU,GAGZja,KAAK6Z,QAAQI,QAAUA,CACzB,CACAja,KAAK8a,QACP,CAMA,gBAAAC,CAAiBX,GACfpa,KAAKoa,cAAgBA,GACQ,IAAzBpa,KAAK6Z,QAAQI,UACfja,KAAK8a,cAC0BxX,IAA3BtD,KAAK6Z,QAAQ9Z,YACfC,KAAKD,UAAYC,KAAK6Z,QAAQ9Z,WAEhCC,KAAK8R,UAET,CAMA,OAAAA,GACE9R,KAAK8a,SACL9a,KAAK2Z,eAAiB,GAEtB,MAAMO,EAASla,KAAK6Z,QAAQK,OAC5B,IAAIc,EAAU,EACV9H,GAAO,EACX,IAAK,MAAMhP,KAAUlE,KAAKyZ,iBACpBhb,OAAOC,UAAUiF,eAAe/E,KAAKoB,KAAKyZ,iBAAkBvV,KAC9DlE,KAAK4Z,eAAgB,EACrB1G,GAAO,EACe,mBAAXgH,GACThH,EAAOgH,EAAOhW,EAAQ,IACtBgP,EACEA,GACAlT,KAAKib,cAAcjb,KAAKyZ,iBAAiBvV,GAAS,CAACA,IAAS,KAC1C,IAAXgW,QAAmBA,EAAOgB,QAAQhX,KAC3CgP,GAAO,IAGI,IAATA,IACFlT,KAAK4Z,eAAgB,EAGjBoB,EAAU,GACZhb,KAAKmb,UAAU,IAGjBnb,KAAKob,YAAYlX,GAGjBlE,KAAKib,cAAcjb,KAAKyZ,iBAAiBvV,GAAS,CAACA,KAErD8W,KAGJhb,KAAKqb,cACLrb,KAAKsb,OAEP,CAMA,KAAAA,GACEtb,KAAK0a,QAAUra,SAASC,cAAc,OACtCN,KAAK0a,QAAQzE,UAAY,4BACzBjW,KAAKD,UAAUU,YAAYT,KAAK0a,SAChC,IAAK,IAAIxU,EAAI,EAAGA,EAAIlG,KAAKqa,YAAYtc,OAAQmI,IAC3ClG,KAAK0a,QAAQja,YAAYT,KAAKqa,YAAYnU,IAG5ClG,KAAKub,oBACP,CAMA,MAAAT,GACE,IAAK,IAAI5U,EAAI,EAAGA,EAAIlG,KAAKqa,YAAYtc,OAAQmI,IAC3ClG,KAAK0a,QAAQ9Z,YAAYZ,KAAKqa,YAAYnU,SAGvB5C,IAAjBtD,KAAK0a,UACP1a,KAAKD,UAAUa,YAAYZ,KAAK0a,SAChC1a,KAAK0a,aAAUpX,GAEjBtD,KAAKqa,YAAc,GAEnBra,KAAK4a,cACP,CAQA,SAAAY,CAAUC,GACR,IAAIC,EAAO1b,KAAKoa,cAChB,IAAK,IAAIlU,EAAI,EAAGA,EAAIuV,EAAK1d,OAAQmI,IAAK,CACpC,QAAsB5C,IAAlBoY,EAAKD,EAAKvV,IAEP,CACLwV,OAAOpY,EACP,KACF,CAJEoY,EAAOA,EAAKD,EAAKvV,GAKrB,CACA,OAAOwV,CACT,CASA,SAAAP,CAAUM,KAASpB,GACjB,IAA2B,IAAvBra,KAAK4Z,cAAwB,CAC/B,MAAM+B,EAAOtb,SAASC,cAAc,OAOpC,OANAqb,EAAK1F,UACH,iDAAmDwF,EAAK1d,OAC1Dsc,EAAYrZ,QAASM,IACnBqa,EAAKlb,YAAYa,KAEnBtB,KAAKqa,YAAY3Z,KAAKib,GACf3b,KAAKqa,YAAYtc,MAC1B,CACA,OAAO,CACT,CAOA,WAAAqd,CAAYQ,GACV,MAAMC,EAAMxb,SAASC,cAAc,OACnCub,EAAI5F,UAAY,sCAChB4F,EAAIvF,UAAYsF,EAChB5b,KAAKmb,UAAU,GAAIU,EACrB,CAUA,UAAAC,CAAWF,EAAMH,EAAMM,GAAc,GACnC,MAAMF,EAAMxb,SAASC,cAAc,OAGnC,GAFAub,EAAI5F,UACF,kDAAoDwF,EAAK1d,QACvC,IAAhBge,EAAsB,CACxB,KAAOF,EAAIG,YACTH,EAAIjb,YAAYib,EAAIG,YAEtBH,EAAIpb,YAAYoY,EAAU,IAAK,IAAK+C,GACtC,MACEC,EAAIvF,UAAYsF,EAAO,IAEzB,OAAOC,CACT,CASA,aAAAI,CAAcnY,EAAK7E,EAAOwc,GACxB,MAAMS,EAAS7b,SAASC,cAAc,UACtC4b,EAAOjG,UAAY,sCACnB,IAAIkG,EAAgB,OACN7Y,IAAVrE,IACyB,IAAvB6E,EAAIoX,QAAQjc,KACdkd,EAAgBrY,EAAIoX,QAAQjc,IAIhC,IAAK,IAAIiH,EAAI,EAAGA,EAAIpC,EAAI/F,OAAQmI,IAAK,CACnC,MAAMhC,EAAS7D,SAASC,cAAc,UACtC4D,EAAOjF,MAAQ6E,EAAIoC,GACfA,IAAMiW,IACRjY,EAAOkY,SAAW,YAEpBlY,EAAOoS,UAAYxS,EAAIoC,GACvBgW,EAAOzb,YAAYyD,EACrB,CAEA,MAAM0S,EAAK5W,KACXkc,EAAOrF,SAAW,WAChBD,EAAGyF,QAAQrc,KAAKf,MAAOwc,EACzB,EAEA,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOJ,EAC9B,CASA,UAAAK,CAAWzY,EAAK7E,EAAOwc,GACrB,MAAMrX,EAAeN,EAAI,GACnB0B,EAAM1B,EAAI,GACV4B,EAAM5B,EAAI,GACV0Y,EAAO1Y,EAAI,GACX2Y,EAAQpc,SAASC,cAAc,SACrCmc,EAAMxG,UAAY,qCAClB,IACEwG,EAAM/F,KAAO,QACb+F,EAAMjX,IAAMA,EACZiX,EAAM/W,IAAMA,CACd,CAAE,MAAOiR,GAET,CACA8F,EAAMD,KAAOA,EAGb,IAAIE,EAAc,GACdC,EAAa,EAEjB,QAAcrZ,IAAVrE,EAAqB,CACvB,MAAM2d,EAAS,IACX3d,EAAQ,GAAKA,EAAQ2d,EAASpX,GAChCiX,EAAMjX,IAAMD,KAAKsX,KAAK5d,EAAQ2d,GAC9BD,EAAaF,EAAMjX,IACnBkX,EAAc,mBACLzd,EAAQ2d,EAASpX,IAC1BiX,EAAMjX,IAAMD,KAAKsX,KAAK5d,EAAQ2d,GAC9BD,EAAaF,EAAMjX,IACnBkX,EAAc,mBAEZzd,EAAQ2d,EAASlX,GAAe,IAARA,IAC1B+W,EAAM/W,IAAMH,KAAKsX,KAAK5d,EAAQ2d,GAC9BD,EAAaF,EAAM/W,IACnBgX,EAAc,mBAEhBD,EAAMxd,MAAQA,CAChB,MACEwd,EAAMxd,MAAQmF,EAGhB,MAAM0Y,EAAQzc,SAASC,cAAc,SACrCwc,EAAM7G,UAAY,0CAClB6G,EAAM7d,MAAQwd,EAAMxd,MAEpB,MAAM2X,EAAK5W,KACXyc,EAAM5F,SAAW,WACfiG,EAAM7d,MAAQe,KAAKf,MACnB2X,EAAGyF,QAAQtZ,OAAO/C,KAAKf,OAAQwc,EACjC,EACAgB,EAAM3F,QAAU,WACdgG,EAAM7d,MAAQe,KAAKf,KACrB,EAEA,MAAMqd,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GAC/CsB,EAAY/c,KAAKmb,UAAUM,EAAMa,EAAOG,EAAOK,GAGjC,KAAhBJ,GAAsB1c,KAAKwa,aAAauC,KAAeJ,IACzD3c,KAAKwa,aAAauC,GAAaJ,EAC/B3c,KAAKgd,YAAYN,EAAaK,GAElC,CAMA,WAAA1B,GACE,IAAgC,IAA5Brb,KAAK6Z,QAAQM,WAAqB,CACpC,MAAM8C,EAAiB5c,SAASC,cAAc,OAC9C2c,EAAehH,UAAY,sCAC3BgH,EAAe3G,UAAY,mBAC3B2G,EAAe/F,QAAU,KACvBlX,KAAKkd,iBAEPD,EAAeE,YAAc,KAC3BF,EAAehH,UAAY,6CAE7BgH,EAAeG,WAAa,KAC1BH,EAAehH,UAAY,uCAG7BjW,KAAKqd,iBAAmBhd,SAASC,cAAc,OAC/CN,KAAKqd,iBAAiBpH,UACpB,gDAEFjW,KAAKqa,YAAY3Z,KAAKV,KAAKqd,kBAC3Brd,KAAKqa,YAAY3Z,KAAKuc,EACxB,CACF,CAQA,WAAAD,CAAYM,EAAQC,GAClB,IACuB,IAArBvd,KAAK8Z,cACkB,IAAvB9Z,KAAK4Z,eACL5Z,KAAK+Z,aAAe/Z,KAAKua,WACzB,CACA,MAAMsB,EAAMxb,SAASC,cAAc,OACnCub,EAAI2B,GAAK,0BACT3B,EAAI5F,UAAY,0BAChB4F,EAAIvF,UAAYgH,EAChBzB,EAAI3E,QAAU,KACZlX,KAAK4a,gBAEP5a,KAAK+Z,cAAgB,EACrB/Z,KAAKsa,SAAW,CAAEmD,KAAM5B,EAAK0B,MAAOA,EACtC,CACF,CAMA,YAAA3C,QAC6BtX,IAAvBtD,KAAKsa,SAASmD,OAChBzd,KAAKsa,SAASmD,KAAK9c,WAAWC,YAAYZ,KAAKsa,SAASmD,MACxDC,aAAa1d,KAAKsa,SAASqD,aAC3BD,aAAa1d,KAAKsa,SAASsD,eAC3B5d,KAAKsa,SAAW,CAAA,EAEpB,CAMA,kBAAAiB,GACE,QAA2BjY,IAAvBtD,KAAKsa,SAASmD,KAAoB,CACpC,MACMtF,EADuBnY,KAAKqa,YAAYra,KAAKsa,SAASiD,OAC1BnF,wBAClCpY,KAAKsa,SAASmD,KAAKnb,MAAM6R,KAAOgE,EAAKhE,KAAO,KAC5CnU,KAAKsa,SAASmD,KAAKnb,MAAM+R,IAAM8D,EAAK9D,IAAM,GAAK,KAC/ChU,SAASe,KAAKX,YAAYT,KAAKsa,SAASmD,MACxCzd,KAAKsa,SAASqD,YAAcrK,WAAW,KACrCtT,KAAKsa,SAASmD,KAAKnb,MAAMub,QAAU,GAClC,MACH7d,KAAKsa,SAASsD,cAAgBtK,WAAW,KACvCtT,KAAK4a,gBACJ,KACL,CACF,CASA,aAAAkD,CAAc1Z,EAAcnF,EAAOwc,GACjC,MAAMsC,EAAW1d,SAASC,cAAc,SACxCyd,EAASrH,KAAO,WAChBqH,EAAS9H,UAAY,wCACrB8H,EAASC,QAAU5Z,OACLd,IAAVrE,IACF8e,EAASC,QAAU/e,EACfA,IAAUmF,IACgB,iBAAjBA,EACLnF,IAAUmF,EAAa6V,SACzBja,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,IAGhDe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,MAKpD,MAAM2X,EAAK5W,KACX+d,EAASlH,SAAW,WAClBD,EAAGyF,QAAQrc,KAAKge,QAASvC,EAC3B,EAEA,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOyB,EAC9B,CASA,cAAAE,CAAe7Z,EAAcnF,EAAOwc,GAClC,MAAMsC,EAAW1d,SAASC,cAAc,SACxCyd,EAASrH,KAAO,OAChBqH,EAAS9H,UAAY,oCACrB8H,EAAS9e,MAAQA,EACbA,IAAUmF,GACZpE,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,IAGhD,MAAM2X,EAAK5W,KACX+d,EAASlH,SAAW,WAClBD,EAAGyF,QAAQrc,KAAKf,MAAOwc,EACzB,EAEA,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOyB,EAC9B,CASA,eAAAG,CAAgBpa,EAAK7E,EAAOwc,GAC1B,MAAM0C,EAAera,EAAI,GACnB+X,EAAMxb,SAASC,cAAc,OAGrB,UAFdrB,OAAkBqE,IAAVrE,EAAsBkf,EAAelf,IAG3C4c,EAAI5F,UAAY,0CAChB4F,EAAIvZ,MAAMuT,gBAAkB5W,GAE5B4c,EAAI5F,UAAY,+CAGlBhX,OAAkBqE,IAAVrE,EAAsBkf,EAAelf,EAC7C4c,EAAI3E,QAAU,KACZlX,KAAKoe,iBAAiBnf,EAAO4c,EAAKJ,IAGpC,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOT,EAC9B,CASA,gBAAAuC,CAAiBnf,EAAO4c,EAAKJ,GAE3BI,EAAI3E,QAAU,WAAa,EAE3BlX,KAAKya,YAAY1I,SAAS8J,GAC1B7b,KAAKya,YAAYvH,OAEjBlT,KAAKya,YAAYlI,SAAStT,GAC1Be,KAAKya,YAAYtI,kBAAmBZ,IAClC,MAAM8M,EACJ,QAAU9M,EAAM1M,EAAI,IAAM0M,EAAMxM,EAAI,IAAMwM,EAAMrT,EAAI,IAAMqT,EAAMtT,EAAI,IACtE4d,EAAIvZ,MAAMuT,gBAAkBwI,EAC5Bre,KAAKqc,QAAQgC,EAAa5C,KAI5Bzb,KAAKya,YAAYpI,iBAAiB,KAChCwJ,EAAI3E,QAAU,KACZlX,KAAKoe,iBAAiBnf,EAAO4c,EAAKJ,KAGxC,CAUA,aAAAR,CAAcqD,EAAK7C,EAAO,GAAI8C,GAAY,GACxC,IAAIrL,GAAO,EACX,MAAMgH,EAASla,KAAK6Z,QAAQK,OAC5B,IAAIsE,GAAe,EACnB,IAAK,MAAMC,KAAUH,EACnB,GAAI7f,OAAOC,UAAUiF,eAAe/E,KAAK0f,EAAKG,GAAS,CACrDvL,GAAO,EACP,MAAMyI,EAAO2C,EAAIG,GACXC,EAAU7a,EAAmB4X,EAAMgD,GAmBzC,GAlBsB,mBAAXvE,IACThH,EAAOgH,EAAOuE,EAAQhD,IAGT,IAATvI,IAECrU,MAAMC,QAAQ6c,IACC,iBAATA,GACS,kBAATA,GACPA,aAAgBld,SAEhBuB,KAAK4Z,eAAgB,EACrB1G,EAAOlT,KAAKib,cAAcU,EAAM+C,GAAS,GACzC1e,KAAK4Z,eAA8B,IAAd2E,KAKd,IAATrL,EAAgB,CAClBsL,GAAe,EACf,MAAMvf,EAAQe,KAAKwb,UAAUkD,GAE7B,GAAI7f,MAAMC,QAAQ6c,GAChB3b,KAAK2e,aAAahD,EAAM1c,EAAOyf,QAC1B,GAAoB,iBAAT/C,EAChB3b,KAAKie,eAAetC,EAAM1c,EAAOyf,QAC5B,GAAoB,kBAAT/C,EAChB3b,KAAK8d,cAAcnC,EAAM1c,EAAOyf,QAC3B,GAAI/C,aAAgBld,QAEzB,IAAKuB,KAAK0Z,WAAW+B,EAAMgD,EAAQze,KAAKoa,eAEtC,QAAqB9W,IAAjBqY,EAAK1B,QAAuB,CAC9B,MAAM2E,EAAc/a,EAAmB6a,EAAS,WAC1CG,EAAe7e,KAAKwb,UAAUoD,GACpC,IAAqB,IAAjBC,EAAuB,CACzB,MAAMvC,EAAQtc,KAAK8b,WAAW2C,EAAQC,GAAS,GAC/C1e,KAAKmb,UAAUuD,EAASpC,GACxBkC,EACExe,KAAKib,cAAcU,EAAM+C,IAAYF,CACzC,MACExe,KAAK8d,cAAcnC,EAAMkD,EAAcH,EAE3C,KAAO,CACL,MAAMpC,EAAQtc,KAAK8b,WAAW2C,EAAQC,GAAS,GAC/C1e,KAAKmb,UAAUuD,EAASpC,GACxBkC,EACExe,KAAKib,cAAcU,EAAM+C,IAAYF,CACzC,OAGFM,QAAQC,MAAM,0BAA2BpD,EAAM8C,EAAQC,EAE3D,CACF,CAEF,OAAOF,CACT,CASA,YAAAG,CAAa7a,EAAK7E,EAAOwc,GACD,iBAAX3X,EAAI,IAA8B,UAAXA,EAAI,IACpC9D,KAAKke,gBAAgBpa,EAAK7E,EAAOwc,GAC7B3X,EAAI,KAAO7E,GACbe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,KAErB,iBAAX6E,EAAI,IACpB9D,KAAKic,cAAcnY,EAAK7E,EAAOwc,GAC3B3X,EAAI,KAAO7E,GACbe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,KAErB,iBAAX6E,EAAI,KACpB9D,KAAKuc,WAAWzY,EAAK7E,EAAOwc,GACxB3X,EAAI,KAAO7E,GACbe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAO8D,OAAO9D,KAG3D,CAQA,OAAAod,CAAQpd,EAAOwc,GACb,MAAM5B,EAAU7Z,KAAKgf,kBAAkB/f,EAAOwc,GAG5Czb,KAAKuB,OAAOH,MACZpB,KAAKuB,OAAOH,KAAK6d,SACjBjf,KAAKuB,OAAOH,KAAK6d,QAAQvf,MAEzBM,KAAKuB,OAAOH,KAAK6d,QAAQvf,KAAK,eAAgBma,GAEhD7Z,KAAK8Z,aAAc,EACnB9Z,KAAKuB,OAAOoZ,WAAWd,EACzB,CAUA,iBAAAmF,CAAkB/f,EAAOwc,EAAMyD,EAAa,CAAA,GAC1C,IAAIC,EAAUD,EAIdjgB,EAAkB,WADlBA,EAAkB,SAAVA,GAA0BA,IACEA,EAEpC,IAAK,IAAIiH,EAAI,EAAGA,EAAIuV,EAAK1d,OAAQmI,IACf,WAAZuV,EAAKvV,UACkB5C,IAArB6b,EAAQ1D,EAAKvV,MACfiZ,EAAQ1D,EAAKvV,IAAM,CAAA,GAEjBA,IAAMuV,EAAK1d,OAAS,EACtBohB,EAAUA,EAAQ1D,EAAKvV,IAEvBiZ,EAAQ1D,EAAKvV,IAAMjH,GAIzB,OAAOigB,CACT,CAKA,aAAAhC,GACE,MAAMrD,EAAU7Z,KAAKof,aAErB,KAAOpf,KAAKqd,iBAAiBrB,YAC3Bhc,KAAKqd,iBAAiBzc,YAAYZ,KAAKqd,iBAAiBrB,YAE1Dhc,KAAKqd,iBAAiB5c,YACpBoY,EAAU,MAAO,mBAAqB9F,KAAKC,UAAU6G,EAAS,KAAM,IAExE,CAMA,UAAAuF,GACE,MAAMvF,EAAU,CAAA,EAChB,IAAK,IAAI3T,EAAI,EAAGA,EAAIlG,KAAK2Z,eAAe5b,OAAQmI,IAC9ClG,KAAKgf,kBACHhf,KAAK2Z,eAAezT,GAAGjH,MACvBe,KAAK2Z,eAAezT,GAAGuV,KACvB5B,GAGJ,OAAOA,CACT,GE9vBW1a,EAAuBkgB,EACvBC,ECTN,MAKL,WAAArO,CAAYlR,EAAWwf,GACrBvf,KAAKD,UAAYA,EACjBC,KAAKuf,eAAiBA,GAAkB,MAExCvf,KAAKqR,EAAI,EACTrR,KAAKsR,EAAI,EACTtR,KAAKqW,QAAU,EACfrW,KAAKwf,QAAS,EAGdxf,KAAKgS,MAAQ3R,SAASC,cAAc,OACpCN,KAAKgS,MAAMiE,UAAY,cACvBjW,KAAKD,UAAUU,YAAYT,KAAKgS,MAClC,CAMA,WAAAyN,CAAYpO,EAAGC,GACbtR,KAAKqR,EAAIvM,SAASuM,GAClBrR,KAAKsR,EAAIxM,SAASwM,EACpB,CAMA,OAAAoO,CAAQC,GACN,GAAIA,aAAmBtY,QAAS,CAC9B,KAAOrH,KAAKgS,MAAMgK,YAChBhc,KAAKgS,MAAMpR,YAAYZ,KAAKgS,MAAMgK,YAEpChc,KAAKgS,MAAMvR,YAAYkf,EACzB,MAGE3f,KAAKgS,MAAMsE,UAAYqJ,CAE3B,CAMA,IAAAzM,CAAK0M,GAKH,QAJetc,IAAXsc,IACFA,GAAS,IAGI,IAAXA,EAAiB,CACnB,MAAM5J,EAAShW,KAAKgS,MAAMsC,aACpByB,EAAQ/V,KAAKgS,MAAMoC,YACnByL,EAAY7f,KAAKgS,MAAMrR,WAAW2T,aAClCwL,EAAW9f,KAAKgS,MAAMrR,WAAWyT,YAEvC,IAAID,EAAO,EACTE,EAAM,EAER,GAA2B,QAAvBrU,KAAKuf,eAA0B,CACjC,IAAIQ,GAAS,EACXC,GAAQ,EAENhgB,KAAKsR,EAAI0E,EAAShW,KAAKqW,UACzB2J,GAAQ,GAGNhgB,KAAKqR,EAAI0E,EAAQ+J,EAAW9f,KAAKqW,UACnC0J,GAAS,GAIT5L,EADE4L,EACK/f,KAAKqR,EAAI0E,EAET/V,KAAKqR,EAIZgD,EADE2L,EACIhgB,KAAKsR,EAAI0E,EAEThW,KAAKsR,CAEf,MACE+C,EAAMrU,KAAKsR,EAAI0E,EACX3B,EAAM2B,EAAShW,KAAKqW,QAAUwJ,IAChCxL,EAAMwL,EAAY7J,EAAShW,KAAKqW,SAE9BhC,EAAMrU,KAAKqW,UACbhC,EAAMrU,KAAKqW,SAGblC,EAAOnU,KAAKqR,EACR8C,EAAO4B,EAAQ/V,KAAKqW,QAAUyJ,IAChC3L,EAAO2L,EAAW/J,EAAQ/V,KAAKqW,SAE7BlC,EAAOnU,KAAKqW,UACdlC,EAAOnU,KAAKqW,SAIhBrW,KAAKgS,MAAM1P,MAAM6R,KAAOA,EAAO,KAC/BnU,KAAKgS,MAAM1P,MAAM+R,IAAMA,EAAM,KAC7BrU,KAAKgS,MAAM1P,MAAM2d,WAAa,UAC9BjgB,KAAKwf,QAAS,CAChB,MACExf,KAAKkgB,MAET,CAKA,IAAAA,GACElgB,KAAKwf,QAAS,EACdxf,KAAKgS,MAAM1P,MAAM6R,KAAO,IACxBnU,KAAKgS,MAAM1P,MAAM+R,IAAM,IACvBrU,KAAKgS,MAAM1P,MAAM2d,WAAa,QAChC,CAKA,OAAAxgB,GACEO,KAAKgS,MAAMrR,WAAWC,YAAYZ,KAAKgS,MACzC,GDvHWkH,EAAgCiH,EAChCC,EDJN,MAAMA,EASX,eAAOC,CAASxG,EAASyG,EAAkBC,GACzCtH,GAAa,EACbD,EAAasH,EACb,IAAIE,EAAcF,EAKlB,YAJkBhd,IAAdid,IACFC,EAAcF,EAAiBC,IAEjCH,EAAUK,MAAM5G,EAAS2G,EAAa,IAC/BvH,CACT,CASA,YAAOwH,CAAM5G,EAASyG,EAAkB7E,GACtC,IAAK,MAAMvX,KAAU2V,EACfpb,OAAOC,UAAUiF,eAAe/E,KAAKib,EAAS3V,IAChDkc,EAAUM,MAAMxc,EAAQ2V,EAASyG,EAAkB7E,EAGzD,CAUA,YAAOiF,CAAMxc,EAAQ2V,EAASyG,EAAkB7E,GAC9C,QAC+BnY,IAA7Bgd,EAAiBpc,SACYZ,IAA7Bgd,EAAiBK,QAGjB,YADAP,EAAUQ,cAAc1c,EAAQoc,EAAkB7E,GAIpD,IAAIoF,EAAkB3c,EAClB4c,GAAY,OAGexd,IAA7Bgd,EAAiBpc,SACYZ,IAA7Bgd,EAAiBK,UAOjBE,EAAkB,UAIlBC,EAAmD,WAAvCV,EAAUW,QAAQlH,EAAQ3V,KAOxC,IAAI8c,EAAeV,EAAiBO,GAChCC,QAAuCxd,IAA1B0d,EAAaC,WAC5BD,EAAeA,EAAaC,UAG9Bb,EAAUc,YACRhd,EACA2V,EACAyG,EACAO,EACAG,EACAvF,EAEJ,CAYA,kBAAOyF,CACLhd,EACA2V,EACAyG,EACAO,EACAG,EACAvF,GAEA,MAAM0F,EAAM,SAAUC,GACpBtC,QAAQC,MACN,KAAOqC,EAAUhB,EAAUiB,cAAc5F,EAAMvX,GAC/CgV,EAEJ,EAEMoI,EAAalB,EAAUW,QAAQlH,EAAQ3V,IACvCqd,EAAgBP,EAAaM,QAEbhe,IAAlBie,EAGqC,UAArCnB,EAAUW,QAAQQ,KACyB,IAA3CA,EAAcrG,QAAQrB,EAAQ3V,KAE9Bid,EACE,+BACEjd,EADF,yBAIEkc,EAAUoB,MAAMD,GAChB,SACA1H,EAAQ3V,GACR,OAEJ+U,GAAa,GACW,WAAfqI,GAA+C,YAApBT,IACpCpF,EAAO5X,EAAmB4X,EAAMvX,GAChCkc,EAAUK,MACR5G,EAAQ3V,GACRoc,EAAiBO,GACjBpF,SAG6BnY,IAAxB0d,EAAkB,MAE3BG,EACE,8BACEjd,EACA,gBACAkc,EAAUoB,MAAM/iB,OAAOS,KAAK8hB,IAC5B,eACAM,EACA,MACAzH,EAAQ3V,GACR,KAEJ+U,GAAa,EAEjB,CAQA,cAAO8H,CAAQU,GACb,MAAM/K,SAAc+K,EAEpB,MAAa,WAAT/K,EACa,OAAX+K,EACK,OAELA,aAAkBC,QACb,UAELD,aAAkB1e,OACb,SAEL0e,aAAkBxe,OACb,SAELpE,MAAMC,QAAQ2iB,GACT,QAELA,aAAkBtjB,KACb,YAEemF,IAApBme,EAAOE,SACF,OAEuB,IAA5BF,EAAOG,iBACF,SAEF,SACW,WAATlL,EACF,SACW,YAATA,EACF,UACW,WAATA,EACF,cACWpT,IAAToT,EACF,YAEFA,CACT,CAQA,oBAAOkK,CAAc1c,EAAQ2V,EAAS4B,GACpC,MAAMoG,EAAczB,EAAU0B,cAAc5d,EAAQ2V,EAAS4B,GAAM,GAC7DsG,EAAe3B,EAAU0B,cAAc5d,EAAQ8U,EAAY,IAAI,GAKrE,IAAIgJ,EAEFA,OAD6B1e,IAA3Bue,EAAYI,WAEZ,OACA7B,EAAUiB,cAAcQ,EAAYpG,KAAMvX,EAAQ,IAClD,6CACA2d,EAAYI,WACZ,SAEFF,EAAaG,UAXe,GAY5BL,EAAYK,SAAWH,EAAaG,SAGlC,OACA9B,EAAUiB,cAAcQ,EAAYpG,KAAMvX,EAAQ,IAClD,uDACAkc,EAAUiB,cACRU,EAAatG,KACbsG,EAAaI,aACb,IAEKN,EAAYK,UAxBM,EA0BzB,mBACAL,EAAYM,aACZ,KACA/B,EAAUiB,cAAcQ,EAAYpG,KAAMvX,GAG1C,gCACAkc,EAAUoB,MAAM/iB,OAAOS,KAAK2a,IAC5BuG,EAAUiB,cAAc5F,EAAMvX,GAGlC4a,QAAQC,MACN,+BAAiC7a,EAAS,IAAM8d,EAChD9I,GAEFD,GAAa,CACf,CAWA,oBAAO6I,CAAc5d,EAAQ2V,EAAS4B,EAAM2G,GAAY,GACtD,IAAI5c,EAAM,IACN2c,EAAe,GACfE,EAAmB,GACvB,MAAMC,EAAkBpe,EAAOqe,cAC/B,IAAIN,EACJ,IAAK,MAAMO,KAAM3I,EAAS,CACxB,IAAIqI,EACJ,QAA6B5e,IAAzBuW,EAAQ2I,GAAIvB,WAAwC,IAAdmB,EAAoB,CAC5D,MAAMzd,EAASyb,EAAU0B,cACvB5d,EACA2V,EAAQ2I,GACR3e,EAAmB4X,EAAM+G,IAEvBhd,EAAMb,EAAOud,WACfC,EAAexd,EAAOwd,aACtBE,EAAmB1d,EAAO8W,KAC1BjW,EAAMb,EAAOud,SACbD,EAAatd,EAAOsd,WAExB,MACoD,IAA9CO,EAAGD,cAAcrH,QAAQoH,KAC3BL,EAAaO,GAEfN,EAAW9B,EAAUqC,oBAAoBve,EAAQse,GAC7Chd,EAAM0c,IACRC,EAAeK,EACfH,EAAmBre,EAAUyX,GAC7BjW,EAAM0c,EAGZ,CACA,MAAO,CACLC,aAAcA,EACd1G,KAAM4G,EACNH,SAAU1c,EACVyc,WAAYA,EAEhB,CASA,oBAAOZ,CAAc5F,EAAMvX,EAAQwe,EAAS,8BAC1C,IAAIC,EAAM,OAASD,EAAS,gBAC5B,IAAK,IAAIxc,EAAI,EAAGA,EAAIuV,EAAK1d,OAAQmI,IAAK,CACpC,IAAK,IAAI0c,EAAI,EAAGA,EAAI1c,EAAI,EAAG0c,IACzBD,GAAO,KAETA,GAAOlH,EAAKvV,GAAK,OACnB,CACA,IAAK,IAAI0c,EAAI,EAAGA,EAAInH,EAAK1d,OAAS,EAAG6kB,IACnCD,GAAO,KAETA,GAAOze,EAAS,KAChB,IAAK,IAAIgC,EAAI,EAAGA,EAAIuV,EAAK1d,OAAS,EAAGmI,IAAK,CACxC,IAAK,IAAI0c,EAAI,EAAGA,EAAInH,EAAK1d,OAASmI,EAAG0c,IACnCD,GAAO,KAETA,GAAO,KACT,CACA,OAAOA,EAAM,MACf,CAOA,YAAOnB,CAAM3H,GACX,OAAO9G,KAAKC,UAAU6G,GACnBgJ,QAAQ,+BAAgC,IACxCA,QAAQ,OAAQ,KACrB,CAkBA,0BAAOJ,CAAoBxkB,EAAGC,GAC5B,GAAiB,IAAbD,EAAEF,OAAc,OAAOG,EAAEH,OAC7B,GAAiB,IAAbG,EAAEH,OAAc,OAAOE,EAAEF,OAE7B,MAAM+kB,EAAS,GAGf,IAAI5c,EAMA0c,EALJ,IAAK1c,EAAI,EAAGA,GAAKhI,EAAEH,OAAQmI,IACzB4c,EAAO5c,GAAK,CAACA,GAKf,IAAK0c,EAAI,EAAGA,GAAK3kB,EAAEF,OAAQ6kB,IACzBE,EAAO,GAAGF,GAAKA,EAIjB,IAAK1c,EAAI,EAAGA,GAAKhI,EAAEH,OAAQmI,IACzB,IAAK0c,EAAI,EAAGA,GAAK3kB,EAAEF,OAAQ6kB,IACrB1kB,EAAE6kB,OAAO7c,EAAI,IAAMjI,EAAE8kB,OAAOH,EAAI,GAClCE,EAAO5c,GAAG0c,GAAKE,EAAO5c,EAAI,GAAG0c,EAAI,GAEjCE,EAAO5c,GAAG0c,GAAKrd,KAAKC,IAClBsd,EAAO5c,EAAI,GAAG0c,EAAI,GAAK,EACvBrd,KAAKC,IACHsd,EAAO5c,GAAG0c,EAAI,GAAK,EACnBE,EAAO5c,EAAI,GAAG0c,GAAK,IAO7B,OAAOE,EAAO5kB,EAAEH,QAAQE,EAAEF,OAC5B,wBG1XI,YAAkBilB,GACtB,OAQF,SAA4BA,GAC1B,IAAKC,EAAIC,EAAIC,GA4Bf,YAAqBH,GACnB,MAAMI,EAkCR,WACE,IAAIC,EAAI,WAER,OAAO,SAAUC,GACf,MAAMhG,EAASgG,EAAKle,WACpB,IAAK,IAAIc,EAAI,EAAGA,EAAIoX,EAAOvf,OAAQmI,IAAK,CACtCmd,GAAK/F,EAAOiG,WAAWrd,GACvB,IAAIP,EAAI,mBAAsB0d,EAC9BA,EAAI1d,IAAM,EACVA,GAAK0d,EACL1d,GAAK0d,EACLA,EAAI1d,IAAM,EACVA,GAAK0d,EACLA,GAAS,WAAJ1d,CACP,CACA,OAAmB,wBAAX0d,IAAM,EAChB,CACF,CAnDeG,GAEb,IAAIP,EAAKG,EAAK,KACVF,EAAKE,EAAK,KACVD,EAAKC,EAAK,KAEd,IAAK,IAAIld,EAAI,EAAGA,EAAI8c,EAAKjlB,OAAQmI,IAC/B+c,GAAMG,EAAKJ,EAAK9c,IACZ+c,EAAK,IACPA,GAAM,GAERC,GAAME,EAAKJ,EAAK9c,IACZgd,EAAK,IACPA,GAAM,GAERC,GAAMC,EAAKJ,EAAK9c,IACZid,EAAK,IACPA,GAAM,GAIV,MAAO,CAACF,EAAIC,EAAIC,EAClB,CAnDqBM,CAAST,GACxBU,EAAI,EAER,MAAMC,EAAc,KAClB,MAAMld,EAAI,QAAUwc,EAAS,uBAAJS,EAGzB,OAFAT,EAAKC,EACLA,EAAKC,EACGA,EAAK1c,GAAKid,EAAQ,EAAJjd,IAYxB,OATAkd,EAAOC,OAAS,IAAyB,WAAXD,IAE9BA,EAAOE,QAAU,IACfF,IAAyC,uBAAjB,QAAXA,IAAuB,GAEtCA,EAAOG,UAAY,OACnBH,EAAOX,KAAOA,EACdW,EAAOI,QAAU,MAEVJ,CACT,CA7BSK,CAAmBhB,EAAKjlB,OAASilB,EAAO,CAAC7kB,KAAK8lB,OACvD,8KNudM,SAAuBC,EAAeC,GAC1C,IAAIC,EAAUF,EAAKjO,UAAUrD,MAAM,KACnC,MAAMyR,EAAaF,EAAWvR,MAAM,KACpCwR,EAAUA,EAAQE,OAChBD,EAAWnK,OAAO,SAAUjE,GAC1B,OAAQmO,EAAQG,SAAStO,EAC3B,IAEFiO,EAAKjO,UAAYmO,EAAQvJ,KAAK,IAChC,eA0jBM,SAAqBvZ,EAAsByE,GAC/C,MAAMye,EAAW1e,EAAaC,GAC9B,IAAK,MAAOjE,EAAK7C,KAAUR,OAAOgmB,QAAQD,GACxCljB,EAAQgB,MAAMoiB,YAAY5iB,EAAK7C,EAEnC,uBAsVM,SACJ0lB,EACAC,EACAC,EACAC,GAGA,IAAIC,EAAY,EACZC,EAAM,EACNC,EAAON,EAAa5mB,OAAS,EAEjC,KAAOinB,GAAOC,GAAQF,EALA,KAK2B,CAC/C,MAAMG,EAAS3f,KAAKc,OAAO2e,EAAMC,GAAQ,GAEnCtJ,EAAOgJ,EAAaO,GAGpBC,EAAeP,OAFIthB,IAAXwhB,EAAuBnJ,EAAKkJ,GAASlJ,EAAKkJ,GAAOC,IAG/D,GAAoB,GAAhBK,EAEF,OAAOD,GACkB,GAAhBC,EAETH,EAAME,EAAS,EAGfD,EAAOC,EAAS,EAGlBH,GACF,CAEA,OAAO,CACT,sBAcM,SACJJ,EACAljB,EACAojB,EACAO,EACAR,GAGA,IAGIS,EACApmB,EACAqmB,EACAJ,EANAH,EAAY,EACZC,EAAM,EACNC,EAAON,EAAa5mB,OAAS,EAajC,IAPA6mB,EACgBthB,MAAdshB,EACIA,EACA,SAAU3mB,EAAWC,GACnB,OAAOD,GAAKC,EAAI,EAAID,EAAIC,GAAI,EAAK,CACnC,EAEC8mB,GAAOC,GAAQF,EAhBA,KAgB2B,CAQ/C,GANAG,EAAS3f,KAAKc,MAAM,IAAO4e,EAAOD,IAClCK,EAAYV,EAAapf,KAAKG,IAAI,EAAGwf,EAAS,IAAIL,GAClD5lB,EAAQ0lB,EAAaO,GAAQL,GAC7BS,EACEX,EAAapf,KAAKC,IAAImf,EAAa5mB,OAAS,EAAGmnB,EAAS,IAAIL,GAE7B,GAA7BD,EAAW3lB,EAAOwC,GAEpB,OAAOyjB,EACF,GACLN,EAAWS,EAAW5jB,GAAU,GAChCmjB,EAAW3lB,EAAOwC,GAAU,EAG5B,MAAyB,UAAlB2jB,EAA6B7f,KAAKG,IAAI,EAAGwf,EAAS,GAAKA,EACzD,GACLN,EAAW3lB,EAAOwC,GAAU,GAC5BmjB,EAAWU,EAAW7jB,GAAU,EAGhC,MAAyB,UAAlB2jB,EACHF,EACA3f,KAAKC,IAAImf,EAAa5mB,OAAS,EAAGmnB,EAAS,GAG3CN,EAAW3lB,EAAOwC,GAAU,EAE9BujB,EAAME,EAAS,EAGfD,EAAOC,EAAS,EAGpBH,GACF,CAGA,OAAO,CACT,6HAnoCM,SAAqB9mB,EAAcC,GACvC,GAAID,EAAEF,SAAWG,EAAEH,OACjB,OAAO,EAGT,IAAK,IAAImI,EAAI,EAAGqf,EAAMtnB,EAAEF,OAAQmI,EAAIqf,EAAKrf,IACvC,GAAIjI,EAAEiI,IAAMhI,EAAEgI,GACZ,OAAO,EAIX,OAAO,CACT,6BAjOM,SAAUsf,EACdvnB,EACAC,EACAkF,GAAgB,GAIhB,IAAK,MAAM9E,KAAQL,EACjB,QAAgBqF,IAAZpF,EAAEI,GACJ,GAAgB,OAAZJ,EAAEI,IAAqC,iBAAZJ,EAAEI,GAE/B6E,EAAalF,EAAGC,EAAGI,EAAM8E,OACpB,CACL,MAAMqiB,EAAQxnB,EAAEK,GACVonB,EAAQxnB,EAAEI,GACZ4E,EAASuiB,IAAUviB,EAASwiB,IAC9BF,EAAcC,EAAOC,EAAOtiB,EAEhC,CAGN,YA+VM,SAAkBqe,EAAavf,GACnC,GAAIrD,MAAMC,QAAQ2iB,GAAS,CAEzB,MAAM8D,EAAM9D,EAAO1jB,OACnB,IAAK,IAAImI,EAAI,EAAGA,EAAIqf,EAAKrf,IACvBhE,EAASuf,EAAOvb,GAAIA,EAAGub,EAE3B,MAEE,IAAK,MAAM3f,KAAO2f,EACZhjB,OAAOC,UAAUiF,eAAe/E,KAAK6iB,EAAQ3f,IAC/CI,EAASuf,EAAO3f,GAAMA,EAAK2f,EAInC,oBAlFM,SAA0ByC,GAC9B,OAAOA,EAAK9L,wBAAwBjE,IACtC,qBAOM,SAA2B+P,GAC/B,OAAOA,EAAK9L,wBAAwBuN,KACtC,mBAOM,SAAyBzB,GAC7B,OAAOA,EAAK9L,wBAAwB/D,GACtC,iCAwpCE,MAAMuR,EAAQvlB,SAASC,cAAc,KACrCslB,EAAMtjB,MAAMyT,MAAQ,OACpB6P,EAAMtjB,MAAM0T,OAAS,QAErB,MAAM6P,EAAQxlB,SAASC,cAAc,OACrCulB,EAAMvjB,MAAMwjB,SAAW,WACvBD,EAAMvjB,MAAM+R,IAAM,MAClBwR,EAAMvjB,MAAM6R,KAAO,MACnB0R,EAAMvjB,MAAM2d,WAAa,SACzB4F,EAAMvjB,MAAMyT,MAAQ,QACpB8P,EAAMvjB,MAAM0T,OAAS,QACrB6P,EAAMvjB,MAAMyjB,SAAW,SACvBF,EAAMplB,YAAYmlB,GAElBvlB,SAASe,KAAKX,YAAYolB,GAC1B,MAAMG,EAAKJ,EAAMK,YACjBJ,EAAMvjB,MAAMyjB,SAAW,SACvB,IAAIG,EAAKN,EAAMK,YAOf,OANID,GAAME,IACRA,EAAKL,EAAMzR,aAGb/T,SAASe,KAAKR,YAAYilB,GAEnBG,EAAKE,CACd,uBA1iCEjlB,EAA2B7B,OAAO6B,OAKlC,IAAIQ,EAA6B,KASjC,OARKR,IAEMA,EAAMQ,OACfA,EAASR,EAAMQ,OACNR,EAAMklB,aACf1kB,EAASR,EAAMklB,aAGX1kB,aAAkB4F,UAID,MAAnB5F,EAAOkgB,UAAuC,GAAnBlgB,EAAOkgB,WAEpClgB,EAASA,EAAOd,WACVc,aAAkB4F,UAKnB5F,EAXE,IAYX,YA/PM,SAAkBggB,GACtB,MAAM/K,SAAc+K,EAEpB,MAAa,WAAT/K,EACa,OAAX+K,EACK,OAELA,aAAkBC,QACb,UAELD,aAAkB1e,OACb,SAEL0e,aAAkBxe,OACb,SAELpE,MAAMC,QAAQ2iB,GACT,QAELA,aAAkBtjB,KACb,OAGF,SAEI,WAATuY,EACK,SAEI,YAATA,EACK,UAEI,WAATA,EACK,cAEIpT,IAAToT,EACK,YAGFA,CACT,cAgOM,SAAoBpV,EAAkBC,GAC1C,IAAI2iB,EAAa5iB,EAEjB,KAAO4iB,GAAM,CACX,GAAIA,IAAS3iB,EACX,OAAO,EACF,IAAI2iB,EAAKvjB,WAGd,OAAO,EAFPujB,EAAOA,EAAKvjB,UAIhB,CAEA,OAAO,CACT,yCA2kBM,SAAwB1C,EAAQmoB,GACpC,IAAK,IAAIlgB,EAAI,EAAGA,EAAIjI,EAAEF,OAAQmI,IAAK,CACjC,MAAMmgB,EAAIpoB,EAAEiI,GACZ,IAAI0c,EACJ,IAAKA,EAAI1c,EAAG0c,EAAI,GAAKwD,EAAQC,EAAGpoB,EAAE2kB,EAAI,IAAM,EAAGA,IAC7C3kB,EAAE2kB,GAAK3kB,EAAE2kB,EAAI,GAEf3kB,EAAE2kB,GAAKyD,CACT,CACA,OAAOpoB,CACT,WAvoCM,SAAiBgB,GACrB,GAAIA,aAAiBd,KACnB,OAAO,EACF,GAAI6E,EAAS/D,GAAQ,CAG1B,GADcwD,EAAamC,KAAK3F,GAE9B,OAAO,EACF,IAAKqnB,MAAMnoB,KAAKsiB,MAAMxhB,IAC3B,OAAO,CAEX,CAEA,OAAO,CACT,sGAuoCM,SACJsnB,EACA1M,EACA3V,EACAsiB,EAAqB,CAAA,GAGrB,MAAMC,EAAY,SAAUnI,GAC1B,OAAOA,OACT,EAEMpb,EAAW,SAAUob,GACzB,OAAe,OAARA,GAA+B,iBAARA,CAChC,EAaA,IAAKpb,EAASqjB,GACZ,MAAM,IAAInU,MAAM,2CAGlB,IAAKlP,EAAS2W,GACZ,MAAM,IAAIzH,MAAM,uCAGlB,IAAKqU,EAAUviB,GACb,MAAM,IAAIkO,MAAM,sCAGlB,IAAKlP,EAASsjB,GACZ,MAAM,IAAIpU,MAAM,6CAOlB,MAeMsU,EAAY7M,EAAQ3V,GAEpByiB,EADezjB,EAASsjB,KA9Cd,SAAUlI,GACxB,IAAK,MAAMjN,KAAKiN,EACd,GAAI7f,OAAOC,UAAUiF,eAAe/E,KAAK0f,EAAKjN,GAC5C,OAAO,EAGX,OAAO,CACT,CAuCiDuV,CAAQJ,GACrBA,EAActiB,QAAUZ,EACtDujB,EAAgBF,EAAeA,EAAa1M,aAAU3W,EAK5D,QAAkBA,IAAdojB,EACF,OAGF,GAAyB,kBAAdA,EAMT,OALKxjB,EAASqjB,EAAYriB,MACxBqiB,EAAYriB,GAAU,CAAA,QAGxBqiB,EAAYriB,GAAQ+V,QAAUyM,GAIhC,GAAkB,OAAdA,IAAuBxjB,EAASqjB,EAAYriB,IAAU,CAExD,IAAIuiB,EAAUE,GAGZ,OAFAJ,EAAYriB,GAAUzF,OAAO8I,OAAOof,EAIxC,CAEA,IAAKzjB,EAASwjB,GACZ,OAOF,IAAIzM,GAAU,OAEY3W,IAAtBojB,EAAUzM,QACZA,EAAUyM,EAAUzM,aAGE3W,IAAlBujB,IACF5M,EAAU0M,EAAa1M,SA5DX,SAAUxY,EAAaoY,EAAc3V,GAC9ChB,EAASzB,EAAOyC,MACnBzC,EAAOyC,GAAU,CAAA,GAGnB,MAAM4iB,EAAMjN,EAAQ3V,GACd6iB,EAAMtlB,EAAOyC,GACnB,IAAK,MAAM5F,KAAQwoB,EACbroB,OAAOC,UAAUiF,eAAe/E,KAAKkoB,EAAKxoB,KAC5CyoB,EAAIzoB,GAAQwoB,EAAIxoB,GAGtB,CAoDA0oB,CAAQT,EAAa1M,EAAS3V,GAC9BqiB,EAAYriB,GAAQ+V,QAAUA,CAChC,+BA3kBM,SAA0B1I,EAAesM,GAC7C,GAAItM,EAAMgT,SAAS,QACjB,OAAOhT,EACF,GAAIA,EAAMgT,SAAS,OAAQ,CAChC,MAAM5d,EAAM4K,EACToB,OAAOpB,EAAM2J,QAAQ,KAAO,GAC5B2H,QAAQ,IAAK,IACbjQ,MAAM,KACT,MAAO,QAAUjM,EAAI,GAAK,IAAMA,EAAI,GAAK,IAAMA,EAAI,GAAK,IAAMkX,EAAU,GAC1E,CAAO,CACL,MAAMlX,EAAMlC,EAAS8M,GACrB,OAAW,MAAP5K,EACK4K,EAEA,QAAU5K,EAAI9B,EAAI,IAAM8B,EAAI5B,EAAI,IAAM4B,EAAIzI,EAAI,IAAM2f,EAAU,GAEzE,CACF,eAyDM,SACJoJ,EACA9I,GAEA,GAAInb,EAASikB,GAAa,CACxB,IAAIC,EAAmBD,EACvB,GAAIjgB,EAAWkgB,GAAW,CACxB,MAAMvgB,EAAMugB,EACTvU,OAAO,GACPA,OAAO,EAAGuU,EAASnpB,OAAS,GAC5B6U,MAAM,KACN5T,IAAI,SAAUC,GACb,OAAO6F,SAAS7F,EAClB,GACFioB,EAAWliB,EAAS2B,EAAI,GAAIA,EAAI,GAAIA,EAAI,GAC1C,CACA,IAA6B,IAAzBG,EAAWogB,GAAoB,CACjC,MAAMtT,EAAMhN,EAASsgB,GACfC,EAAkB,CACtBxhB,EAAGiO,EAAIjO,EACPC,EAAW,GAARgO,EAAIhO,EACPC,EAAGN,KAAKC,IAAI,EAAW,KAARoO,EAAI/N,IAEfuhB,EAAiB,CACrBzhB,EAAGiO,EAAIjO,EACPC,EAAGL,KAAKC,IAAI,EAAW,KAARoO,EAAIhO,GACnBC,EAAW,GAAR+N,EAAI/N,GAEHwhB,EAAiB3gB,EACrB0gB,EAAezhB,EACfyhB,EAAexhB,EACfwhB,EAAevhB,GAEXyhB,EAAkB5gB,EACtBygB,EAAgBxhB,EAChBwhB,EAAgBvhB,EAChBuhB,EAAgBthB,GAElB,MAAO,CACL0hB,WAAYL,EACZM,OAAQH,EACRI,UAAW,CACTF,WAAYD,EACZE,OAAQH,GAEVK,MAAO,CACLH,WAAYD,EACZE,OAAQH,GAGd,CACE,MAAO,CACLE,WAAYL,EACZM,OAAQN,EACRO,UAAW,CACTF,WAAYL,EACZM,OAAQN,GAEVQ,MAAO,CACLH,WAAYL,EACZM,OAAQN,GAIhB,CACE,GAAI/I,EAAc,CA+BhB,MA9B+B,CAC7BoJ,WAAYN,EAAWM,YAAcpJ,EAAaoJ,WAClDC,OAAQP,EAAWO,QAAUrJ,EAAaqJ,OAC1CC,UAAWzkB,EAASikB,EAAWQ,WAC3B,CACED,OAAQP,EAAWQ,UACnBF,WAAYN,EAAWQ,WAEzB,CACEF,WACGN,EAAWQ,WAAaR,EAAWQ,UAAUF,YAC9CpJ,EAAasJ,UAAUF,WACzBC,OACGP,EAAWQ,WAAaR,EAAWQ,UAAUD,QAC9CrJ,EAAasJ,UAAUD,QAE/BE,MAAO1kB,EAASikB,EAAWS,OACvB,CACEF,OAAQP,EAAWS,MACnBH,WAAYN,EAAWS,OAEzB,CACEF,OACGP,EAAWS,OAAST,EAAWS,MAAMF,QACtCrJ,EAAauJ,MAAMF,OACrBD,WACGN,EAAWS,OAAST,EAAWS,MAAMH,YACtCpJ,EAAauJ,MAAMH,YAI/B,CA6BE,MA5B2B,CACzBA,WAAYN,EAAWM,iBAAcjkB,EACrCkkB,OAAQP,EAAWO,aAAUlkB,EAC7BmkB,UAAWzkB,EAASikB,EAAWQ,WAC3B,CACED,OAAQP,EAAWQ,UACnBF,WAAYN,EAAWQ,WAEzB,CACEF,WACGN,EAAWQ,WAAaR,EAAWQ,UAAUF,iBAC9CjkB,EACFkkB,OACGP,EAAWQ,WAAaR,EAAWQ,UAAUD,aAC9ClkB,GAERokB,MAAO1kB,EAASikB,EAAWS,OACvB,CACEF,OAAQP,EAAWS,MACnBH,WAAYN,EAAWS,OAEzB,CACEF,OACGP,EAAWS,OAAST,EAAWS,MAAMF,aAAWlkB,EACnDikB,WACGN,EAAWS,OAAST,EAAWS,MAAMH,iBAAejkB,GAMrE,mBAzZM,SAAyBrC,GACxBA,IACHA,EAAQ7B,OAAO6B,OAGZA,IAEMA,EAAM0mB,eACf1mB,EAAM0mB,iBAGL1mB,EAAc2mB,aAAc,EAEjC,kCHplBElM,KACGmM,GAEH,OAAOnqB,EAAiB,CAAA,EAAWge,KAASmM,EAC9C,uBGoDM,SAAUC,EAAmBC,GACjC,GAAIA,EACF,MAAqC,IAA9BA,EAAUC,iBAA0B,CACzC,MAAMC,EAAQF,EAAU/L,WACpBiM,IACFH,EAAmBG,GACnBF,EAAUnnB,YAAYqnB,GAE1B,CAEJ,oBA0aM,SAA0B/D,EAAeC,GAC7C,IAAIC,EAAUF,EAAKjO,UAAUrD,MAAM,KACnC,MAAMsV,EAAa/D,EAAWvR,MAAM,KACpCwR,EAAUA,EAAQlK,OAAO,SAAUjE,GACjC,OAAQiS,EAAW3D,SAAStO,EAC9B,GACAiO,EAAKjO,UAAYmO,EAAQvJ,KAAK,IAChC,kBAwjBM,SAAwBvZ,EAAsByE,GAClD,MAAMye,EAAW1e,EAAaC,GAC9B,IAAK,MAAMjE,KAAOrD,OAAOS,KAAKslB,GAC5BljB,EAAQgB,MAAM6lB,eAAermB,EAEjC,0BA8GM,SACJsmB,EACAhhB,GAEA,GAAwB,OAApBA,GAAuD,iBAApBA,EAA8B,CAEnE,MAAME,EAAW7I,OAAO8I,OAAOH,GAC/B,IAAK,IAAIlB,EAAI,EAAGA,EAAIkiB,EAAOrqB,OAAQmI,IAC7BzH,OAAOC,UAAUiF,eAAe/E,KAAKwI,EAAiBghB,EAAOliB,KACtB,iBAA9BkB,EAAgBghB,EAAOliB,MAChCoB,EAAS8gB,EAAOliB,IAAMiB,EAAaC,EAAgBghB,EAAOliB,MAIhE,OAAOoB,CACT,CACE,OAAO,IAEX,wBAt9BM,SACJ+gB,EACApqB,EACAC,EACAkF,GAAgB,GAGhB,GAAIvE,MAAMC,QAAQZ,GAChB,MAAM,IAAI2I,UAAU,0CAGtB,IAAK,IAAIN,EAAI,EAAGA,EAAI8hB,EAAMtqB,OAAQwI,IAAK,CACrC,MAAMjI,EAAO+pB,EAAM9hB,GACnB,GAAI9H,OAAOC,UAAUiF,eAAe/E,KAAKV,EAAGI,GAC1C,GAAIJ,EAAEI,IAASJ,EAAEI,GAAM2S,cAAgBxS,YACrB6E,IAAZrF,EAAEK,KACJL,EAAEK,GAAQ,CAAA,GAERL,EAAEK,GAAM2S,cAAgBxS,OAC1BgF,EAAWxF,EAAEK,GAAOJ,EAAEI,IAAO,EAAO8E,GAEpCD,EAAalF,EAAGC,EAAGI,EAAM8E,OAEtB,IAAIvE,MAAMC,QAAQZ,EAAEI,IACzB,MAAM,IAAIuI,UAAU,0CAEpB1D,EAAalF,EAAGC,EAAGI,EAAM8E,EAC3B,CAEJ,CACA,OAAOnF,CACT,oBAhEM,SACJoqB,EACApqB,KACGqqB,GAEH,IAAKzpB,MAAMC,QAAQupB,GACjB,MAAM,IAAIjW,MAAM,wDAGlB,IAAK,MAAMmW,KAASD,EAClB,IAAK,IAAI/hB,EAAI,EAAGA,EAAI8hB,EAAMtqB,OAAQwI,IAAK,CACrC,MAAMjI,EAAO+pB,EAAM9hB,GACfgiB,GAAS9pB,OAAOC,UAAUiF,eAAe/E,KAAK2pB,EAAOjqB,KACvDL,EAAEK,GAAQiqB,EAAMjqB,GAEpB,CAEF,OAAOL,CACT,2BA8DM,SACJuqB,EACAvqB,EACAC,EACAkF,GAAgB,GAIhB,GAAIvE,MAAMC,QAAQZ,GAChB,MAAM,IAAI2I,UAAU,0CAGtB,IAAK,MAAMvI,KAAQJ,EACjB,GAAKO,OAAOC,UAAUiF,eAAe/E,KAAKV,EAAGI,KAGzCkqB,EAAejE,SAASjmB,GAI5B,GAAIJ,EAAEI,IAASJ,EAAEI,GAAM2S,cAAgBxS,YACrB6E,IAAZrF,EAAEK,KACJL,EAAEK,GAAQ,CAAA,GAERL,EAAEK,GAAM2S,cAAgBxS,OAC1BgF,EAAWxF,EAAEK,GAAOJ,EAAEI,IAEtB6E,EAAalF,EAAGC,EAAGI,EAAM8E,QAEtB,GAAIvE,MAAMC,QAAQZ,EAAEI,IAAQ,CACjCL,EAAEK,GAAQ,GACV,IAAK,IAAI4H,EAAI,EAAGA,EAAIhI,EAAEI,GAAMP,OAAQmI,IAClCjI,EAAEK,GAAMoC,KAAKxC,EAAEI,GAAM4H,GAEzB,MACE/C,EAAalF,EAAGC,EAAGI,EAAM8E,GAI7B,OAAOnF,CACT,aAoQM,SAAmBwqB,GACvB,IAAIC,GAAY,EAEhB,MAAO,KACAA,IACHA,GAAY,EACZC,sBAAsB,KACpBD,GAAY,EACZD,OAIR,wBA6lCM,SAAkBG,EAAWC,GACjC,IAAIC,EACCjqB,MAAMC,QAAQ+pB,KACjBA,EAAY,CAACA,IAEf,IAAK,MAAME,KAAUH,EACnB,GAAIG,EAAQ,CACVD,EAAYC,EAAOF,EAAU,IAC7B,IAAK,IAAI3iB,EAAI,EAAGA,EAAI2iB,EAAU9qB,OAAQmI,IAChC4iB,IACFA,EAAYA,EAAUD,EAAU3iB,KAGpC,QAAyB,IAAd4iB,EACT,KAEJ,CAEF,OAAOA,CACT,4BA7oCErH,EACA3f,EACA7C,GAEA,OAAIwiB,EAAO3f,KAAS7C,IAClBwiB,EAAO3f,GAAO7C,GACP,EAIX"}