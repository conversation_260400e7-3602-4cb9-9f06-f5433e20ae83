import { e, b, g, c, f, a, h } from './chunk-JXBJZR5A.js';
import { twMerge, extendTailwindMerge } from 'tailwind-merge';

var ie={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},_=s=>s||void 0,M=(...s)=>_(e(s).filter(Boolean).join(" ")),R=null,v={},q=!1,j=(...s)=>b$1=>b$1.twMerge?((!R||q)&&(q=!1,R=b(v)?twMerge:extendTailwindMerge({...v,extend:{theme:v.theme,classGroups:v.classGroups,conflictingClassGroupModifiers:v.conflictingClassGroupModifiers,conflictingClassGroups:v.conflictingClassGroups,...v.extend}})),_(R(M(s)))):M(s),Z=(s,b)=>{for(let e in b)s.hasOwnProperty(e)?s[e]=M(s[e],b[e]):s[e]=b[e];return s},ce=(s,b$1)=>{let{extend:e=null,slots:N={},variants:F={},compoundVariants:U=[],compoundSlots:C=[],defaultVariants:W={}}=s,m={...ie,...b$1},S=e!=null&&e.base?M(e.base,s==null?void 0:s.base):s==null?void 0:s.base,g$1=e!=null&&e.variants&&!b(e.variants)?g(F,e.variants):F,A=e!=null&&e.defaultVariants&&!b(e.defaultVariants)?{...e.defaultVariants,...W}:W;!b(m.twMergeConfig)&&!c(m.twMergeConfig,v)&&(q=!0,v=m.twMergeConfig);let O=b(e==null?void 0:e.slots),$=b(N)?{}:{base:M(s==null?void 0:s.base,O&&(e==null?void 0:e.base)),...N},w=O?$:Z({...e==null?void 0:e.slots},b($)?{base:s==null?void 0:s.base}:$),h$1=b(e==null?void 0:e.compoundVariants)?U:f(e==null?void 0:e.compoundVariants,U),V=f=>{if(b(g$1)&&b(N)&&O)return j(S,f==null?void 0:f.class,f==null?void 0:f.className)(m);if(h$1&&!Array.isArray(h$1))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof h$1}`);if(C&&!Array.isArray(C))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof C}`);let K=(t,n,a=[],i)=>{let r=a;if(typeof n=="string")r=r.concat(h(n).split(" ").map(o=>`${t}:${o}`));else if(Array.isArray(n))r=r.concat(n.reduce((o,c)=>o.concat(`${t}:${c}`),[]));else if(typeof n=="object"&&typeof i=="string"){for(let o in n)if(n.hasOwnProperty(o)&&o===i){let c=n[o];if(c&&typeof c=="string"){let l=h(c);r[i]?r[i]=r[i].concat(l.split(" ").map(u=>`${t}:${u}`)):r[i]=l.split(" ").map(u=>`${t}:${u}`);}else Array.isArray(c)&&c.length>0&&(r[i]=c.reduce((l,u)=>l.concat(`${t}:${u}`),[]));}}return r},z=(t,n=g$1,a$1=null,i=null)=>{var J;let r=n[t];if(!r||b(r))return null;let o=(J=i==null?void 0:i[t])!=null?J:f==null?void 0:f[t];if(o===null)return null;let c=a(o),l=Array.isArray(m.responsiveVariants)&&m.responsiveVariants.length>0||m.responsiveVariants===!0,u=A==null?void 0:A[t],d=[];if(typeof c=="object"&&l)for(let[T,L]of Object.entries(c)){let ne=r[L];if(T==="initial"){u=L;continue}Array.isArray(m.responsiveVariants)&&!m.responsiveVariants.includes(T)||(d=K(T,ne,d,a$1));}let ae=c!=null&&typeof c!="object"?c:a(u),k=r[ae||"false"];return typeof d=="object"&&typeof a$1=="string"&&d[a$1]?Z(d,k):d.length>0?(d.push(k),d):k},P=()=>g$1?Object.keys(g$1).map(t=>z(t,g$1)):null,p=(t,n)=>{if(!g$1||typeof g$1!="object")return null;let a=new Array;for(let i in g$1){let r=z(i,g$1,t,n),o=t==="base"&&typeof r=="string"?r:r&&r[t];o&&(a[a.length]=o);}return a},D={};for(let t in f)f[t]!==void 0&&(D[t]=f[t]);let H=(t,n)=>{var i;let a=typeof(f==null?void 0:f[t])=="object"?{[t]:(i=f[t])==null?void 0:i.initial}:{};return {...A,...D,...a,...n}},I=(t=[],n)=>{let a=[];for(let{class:i,className:r,...o}of t){let c=!0;for(let[l,u]of Object.entries(o)){let d=H(l,n);if(Array.isArray(u)){if(!u.includes(d[l])){c=!1;break}}else if(d[l]!==u){c=!1;break}}c&&(i&&a.push(i),r&&a.push(r));}return a},ee=t=>{let n=I(h$1,t);if(!Array.isArray(n))return n;let a={};for(let i of n)if(typeof i=="string"&&(a.base=j(a.base,i)(m)),typeof i=="object")for(let[r,o]of Object.entries(i))a[r]=j(a[r],o)(m);return a},te=t=>{if(C.length<1)return null;let n={};for(let{slots:a=[],class:i,className:r,...o}of C){if(!b(o)){let c=!0;for(let l of Object.keys(o)){let u=H(l,t)[l];if(u===void 0||(Array.isArray(o[l])?!o[l].includes(u):o[l]!==u)){c=!1;break}}if(!c)continue}for(let c of a)n[c]=n[c]||[],n[c].push([i,r]);}return n};if(!b(N)||!O){let t={};if(typeof w=="object"&&!b(w))for(let n of Object.keys(w))t[n]=a=>{var i,r;return j(w[n],p(n,a),((i=ee(a))!=null?i:[])[n],((r=te(a))!=null?r:[])[n],a==null?void 0:a.class,a==null?void 0:a.className)(m)};return t}return j(S,P(),I(h$1),f==null?void 0:f.class,f==null?void 0:f.className)(m)},x=()=>{if(!(!g$1||typeof g$1!="object"))return Object.keys(g$1)};return V.variantKeys=x(),V.extend=e,V.base=S,V.slots=w,V.variants=g$1,V.defaultVariants=A,V.compoundSlots=C,V.compoundVariants=h$1,V},le=s=>(b,e)=>ce(b,e?g(s,e):s);

export { j as cn, M as cnBase, le as createTV, ie as defaultConfig, ce as tv, _ as voidEmpty };
