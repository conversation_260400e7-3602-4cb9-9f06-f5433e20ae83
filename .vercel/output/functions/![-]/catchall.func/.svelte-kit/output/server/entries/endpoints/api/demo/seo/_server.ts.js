import { j as json } from "../../../../../chunks/index2.js";
const rateLimitMap = /* @__PURE__ */ new Map();
const cachedResponses = {
  "organic skincare": {
    response: `# SEO Keyword Analysis: Organic Skincare

## 🎯 Quick Summary
The organic skincare market shows strong search demand with moderate competition. Focus on long-tail keywords and local intent for best results.

## 📊 Top Keywords

| Keyword | Volume | Difficulty | Intent |
|---------|--------|------------|--------|
| organic skincare products | 8,100 | 🟡 Medium | Commercial |
| natural face moisturizer | 6,600 | 🟢 Low | Commercial |
| organic skincare routine | 4,400 | 🟢 Low | Informational |
| best organic face cream | 3,600 | 🟡 Medium | Commercial |
| organic skincare brands | 2,900 | 🔴 High | Navigational |

## 💡 Opportunities
- **Low Competition Keywords**: "organic skincare for sensitive skin" (1,300 searches)
- **Rising Trends**: "clean beauty organic" (+45% YoY)
- **Local Keywords**: "organic skincare near me" (2,400 searches)

## 📝 Content Recommendations
1. Create comprehensive guides on organic skincare routines
2. Product comparison posts for different skin types
3. Ingredient spotlight content (e.g., "Benefits of Rosehip Oil")`,
    metadata: {
      keywords: ["organic skincare", "natural face moisturizer", "clean beauty", "organic face cream", "skincare routine"]
    }
  },
  "coffee shop": {
    response: `# SEO Keyword Analysis: Coffee Shop (Local)

## 🎯 Quick Summary
Local coffee shop searches have high intent but require location-specific optimization. Focus on "near me" searches and neighborhood keywords.

## 📊 Top Keywords

| Keyword | Volume | Difficulty | Intent |
|---------|--------|------------|--------|
| coffee shop near me | 246,000 | 🟢 Low | Local |
| coffee shops Seattle | 14,800 | 🟡 Medium | Local |
| best coffee Seattle | 9,900 | 🟡 Medium | Local |
| Seattle coffee roasters | 3,600 | 🟢 Low | Local |
| Capitol Hill coffee | 1,900 | 🟢 Low | Local |

## 💡 Opportunities
- **Neighborhood Keywords**: Target specific areas like "Fremont coffee shop"
- **Specialty Terms**: "third wave coffee Seattle" (880 searches)
- **Amenity Keywords**: "coffee shop with wifi" (5,400 searches)

## 📝 Content Recommendations
1. Create location pages for each neighborhood
2. Blog about local coffee culture and events
3. Highlight unique offerings (specialty drinks, local roasts)`,
    metadata: {
      keywords: ["coffee shop near me", "Seattle coffee", "local coffee", "coffee roasters", "specialty coffee"]
    }
  },
  "project management": {
    response: `# SEO Keyword Analysis: Project Management Software

## 🎯 Quick Summary
B2B project management keywords show high commercial intent but face strong competition from established players. Target specific use cases and industries.

## 📊 Top Keywords

| Keyword | Volume | Difficulty | Intent |
|---------|--------|------------|--------|
| project management software | 40,500 | 🔴 High | Commercial |
| project management tools | 33,100 | 🔴 High | Commercial |
| agile project management | 22,200 | 🟡 Medium | Informational |
| project tracking software | 8,100 | 🟡 Medium | Commercial |
| team collaboration tools | 6,600 | 🟡 Medium | Commercial |

## 💡 Opportunities
- **Industry-Specific**: "construction project management" (4,400 searches)
- **Feature-Based**: "kanban project management" (5,400 searches)  
- **Comparison Keywords**: "asana vs monday" (2,900 searches)

## 📝 Content Recommendations
1. Create detailed comparison guides vs competitors
2. Industry-specific use case content
3. Project management methodology guides`,
    metadata: {
      keywords: ["project management", "agile tools", "team collaboration", "project tracking", "kanban software"]
    }
  }
};
function generateGenericResponse(query) {
  return {
    response: `# SEO Keyword Analysis: ${query}

## 🎯 Quick Summary
Your keyword "${query}" shows potential in the search landscape. Here's a preliminary analysis based on common patterns.

## 📊 Estimated Keywords

| Keyword | Est. Volume | Difficulty | Intent |
|---------|-------------|------------|--------|
| ${query} | 2,000-5,000 | 🟡 Medium | Mixed |
| best ${query} | 500-1,000 | 🟡 Medium | Commercial |
| ${query} guide | 300-800 | 🟢 Low | Informational |
| ${query} reviews | 200-500 | 🟢 Low | Commercial |
| how to ${query} | 100-300 | 🟢 Low | Informational |

## 💡 Opportunities
- Target long-tail variations of "${query}"
- Create comprehensive guides and tutorials
- Focus on user intent matching

## 📝 Content Recommendations
1. Create a comprehensive "${query}" guide
2. Build comparison content if applicable
3. Answer common questions about ${query}

**Note**: This is a demo analysis. Sign up for detailed keyword data and competitive insights.`,
    metadata: {
      keywords: [query, `best ${query}`, `${query} guide`, `${query} reviews`, `how to ${query}`]
    }
  };
}
const POST = async ({ request, getClientAddress }) => {
  const clientIp = getClientAddress();
  const now = Date.now();
  const userLimit = rateLimitMap.get(clientIp);
  if (userLimit) {
    if (now < userLimit.resetTime) {
      if (userLimit.count >= 5) {
        return json(
          { error: "Rate limit exceeded. Please try again later or sign up for unlimited access." },
          { status: 429 }
        );
      }
      userLimit.count++;
    } else {
      rateLimitMap.set(clientIp, { count: 1, resetTime: now + 36e5 });
    }
  } else {
    rateLimitMap.set(clientIp, { count: 1, resetTime: now + 36e5 });
  }
  try {
    const { message } = await request.json();
    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 });
    }
    await new Promise((resolve) => setTimeout(resolve, 1500));
    const lowerMessage = message.toLowerCase();
    for (const [key, value] of Object.entries(cachedResponses)) {
      if (lowerMessage.includes(key)) {
        return json(value);
      }
    }
    const genericResponse = generateGenericResponse(message);
    return json(genericResponse);
  } catch (error) {
    console.error("Demo SEO error:", error);
    return json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
};
export {
  POST
};
