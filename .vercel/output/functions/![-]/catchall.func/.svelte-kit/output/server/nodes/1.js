

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.CreHymtw.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/Cvx8ZW61.js","_app/immutable/chunks/wnqW1tdD.js","_app/immutable/chunks/CDPCzm7q.js","_app/immutable/chunks/D5ITLM2v.js","_app/immutable/chunks/BvpDAKCq.js","_app/immutable/chunks/B6j3ckjV.js","_app/immutable/chunks/COZ5WnQL.js","_app/immutable/chunks/CfBaWyh2.js","_app/immutable/chunks/yk44OJLy.js"];
export const stylesheets = [];
export const fonts = [];
