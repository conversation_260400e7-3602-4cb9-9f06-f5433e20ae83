import { K as sanitize_props, R as rest_props, A as push, P as fallback, S as spread_attributes, a4 as clsx, N as slot, Q as bind_props, E as pop, T as copy_payload, U as assign_payload, J as escape_html, Y as attr, F as store_get, V as ensure_array_like, $ as invalid_default_snippet, M as spread_props, Z as stringify, a6 as store_mutate, G as unsubscribe_stores, X as attr_class } from "./index.js";
import { p as page } from "./stores.js";
import "./formData.js";
import { s as superForm, e as enhance } from "./superForm.js";
import "./index2.js";
import { a as zodClient } from "./zod.js";
import { C as Card, a as Card_content } from "./card-content.js";
import "clsx";
import { F as Form_field, C as Control, a as Form_label, b as Form_field_errors } from "./index8.js";
import { b as buttonVariants } from "./index5.js";
import { I as Input } from "./input.js";
import { a as alertVariants } from "./index6.js";
import { g as getEnvironmentState } from "./environment.svelte.js";
import { c as cn } from "./utils.js";
import { B as Button } from "./button.js";
function Alert($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "variant"]);
  push();
  let className = fallback($$props["class"], void 0);
  let variant = fallback($$props["variant"], "default");
  $$payload.out += `<div${spread_attributes(
    {
      class: clsx(cn(alertVariants({ variant }), className)),
      ...$$restProps,
      role: "alert"
    },
    null
  )}><!---->`;
  slot($$payload, $$props, "default", {}, null);
  $$payload.out += `<!----></div>`;
  bind_props($$props, { class: className, variant });
  pop();
}
function Settings_module($$payload, $$props) {
  push();
  var $$store_subs;
  const env = getEnvironmentState();
  const fieldError = (liveForm, name) => {
    let errors2 = liveForm?.errorFields ?? [];
    return errors2.includes(name);
  };
  let showSuccess = false;
  let {
    data,
    schema,
    editable = false,
    dangerous = false,
    title = "",
    message = "",
    fields,
    formTarget = "",
    successTitle = "Success",
    successBody = "",
    editButtonTitle = "",
    editLink = "",
    saveButtonTitle = "Save"
  } = $$props;
  const form = data && schema ? superForm(data, {
    validators: zodClient(schema),
    onUpdated: ({ form: f }) => {
      if (f.valid) {
        showSuccess = true;
      }
    }
  }) : null;
  const formData = form?.form;
  const delayed = form?.delayed;
  const errors = form?.errors;
  form?.enhance ?? enhance;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Card($$payload2, {
      class: "p-6 pb-7 mt-8 max-w-xl flex flex-col md:flex-row shadow",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            if (title) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="text-xl font-bold mb-3 w-48 md:pr-8 flex-none">${escape_html(title)}</div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div class="w-full min-w-48">`;
            if (!showSuccess) {
              $$payload4.out += "<!--[-->";
              if (message) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<!---->`;
                Alert($$payload4, {
                  variant: dangerous ? "destructive" : "default",
                  class: "mb-6",
                  children: ($$payload5) => {
                    if (dangerous) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> <span>${escape_html(message)}</span>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--> `;
              if (editable) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<form class="form-widget flex flex-col" method="POST"${attr("action", formTarget)}>`;
                if (form && store_get($$store_subs ??= {}, "$errors", errors) && store_get($$store_subs ??= {}, "$formData", formData)) {
                  $$payload4.out += "<!--[-->";
                  const each_array = ensure_array_like(fields);
                  $$payload4.out += `<!--[-->`;
                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                    let field = each_array[$$index];
                    $$payload4.out += `<!---->`;
                    Form_field($$payload4, {
                      form,
                      name: field.id,
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->`;
                        Control($$payload5, {
                          children: invalid_default_snippet,
                          $$slots: {
                            default: ($$payload6, { attrs }) => {
                              if (field.label) {
                                $$payload6.out += "<!--[-->";
                                $$payload6.out += `<!---->`;
                                Form_label($$payload6, {
                                  children: ($$payload7) => {
                                    $$payload7.out += `<!---->${escape_html(field.label)}`;
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload6.out += `<!---->`;
                              } else {
                                $$payload6.out += "<!--[!-->";
                              }
                              $$payload6.out += `<!--]--> `;
                              if (editable) {
                                $$payload6.out += "<!--[-->";
                                Input($$payload6, spread_props([
                                  attrs,
                                  {
                                    id: field.id,
                                    name: field.id,
                                    type: field.inputType ?? "text",
                                    disabled: !editable,
                                    placeholder: field.placeholder ?? field.label ?? "",
                                    class: `${stringify(fieldError(store_get($$store_subs ??= {}, "$page", page)?.form, field.id) ? "border-destructive" : "")} w-full max-w-xs mb-3 py-4`,
                                    maxlength: field.maxlength ? field.maxlength : null,
                                    get value() {
                                      return store_get($$store_subs ??= {}, "$formData", formData)[field.id];
                                    },
                                    set value($$value) {
                                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData)[field.id] = $$value);
                                      $$settled = false;
                                    }
                                  }
                                ]));
                                $$payload6.out += `<!----> <!---->`;
                                Form_field_errors($$payload6, {});
                                $$payload6.out += `<!---->`;
                              } else {
                                $$payload6.out += "<!--[!-->";
                                $$payload6.out += `<div class="text-lg mb-3">${escape_html(field.initialValue)}</div>`;
                              }
                              $$payload6.out += `<!--]-->`;
                            }
                          }
                        });
                        $$payload5.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!---->`;
                  }
                  $$payload4.out += `<!--]--> `;
                  if (store_get($$store_subs ??= {}, "$errors", errors)._errors) {
                    $$payload4.out += "<!--[-->";
                    $$payload4.out += `<p class="text-destructive text-sm font-bold mt-1">${escape_html(store_get($$store_subs ??= {}, "$errors", errors)._errors[0])}</p>`;
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]-->`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> <div>`;
                Button($$payload4, {
                  type: "submit",
                  variant: "outline",
                  class: `ml-auto mt-3 min-w-[145px] ${stringify(dangerous ? "border-destructive" : "")}`,
                  disabled: store_get($$store_subs ??= {}, "$delayed", delayed),
                  children: ($$payload5) => {
                    if (store_get($$store_subs ??= {}, "$delayed", delayed)) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<span class="loading loading-spinner loading-md align-middle mx-3"></span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `${escape_html(saveButtonTitle)}`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div></form>`;
              } else {
                $$payload4.out += "<!--[!-->";
                const each_array_1 = ensure_array_like(fields);
                $$payload4.out += `<!--[-->`;
                for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                  let field = each_array_1[$$index_1];
                  $$payload4.out += `<div class="mb-4"><h4 class="font-bold mb-2">${escape_html(field.label)}</h4> <p>${escape_html(field.initialValue)}</p></div>`;
                }
                $$payload4.out += `<!--]--> <a${attr("href", editLink)} class="mt-1">`;
                Button($$payload4, {
                  class: `${stringify(dangerous ? "border-destructive" : "")} min-w-[145px]`,
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(editButtonTitle)}`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></a>`;
              }
              $$payload4.out += `<!--]-->`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `<div><div class="text-l font-bold">${escape_html(successTitle)}</div> <div class="text-base">${escape_html(successBody)}</div></div> <a${attr("href", `/dashboard/${stringify(env.value?.name)}/settings`)}${attr_class(`${stringify(buttonVariants({ size: "sm" }))} mt-3 min-w-[145px]`)}>Return to Settings</a>`;
            }
            $$payload4.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  Settings_module as S
};
