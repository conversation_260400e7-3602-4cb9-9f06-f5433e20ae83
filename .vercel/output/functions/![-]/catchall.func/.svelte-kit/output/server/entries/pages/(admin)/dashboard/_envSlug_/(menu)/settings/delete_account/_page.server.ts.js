import "clsx";
import "../../../../../../../../chunks/client.js";
import "../../../../../../../../chunks/formData.js";
import "../../../../../../../../chunks/superForm.js";
import "../../../../../../../../chunks/index2.js";
import { s as superValidate } from "../../../../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../../../../chunks/zod.js";
import { d as deleteAccountSchema } from "../../../../../../../../chunks/schemas.js";
const load = async () => {
  const form = await superValidate(zod(deleteAccountSchema));
  return { form };
};
export {
  load
};
