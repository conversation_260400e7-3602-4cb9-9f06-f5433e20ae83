import "../../../../../chunks/schemas.js";
import { r as redirect } from "../../../../../chunks/index2.js";
import "clsx";
import "../../../../../chunks/client.js";
import "../../../../../chunks/formData.js";
import "../../../../../chunks/superForm.js";
import { f as fail } from "../../../../../chunks/superValidate.js";
const load = async ({ locals: { safeGetSession } }) => {
  const { user } = await safeGetSession();
  if (user && !user.is_anonymous) {
    return redirect(300, "/find-env");
  }
  return {};
};
const actions = {
  signIn: async ({ request, locals: { supabase } }) => {
    const formData = await request.formData();
    const email = formData.get("email");
    const password = formData.get("password");
    if (!email || !password) {
      return fail(400, { error: "Email and password are required" });
    }
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) {
      console.error("Sign-in error:", error);
      if (error.message.includes("email not confirmed") || error.message.includes("Email not confirmed")) {
        redirect(303, "/login/sign_in?error=email_not_confirmed");
      }
      return fail(400, { error: error.message });
    }
    if (data.user && !data.user.email_confirmed_at) {
      await supabase.auth.signOut();
      redirect(303, "/login/sign_in?error=email_not_confirmed");
    }
    redirect(303, "/find-env");
  }
};
export {
  actions,
  load
};
