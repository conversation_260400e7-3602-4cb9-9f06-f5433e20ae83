import { E as pop, A as push, F as store_get, G as unsubscribe_stores } from "../../chunks/index.js";
import { n as navigating } from "../../chunks/stores.js";
import { s as setEnvironmentState } from "../../chunks/environment.svelte.js";
import "clsx";
function ThemeProvider($$payload, $$props) {
  push();
  pop();
}
function _layout($$payload, $$props) {
  push();
  var $$store_subs;
  let { children, data } = $$props;
  setEnvironmentState(data.environment);
  ThemeProvider($$payload, { themeCSS: data.themeCSS });
  $$payload.out += `<!----> `;
  if (store_get($$store_subs ??= {}, "$navigating", navigating)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="fixed w-full top-0 right-0 left-0 h-1 z-50 bg-primary"></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  children($$payload);
  $$payload.out += `<!---->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _layout as default
};
