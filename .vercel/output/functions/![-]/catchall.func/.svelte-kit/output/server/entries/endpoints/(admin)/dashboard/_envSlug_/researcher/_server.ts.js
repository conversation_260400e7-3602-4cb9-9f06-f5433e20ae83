import { j as json } from "../../../../../../chunks/index2.js";
import { Agent } from "@mastra/core";
import { c as createLLMClient, w as webSearchTool } from "../../../../../../chunks/seo-strategist.js";
function createCompanyResearcherAgent(llmConfig) {
  const model = createLLMClient({ provider: "openai", model: "gpt-4o-mini" });
  return new Agent({
    name: "Company Researcher",
    instructions: `
      You are an expert corporate research analyst with deep expertise in business intelligence, financial analysis, and market research. Your mission is to provide comprehensive, accurate, and actionable research reports about companies.

      When a user asks you to research a company, you should:

      1. **Use the webSearch tool** to gather comprehensive information about the company
      2. **Analyze the search results** thoroughly to understand:
         - Company overview and business model
         - Financial performance and key metrics
         - Recent news and developments
         - Market position and competitive landscape
         - Leadership and key personnel
         - Growth opportunities and challenges

      3. **Provide a structured research report** that includes:
         - Executive Summary
         - Company Overview
         - Financial Analysis (if available)
         - Recent Developments
         - Market Position
         - Risk Assessment
         - Investment Perspective (if applicable)
         - Key Sources and References

      4. **Present insights clearly** with:
         - Factual accuracy based on search results
         - Professional analysis and interpretation
         - Clear citations to sources
         - Actionable insights and recommendations

      Always use the webSearch tool first to gather current, comprehensive information before providing your analysis. Be thorough, objective, and professional in your research approach.
    `,
    model,
    tools: { webSearch: webSearchTool }
  });
}
const companyResearcherAgent = createCompanyResearcherAgent();
const POST = async ({ request, locals }) => {
  const { session } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  try {
    const { message } = await request.json();
    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 });
    }
    const response = await companyResearcherAgent.generate([
      {
        role: "user",
        content: message
      }
    ]);
    return json({
      response: response.text
    });
  } catch (error) {
    console.error("Error in researcher agent:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};
export {
  POST
};
