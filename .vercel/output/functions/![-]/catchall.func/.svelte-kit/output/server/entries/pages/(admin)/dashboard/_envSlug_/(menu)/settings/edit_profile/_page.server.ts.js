import "clsx";
import "../../../../../../../../chunks/client.js";
import "../../../../../../../../chunks/formData.js";
import "../../../../../../../../chunks/superForm.js";
import "../../../../../../../../chunks/index2.js";
import { s as superValidate } from "../../../../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../../../../chunks/zod.js";
import { p as profileSchema } from "../../../../../../../../chunks/schemas.js";
const load = async ({ parent }) => {
  const { profile } = await parent();
  const form = await superValidate(
    {
      full_name: profile?.full_name ?? "",
      company_name: profile?.company_name ?? "",
      website: profile?.website ?? ""
    },
    zod(profileSchema),
    { errors: false }
  );
  return { form };
};
export {
  load
};
