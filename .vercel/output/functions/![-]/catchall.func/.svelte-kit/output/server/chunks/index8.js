import { B as setContext, a7 as hasContext, O as getContext, A as push, F as store_get, N as slot, G as unsubscribe_stores, Q as bind_props, E as pop, P as fallback, K as sanitize_props, R as rest_props, S as spread_attributes, V as ensure_array_like, J as escape_html, M as spread_props, $ as invalid_default_snippet, a4 as clsx, X as attr_class } from "./index.js";
import "clsx";
import { c as cn } from "./utils.js";
import { L as Label } from "./input.js";
import { w as writable } from "./index3.js";
import { n as nanoid } from "./index7.js";
import "./create.js";
import "./index5.js";
const FORM_FIELD = Symbol("FORM_FIELD_CTX");
function setFormField(props) {
  setContext(FORM_FIELD, props);
  return props;
}
function getFormField() {
  if (!hasContext(FORM_FIELD)) {
    ctxError("Form.Field");
  }
  return getContext(FORM_FIELD);
}
const FORM_CONTROL = Symbol("FORM_CONTROL_CTX");
function setFormControl(props) {
  setContext(FORM_CONTROL, props);
  return props;
}
function getFormControl() {
  if (!hasContext(FORM_CONTROL)) {
    ctxError("<Control />");
  }
  return getContext(FORM_CONTROL);
}
function ctxError(ctx) {
  throw new Error(`Unable to find \`${ctx}\` context. Did you forget to wrap the component in a \`${ctx}\`?`);
}
function getAriaDescribedBy({ fieldErrorsId = void 0, descriptionId = void 0, errors }) {
  let describedBy = "";
  if (descriptionId) {
    describedBy += descriptionId + " ";
  }
  if (errors.length && fieldErrorsId) {
    describedBy += fieldErrorsId;
  }
  return describedBy ? describedBy.trim() : void 0;
}
function getAriaRequired(constraints) {
  if (!("required" in constraints))
    return void 0;
  return constraints.required ? "true" : void 0;
}
function getAriaInvalid(errors) {
  return errors && errors.length ? "true" : void 0;
}
function getDataFsError(errors) {
  return errors && errors.length ? "" : void 0;
}
function generateId() {
  return nanoid(5);
}
function extractErrorArray(errors) {
  if (Array.isArray(errors))
    return errors;
  if (typeof errors === "object" && "_errors" in errors) {
    if (errors._errors !== void 0)
      return errors._errors;
  }
  return [];
}
function getValueAtPath(path, obj) {
  const keys = path.split(/[[\].]/).filter(Boolean);
  let value = obj;
  for (const key of keys) {
    if (typeof value !== "object" || value === null) {
      return void 0;
    }
    value = value[key];
  }
  return value;
}
function Field($$payload, $$props) {
  push();
  var $$store_subs;
  let formErrors, formConstraints, formTainted, formData;
  let form = $$props["form"];
  let name = $$props["name"];
  const field = {
    name: writable(name),
    errors: writable([]),
    constraints: writable({}),
    tainted: writable(false),
    fieldErrorsId: writable(),
    descriptionId: writable(),
    form
  };
  const { tainted, errors } = field;
  setFormField(field);
  ({
    errors: formErrors,
    constraints: formConstraints,
    tainted: formTainted,
    form: formData
  } = form);
  field.name.set(name);
  field.errors.set(extractErrorArray(getValueAtPath(name, store_get($$store_subs ??= {}, "$formErrors", formErrors))));
  field.constraints.set(getValueAtPath(name, store_get($$store_subs ??= {}, "$formConstraints", formConstraints)) ?? {});
  field.tainted.set(store_get($$store_subs ??= {}, "$formTainted", formTainted) ? getValueAtPath(name, store_get($$store_subs ??= {}, "$formTainted", formTainted)) === true : false);
  $$payload.out += `<!---->`;
  slot(
    $$payload,
    $$props,
    "default",
    {
      value: store_get($$store_subs ??= {}, "$formData", formData)[name],
      errors: store_get($$store_subs ??= {}, "$errors", errors),
      tainted: store_get($$store_subs ??= {}, "$tainted", tainted),
      constraints: store_get($$store_subs ??= {}, "$formConstraints", formConstraints)[name]
    },
    null
  );
  $$payload.out += `<!---->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { form, name });
  pop();
}
function Control$1($$payload, $$props) {
  push();
  var $$store_subs;
  let errorAttr, attrs, labelAttrs;
  let id = fallback($$props["id"], generateId, true);
  const {
    name,
    fieldErrorsId,
    descriptionId,
    errors,
    constraints
  } = getFormField();
  const controlContext = {
    id: writable(id),
    attrs: writable(),
    labelAttrs: writable()
  };
  const { id: idStore } = controlContext;
  setFormControl(controlContext);
  controlContext.id.set(id);
  errorAttr = getDataFsError(store_get($$store_subs ??= {}, "$errors", errors));
  attrs = {
    name: store_get($$store_subs ??= {}, "$name", name),
    id: store_get($$store_subs ??= {}, "$idStore", idStore),
    "data-fs-error": errorAttr,
    "aria-describedby": getAriaDescribedBy({
      fieldErrorsId: store_get($$store_subs ??= {}, "$fieldErrorsId", fieldErrorsId),
      descriptionId: store_get($$store_subs ??= {}, "$descriptionId", descriptionId),
      errors: store_get($$store_subs ??= {}, "$errors", errors)
    }),
    "aria-invalid": getAriaInvalid(store_get($$store_subs ??= {}, "$errors", errors)),
    "aria-required": getAriaRequired(store_get($$store_subs ??= {}, "$constraints", constraints)),
    "data-fs-control": ""
  };
  labelAttrs = {
    for: store_get($$store_subs ??= {}, "$idStore", idStore),
    "data-fs-label": "",
    "data-fs-error": errorAttr
  };
  controlContext.attrs.set(attrs);
  controlContext.labelAttrs.set(labelAttrs);
  $$payload.out += `<!---->`;
  slot($$payload, $$props, "default", { attrs }, null);
  $$payload.out += `<!---->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { id });
  pop();
}
function Field_errors($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["id", "asChild", "el"]);
  push();
  var $$store_subs;
  let errorAttr, fieldErrorsAttrs, errorAttrs;
  const { fieldErrorsId, errors } = getFormField();
  let id = fallback($$props["id"], generateId, true);
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  errorAttr = getDataFsError(store_get($$store_subs ??= {}, "$errors", errors));
  fieldErrorsId.set(id);
  fieldErrorsAttrs = {
    id: store_get($$store_subs ??= {}, "$fieldErrorsId", fieldErrorsId),
    "data-fs-error": errorAttr,
    "data-fs-field-errors": "",
    "aria-live": "assertive",
    ...$$restProps
  };
  errorAttrs = {
    "data-fs-field-error": "",
    "data-fs-error": errorAttr
  };
  if (asChild) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot(
      $$payload,
      $$props,
      "default",
      {
        errors: store_get($$store_subs ??= {}, "$errors", errors),
        fieldErrorsAttrs,
        errorAttrs
      },
      null
    );
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...fieldErrorsAttrs }, null)}><!---->`;
    slot(
      $$payload,
      $$props,
      "default",
      {
        errors: store_get($$store_subs ??= {}, "$errors", errors),
        fieldErrorsAttrs,
        errorAttrs
      },
      () => {
        const each_array = ensure_array_like(store_get($$store_subs ??= {}, "$errors", errors));
        $$payload.out += `<!--[-->`;
        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
          let error = each_array[$$index];
          $$payload.out += `<div${spread_attributes({ ...errorAttrs }, null)}>${escape_html(error)}</div>`;
        }
        $$payload.out += `<!--]-->`;
      }
    );
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { id, asChild, el });
  pop();
}
function Form_label($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class"]);
  push();
  var $$store_subs;
  let className = fallback($$props["class"], void 0);
  const { labelAttrs } = getFormControl();
  Label($$payload, spread_props([
    store_get($$store_subs ??= {}, "$labelAttrs", labelAttrs),
    {
      class: cn("data-[fs-error]:text-destructive", className)
    },
    $$restProps,
    {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", { labelAttrs }, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { class: className });
  pop();
}
function Form_field_errors($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "errorClasses"]);
  push();
  let className = fallback($$props["class"], void 0);
  let errorClasses = fallback($$props["errorClasses"], void 0);
  Field_errors($$payload, spread_props([
    {
      class: cn("text-destructive text-sm font-medium", className)
    },
    $$restProps,
    {
      children: invalid_default_snippet,
      $$slots: {
        default: ($$payload2, { errors, fieldErrorsAttrs, errorAttrs }) => {
          $$payload2.out += `<!---->`;
          slot($$payload2, $$props, "default", { errors, fieldErrorsAttrs, errorAttrs }, () => {
            const each_array = ensure_array_like(errors);
            $$payload2.out += `<!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let error = each_array[$$index];
              $$payload2.out += `<div${spread_attributes(
                {
                  ...errorAttrs,
                  class: clsx(cn(errorClasses))
                },
                null
              )}>${escape_html(error)}</div>`;
            }
            $$payload2.out += `<!--]-->`;
          });
          $$payload2.out += `<!---->`;
        }
      }
    }
  ]));
  bind_props($$props, { class: className, errorClasses });
  pop();
}
function Form_field($$payload, $$props) {
  push();
  let form = $$props["form"];
  let name = $$props["name"];
  let className = fallback($$props["class"], void 0);
  Field($$payload, {
    form,
    name,
    children: invalid_default_snippet,
    $$slots: {
      default: ($$payload2, { constraints, errors, tainted, value }) => {
        $$payload2.out += `<div${attr_class(clsx(cn("space-y-2", className)))}><!---->`;
        slot($$payload2, $$props, "default", { constraints, errors, tainted, value }, null);
        $$payload2.out += `<!----></div>`;
      }
    }
  });
  bind_props($$props, { form, name, class: className });
  pop();
}
const Control = Control$1;
export {
  Control as C,
  Form_field as F,
  Form_label as a,
  Form_field_errors as b
};
