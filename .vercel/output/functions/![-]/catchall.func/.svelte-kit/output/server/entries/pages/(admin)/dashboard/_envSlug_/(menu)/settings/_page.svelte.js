import { I as head, Z as stringify, X as attr_class, a4 as clsx, E as pop, A as push } from "../../../../../../../chunks/index.js";
import { S as Settings_module } from "../../../../../../../chunks/settings_module.js";
import { g as getEnvironmentState } from "../../../../../../../chunks/environment.svelte.js";
import { b as buttonVariants } from "../../../../../../../chunks/index5.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let { profile, auth } = data;
  const environment = getEnvironmentState();
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Settings</title>`;
  });
  $$payload.out += `<h1 class="text-2xl font-bold mb-6">Settings</h1> `;
  if (!auth.user?.is_anonymous) {
    $$payload.out += "<!--[-->";
    Settings_module($$payload, {
      title: "Profile",
      editable: false,
      fields: [
        {
          id: "fullName",
          label: "Name",
          initialValue: profile?.full_name ?? ""
        },
        {
          id: "companyName",
          label: "Company Name",
          initialValue: profile?.company_name ?? ""
        },
        {
          id: "website",
          label: "Company Website",
          initialValue: profile?.website ?? ""
        }
      ],
      editButtonTitle: "Edit Profile",
      editLink: `/dashboard/${stringify(environment.value?.slug)}/settings/edit_profile`
    });
    $$payload.out += `<!----> `;
    Settings_module($$payload, {
      title: "Email",
      editable: false,
      fields: [
        {
          id: "email",
          initialValue: auth.user?.email || ""
        }
      ],
      editButtonTitle: "Change Email",
      editLink: `/dashboard/${stringify(environment.value?.slug)}/settings/change_email`
    });
    $$payload.out += `<!----> `;
    Settings_module($$payload, {
      title: "Password",
      editable: false,
      fields: [
        {
          id: "password",
          initialValue: "••••••••••••••••"
        }
      ],
      editButtonTitle: "Change Password",
      editLink: `/dashboard/${stringify(environment.value?.slug)}/settings/change_password`
    });
    $$payload.out += `<!----> `;
    Settings_module($$payload, {
      title: "Email Subscription",
      editable: false,
      fields: [
        {
          id: "subscriptionStatus",
          initialValue: profile?.unsubscribed ? "Unsubscribed" : "Subscribed"
        }
      ],
      editButtonTitle: "Change Subscription",
      editLink: `/dashboard/${stringify(environment.value?.slug)}/settings/change_email_subscription`
    });
    $$payload.out += `<!----> `;
    Settings_module($$payload, {
      title: "Billing",
      editable: false,
      fields: [],
      editButtonTitle: "Manage Billing",
      editLink: `/dashboard/${stringify(environment.value?.slug)}/billing`
    });
    $$payload.out += `<!----> `;
    Settings_module($$payload, {
      title: "Danger Zone",
      editable: false,
      dangerous: true,
      fields: [],
      editButtonTitle: "Delete Account",
      editLink: `/dashboard/${stringify(environment.value?.slug)}/settings/delete_account`
    });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p>You need to <a href="/login/sign_up"${attr_class(clsx(buttonVariants({ size: "sm" })))}>sign up</a> to update your settings</p>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
export {
  _page as default
};
