import { I as head, E as pop, A as push, J as escape_html, X as attr_class, Z as stringify } from "../../../../../../../../chunks/index.js";
import "../../../../../../../../chunks/client.js";
import { S as Settings_module } from "../../../../../../../../chunks/settings_module.js";
import { C as Card, a as Card_content } from "../../../../../../../../chunks/card-content.js";
import "clsx";
import "../../../../../../../../chunks/index6.js";
import { b as buttonVariants } from "../../../../../../../../chunks/index5.js";
import { c as changePasswordSchema } from "../../../../../../../../chunks/schemas.js";
import { g as getEnvironmentState } from "../../../../../../../../chunks/environment.svelte.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let { session, supabase } = data;
  getEnvironmentState();
  let hasPassword = Boolean(session?.user.user_metadata.hasPassword);
  let usingOAuth = ["google", "github"].includes(session?.user?.app_metadata?.provider ?? "") ? true : false;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Change Password</title>`;
  });
  $$payload.out += `<h1 class="text-2xl font-bold mb-6">Change Password</h1> `;
  if (hasPassword) {
    $$payload.out += "<!--[-->";
    Settings_module($$payload, {
      data: data.form,
      schema: changePasswordSchema,
      title: "Change Password",
      editable: true,
      saveButtonTitle: "Change Password",
      successTitle: "Password Changed",
      successBody: "On next sign in, use your new password.",
      formTarget: "/api?/updatePassword",
      fields: [
        {
          id: "newPassword1",
          label: "New Password",
          initialValue: "",
          inputType: "password"
        },
        {
          id: "newPassword2",
          label: "Confirm New Password",
          initialValue: "",
          inputType: "password"
        },
        {
          id: "currentPassword",
          label: "Current Password",
          initialValue: "",
          inputType: "password"
        }
      ]
    });
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<!---->`;
    Card($$payload, {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Card_content($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<div class="flex flex-col gap-y-4">`;
            if (usingOAuth) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="font-bold">Set Password By Email</div> <div>You use oAuth to sign in ("Sign in with Github" or similar). You can
            continue to access your account using only oAuth if you like!</div>`;
            } else {
              $$payload3.out += "<!--[!-->";
              $$payload3.out += `<div class="font-bold">Change Password By Email</div>`;
            }
            $$payload3.out += `<!--]--> <div>The button below will send you an email at ${escape_html(session?.user?.email)} which
          will allow you to set your password.</div> <button${attr_class(`${stringify(buttonVariants({ variant: "outline" }))} ${stringify("")}`)}>Send Set Password Email</button> `;
            {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
export {
  _page as default
};
