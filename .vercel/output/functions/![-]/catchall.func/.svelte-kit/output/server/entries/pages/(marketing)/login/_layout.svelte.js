import { X as attr_class, E as pop, A as push, Z as stringify } from "../../../../chunks/index.js";
function _layout($$payload, $$props) {
  push();
  let { children } = $$props;
  let isEurope = false;
  try {
    isEurope = Intl.DateTimeFormat().resolvedOptions().timeZone.startsWith("Europe/");
  } catch (e) {
  }
  $$payload.out += `<div class="content-center max-w-lg mx-auto min-h-[70vh] pb-12 flex items-center place-content-center"><div class="flex flex-col w-64 lg:w-80"><div class="card-brutal p-8 mb-8">`;
  children($$payload);
  $$payload.out += `<!----></div> <div${attr_class(`mt-8 ${stringify(isEurope ? "block" : "hidden")} text-center`)}><span class="text-sm font-bold text-muted-foreground">🍪 Logging in uses Cookies 🍪</span></div></div></div>`;
  pop();
}
export {
  _layout as default
};
