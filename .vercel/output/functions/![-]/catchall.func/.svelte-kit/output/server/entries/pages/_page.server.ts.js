import matter from "gray-matter";
import { marked } from "marked";
const __vite_glob_2_0 = `---
name: "Competitive Researcher"
order: 2
videoPlaceholder: "competitive-research-demo.mp4"
---

Custom agent to do deep competitive analysis on real-time data. Continuously monitors your competitors' strategies, pricing, content, and market positioning to give you actionable insights and strategic advantages.`;
const __vite_glob_2_1 = '---\nname: "Custom Content Agent"\norder: 1\nvideoPlaceholder: "content-agent-demo.mp4"\n---\n\nGenerate SEO optimized content in your brand voice. This agent analyzes your existing content, understands your unique voice and tone, then creates high-performing content that ranks while staying true to your brand identity.';
const __vite_glob_2_2 = `---
name: "Social Media Agent"
order: 3
videoPlaceholder: "social-media-demo.mp4"
---

Listen to your brand and your competitors' signals, assess sentiment and act upon them. This agent monitors social conversations, tracks brand mentions, analyzes sentiment trends, and provides real-time insights to optimize your social media strategy.`;
const __vite_glob_2_3 = `---
company: "Protecto"
industry: "Data Guardrails for Enterprise AI Agents"
logo: "M"
featured: true
order: 1
results:
  - metric: "300%"
    label: "Pipeline Growth"
  - metric: "60%"
    label: "Shorter Sales Cycle"
author: "Amar Kanagaraj"
role: "CEO"
---

A brilliant AI privacy startup helping prevent data leaks, privacy violations, and compliance risks in AI automation couldn't break through in a crowded market. We repositioned them around "Data Guardrails" and built a comprehensive GTM strategy.

> "Working with <PERSON>ynn was like having a strategic co-founder who understood both our technology and our market better than we did."
`;
const __vite_glob_2_4 = '---\ncompany: "Apptware"\nindustry: "Design first AI Services"\nlogo: "N"\nfeatured: true\norder: 2\nresults:\n  - metric: "$8M"\n    label: "US Pipeline"\n  - metric: "2X"\n    label: "Goal Exceeded"\nauthor: "Harish Rohokale"\nrole: "CEO"\n---\n\nA successful AI services company was struggling to gain traction in the US market. We rebuilt their market entry strategy from the ground up, created campaigns and helped them achieve a 2X pipeline growth.\n\n> "They transformed our US market entry from a costly experiment into our fastest-growing revenue stream."\n';
const __vite_glob_2_5 = '---\nfeatures:\n  - icon: "Users"\n    title: "Embedded Team"\n    description: "Your marketing co-founder and your agents when and where you need them"\n  - icon: "Zap"\n    title: "AI-Native Approach"\n    description: "Built for efficiency, powered by intelligence. Automation for scaling up."\n  - icon: "TrendingUp"\n    title: "Iteration by Continuous Learning"\n    description: "End-to-end campaigns tried and tested to generate pipelines and revenue"\nstrategicBox:\n  title: "Strategic Leadership, Not Just Services"\n  description: "You get the strategic thinking of a seasoned CMO, the execution power of a full marketing team, and the efficiency of AI-native tools. All without the politics, overhead, or six-figure salaries."\n  checklistItems:\n    - "Comprehensive GTM strategy development"\n    - "Product-led growth optimization"\n    - "AI-powered campaign automation"\n    - "Real-time performance analytics"\n---\n\nOur unique approach to marketing leadership that sets us apart from traditional agencies.\n';
const __vite_glob_2_6 = '---\nmetrics:\n  - metric: "3x"\n    label: "Pipeline Growth"\n    icon: "TrendingUp"\n    color: "primary"\n  - metric: "60%"\n    label: "CAC Reduction"\n    icon: "Target"\n    color: "accent"\n  - metric: "45%"\n    label: "Faster Sales Cycles"\n    icon: "Zap"\n    color: "secondary"\n---\n\nKey performance metrics that demonstrate the impact of our marketing leadership approach.';
const __vite_glob_2_7 = `---
problems:
  - icon: "Users"
    title: "Expensive Team Building"
    description: "VP of Marketing + team costs $500K+ annually"
    color: "destructive"
  - icon: "BarChart3"
    title: "Scattered Marketing"
    description: "Tactics without strategy, campaigns without cohesion"
    color: "primary"
  - icon: "Target"
    title: "Unclear ROI"
    description: "Spending money without knowing what's working"
    color: "secondary"
---

Common problems faced by growing companies when scaling their marketing efforts.`;
const __vite_glob_2_8 = '---\nservices:\n  - icon: "Users"\n    title: "Custom Marketing Agents"\n    description: "Build and run your custom marketing agents centered around your product, your audience and your business requirements."\n    color: "primary"\n  - icon: "Zap"\n    title: "AI Campaign Orchestration"\n    description: "Build and setup an entire AI based campaign orchestration machine for your business."\n    color: "accent"\n  - icon: "BarChart3"\n    title: "Complete Marketing Tech Stack"\n    description: "Build, setup, and run your entire marketing tech stack from website to AI-powered automation, data pipelines and analytics dashboard."\n    color: "secondary"\n---\n\nOur comprehensive service offerings that transform your marketing operations from strategy to execution.';
const __vite_glob_2_9 = `---
title: "Think different about"
titleHighlight: "marketing in the world of AI"
subtitle: "While others offer fractional hours, we offer strategic embedded team members and custom agents for your team. Our CMO-led team doesn't just provide stragey, we build your entire GTM machine from the tech stacl to a GTM campaign system built for your needs."
---

This section explains our unique approach to marketing leadership.
`;
const __vite_glob_2_10 = `---
title: "Your next chapter starts with a"
titleHighlight: "conversation"
subtitle: "We don't believe in hard sells or high-pressure tactics. We believe in finding the right fit. If you're ready to transform your marketing from a cost center into a growth engine, let's talk."
ctaPrimary: "Schedule Your Discovery Call"
footer: "Ready to grow smarter, not just faster?"
---

This section encourages visitors to start a conversation with our team.`;
const __vite_glob_2_11 = '---\nbadge: "CMO, marketers, and AI. All in one."\ntitle: "Your unfair advantage. "\ntitleHighlight: "In a fractional package"\nctaPrimary: "Start Your Growth Story"\ntrustBadge: "Trusted by 50+ scaling startups"\ntrustCount: 50\n---\n\nGet a 10x CMO team—plus expert marketers and AI agents—custom-built to run campaigns at scale. No bloated org chart. No overpriced agency. Just results.\n';
const __vite_glob_2_12 = '---\ntitle: "Marketing Agents"\nsubtitle: "Our AI Agents That Scale Your Marketing"\ndescription: "Purpose-built AI agents that work 24/7 to optimize your marketing efforts across content, competition, and social media."\n---\n\nMeet your new marketing team. Each agent is specifically designed to handle complex marketing tasks that typically require hours of manual work, delivering results in minutes with unprecedented accuracy and insight.';
const __vite_glob_2_13 = `---
title: "You've built something"
titleHighlight: "remarkable"
subtitle: "You're past the early stage hustle. Your product works. Your customers love it. You've hit that magical 5M+ revenue milestone. But now you're facing a new challenge."
---

This section describes the problems faced by growing companies that need marketing leadership.`;
const __vite_glob_2_14 = '---\ntitle: "Growth you can"\ntitleHighlight: "measure"\ntitleSuffix: ". Impact you can feel."\nsubtitle: "Our clients typically see 40% improvement in marketing efficiency within 90 days, 60% reduction in customer acquisition costs within 6 months, and 3x pipeline growth within the first year."\n---\n\nThis section showcases the measurable results we deliver for our clients.';
const __vite_glob_2_15 = `---
title: "Our"
titleHighlight: "Services"
subtitle: "We don't just advise—we build, deploy, and run the complete marketing infrastructure your growing business needs to scale efficiently."
---

Comprehensive marketing solutions designed for modern businesses that need to scale rapidly without the overhead of building everything in-house.`;
const __vite_glob_2_16 = `---
title: "Real companies."
titleHighlight: "Real results"
titleSuffix: "."
subtitle: "See how we've helped AI and technology companies transform their go-to-market strategy and achieve remarkable growth."
---

This section features case studies and success stories from our clients.`;
const __vite_glob_2_17 = `---
title: "The"
titleHighlight: "Robynn Team"
subtitle: "Meet the strategic minds behind your growth. We're not just marketers—we're growth architects with deep expertise in AI, technology, and scaling businesses."
---

This section introduces our team of marketing and growth experts.`;
const __vite_glob_2_18 = '---\nname: "Matt Tanner"\nrole: "SEO and Content 10Xer"\norder: 3\nexpertise: ["AI Implementation", "SEO", "Data-driven Content Strategy"]\n---\n\nSoftware engineer turned SEO expert. Analyze and optimize content for AI-powered search engines. Helped large and small companies scale their organic traffic by orders of magnitude.\n';
const __vite_glob_2_19 = '---\nname: "Joel Horwitz"\nrole: "The AI Demand Gen Maestro"\norder: 2\nexpertise: ["Marketing Automation", "Demand Generation", "Growth Analytics"]\n---\n\nFormer Head of Growth at Series B AI company. Built marketing automation systems that scaled 10x without proportional team growth. Expert in AI Demand Gen Orchestration.\n';
const __vite_glob_2_20 = '---\nname: "Madhukar Kumar"\nrole: "CEO, Co-Founder"\norder: 1\nexpertise: ["Go-to-Market Strategy", "Product-Led Growth", "AI Marketing"]\n---\n\nFormer CMO at two unicorn startups. Two decades plus years scaling B2B SaaS companies from $5M to $100M+ ARR. Expert in product-led growth and AI-native marketing strategies including building compelling and memorable brands.\n';
marked.setOptions({
  breaks: true,
  gfm: true
});
async function loadMarkdownContent(path) {
  try {
    const modules = /* @__PURE__ */ Object.assign({
      "/src/content/agents/competitive-researcher.md": __vite_glob_2_0,
      "/src/content/agents/custom-content-agent.md": __vite_glob_2_1,
      "/src/content/agents/social-media-agent.md": __vite_glob_2_2,
      "/src/content/case-studies/medai-solutions.md": __vite_glob_2_3,
      "/src/content/case-studies/nexus-ai-services.md": __vite_glob_2_4,
      "/src/content/features/approach.md": __vite_glob_2_5,
      "/src/content/features/metrics.md": __vite_glob_2_6,
      "/src/content/features/problems.md": __vite_glob_2_7,
      "/src/content/features/services.md": __vite_glob_2_8,
      "/src/content/home/<USER>": __vite_glob_2_9,
      "/src/content/home/<USER>": __vite_glob_2_10,
      "/src/content/home/<USER>": __vite_glob_2_11,
      "/src/content/home/<USER>": __vite_glob_2_12,
      "/src/content/home/<USER>": __vite_glob_2_13,
      "/src/content/home/<USER>": __vite_glob_2_14,
      "/src/content/home/<USER>": __vite_glob_2_15,
      "/src/content/home/<USER>": __vite_glob_2_16,
      "/src/content/home/<USER>": __vite_glob_2_17,
      "/src/content/team/emily-watson.md": __vite_glob_2_18,
      "/src/content/team/marcus-rodriguez.md": __vite_glob_2_19,
      "/src/content/team/sarah-chen.md": __vite_glob_2_20
    });
    const fullPath = `/src/content/${path}.md`;
    const rawContent = modules[fullPath];
    if (!rawContent) {
      return {
        success: false,
        error: `Content not found: ${path}`
      };
    }
    const { data: frontmatter, content: rawMarkdown } = matter(rawContent);
    const htmlContent = await marked(rawMarkdown);
    return {
      success: true,
      data: {
        frontmatter,
        content: htmlContent,
        slug: path.split("/").pop()
      }
    };
  } catch (error) {
    return {
      success: false,
      error: `Error loading content: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
}
async function loadContentCollection(directory) {
  try {
    const modules = /* @__PURE__ */ Object.assign({
      "/src/content/agents/competitive-researcher.md": __vite_glob_2_0,
      "/src/content/agents/custom-content-agent.md": __vite_glob_2_1,
      "/src/content/agents/social-media-agent.md": __vite_glob_2_2,
      "/src/content/case-studies/medai-solutions.md": __vite_glob_2_3,
      "/src/content/case-studies/nexus-ai-services.md": __vite_glob_2_4,
      "/src/content/features/approach.md": __vite_glob_2_5,
      "/src/content/features/metrics.md": __vite_glob_2_6,
      "/src/content/features/problems.md": __vite_glob_2_7,
      "/src/content/features/services.md": __vite_glob_2_8,
      "/src/content/home/<USER>": __vite_glob_2_9,
      "/src/content/home/<USER>": __vite_glob_2_10,
      "/src/content/home/<USER>": __vite_glob_2_11,
      "/src/content/home/<USER>": __vite_glob_2_12,
      "/src/content/home/<USER>": __vite_glob_2_13,
      "/src/content/home/<USER>": __vite_glob_2_14,
      "/src/content/home/<USER>": __vite_glob_2_15,
      "/src/content/home/<USER>": __vite_glob_2_16,
      "/src/content/home/<USER>": __vite_glob_2_17,
      "/src/content/team/emily-watson.md": __vite_glob_2_18,
      "/src/content/team/marcus-rodriguez.md": __vite_glob_2_19,
      "/src/content/team/sarah-chen.md": __vite_glob_2_20
    });
    const items = [];
    for (const [path, rawContent] of Object.entries(modules)) {
      if (path.includes(`/src/content/${directory}/`)) {
        const { data: frontmatter, content: rawMarkdown } = matter(rawContent);
        const htmlContent = await marked(rawMarkdown);
        const slug = path.split("/").pop()?.replace(".md", "");
        items.push({
          frontmatter,
          content: htmlContent,
          slug
        });
      }
    }
    const sortedItems = items.sort((a, b) => {
      const orderA = a.frontmatter.order || 0;
      const orderB = b.frontmatter.order || 0;
      if (orderA !== orderB) {
        return orderA - orderB;
      }
      return (a.slug || "").localeCompare(b.slug || "");
    });
    return {
      success: true,
      data: sortedItems
    };
  } catch (error) {
    return {
      success: false,
      error: `Error loading content collection: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
}
const contentCache = /* @__PURE__ */ new Map();
function getCachedContent(key) {
  return contentCache.get(key) || null;
}
function setCachedContent(key, content) {
  contentCache.set(key, content);
}
const CONTENT_PATHS = {
  TEAM: "team",
  CASE_STUDIES: "case-studies",
  AGENTS: "agents"
};
class HomeContentService {
  static instance;
  static getInstance() {
    if (!this.instance) {
      this.instance = new HomeContentService();
    }
    return this.instance;
  }
  /**
   * Load hero section content
   */
  async getHeroContent() {
    const cacheKey = "home-hero";
    const cached = getCachedContent(cacheKey);
    if (cached) return cached;
    const result = await loadMarkdownContent("home/hero");
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    console.error("Failed to load hero content:", result.error);
    return null;
  }
  /**
   * Load section content by name
   */
  async getSectionContent(section) {
    const cacheKey = `home-${section}`;
    const cached = getCachedContent(cacheKey);
    if (cached) return cached;
    const result = await loadMarkdownContent(`home/${section}`);
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    console.error(`Failed to load ${section} content:`, result.error);
    return null;
  }
  /**
   * Load all team members
   */
  async getTeamMembers() {
    const cacheKey = "team-members";
    const cached = getCachedContent(cacheKey);
    if (cached) return cached;
    const result = await loadContentCollection(CONTENT_PATHS.TEAM);
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    console.error("Failed to load team members:", result.error);
    return [];
  }
  /**
   * Load case studies
   */
  async getCaseStudies() {
    const cacheKey = "case-studies";
    const cached = getCachedContent(cacheKey);
    if (cached) return cached;
    const result = await loadContentCollection(CONTENT_PATHS.CASE_STUDIES);
    if (result.success && result.data) {
      const featured = result.data.filter((cs) => cs.frontmatter.featured);
      setCachedContent(cacheKey, featured);
      return featured;
    }
    console.error("Failed to load case studies:", result.error);
    return [];
  }
  /**
   * Load feature content (problems, approach, metrics)
   */
  async getFeatureContent(type) {
    const cacheKey = `features-${type}`;
    const cached = getCachedContent(cacheKey);
    if (cached) return cached;
    const result = await loadMarkdownContent(`features/${type}`);
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    console.error(`Failed to load ${type} features:`, result.error);
    return null;
  }
  /**
   * Load marketing agents
   */
  async getMarketingAgents() {
    const cacheKey = "marketing-agents";
    const cached = getCachedContent(cacheKey);
    if (cached) return cached;
    const result = await loadContentCollection(CONTENT_PATHS.AGENTS);
    if (result.success && result.data) {
      const sorted = result.data.sort((a, b) => a.frontmatter.order - b.frontmatter.order);
      setCachedContent(cacheKey, sorted);
      return sorted;
    }
    console.error("Failed to load marketing agents:", result.error);
    return [];
  }
  /**
   * Load all home page content at once
   */
  async getAllHomeContent() {
    const [
      heroContent,
      problemContent,
      approachContent,
      servicesContent,
      marketingAgentsContent,
      resultsContent,
      storiesContent,
      teamContent,
      ctaContent,
      teamMembers,
      caseStudies,
      marketingAgents,
      problemsFeatures,
      approachFeatures,
      servicesFeatures,
      metricsFeatures
    ] = await Promise.all([
      this.getHeroContent(),
      this.getSectionContent("problem"),
      this.getSectionContent("approach"),
      this.getSectionContent("services"),
      this.getSectionContent("marketing-agents"),
      this.getSectionContent("results"),
      this.getSectionContent("stories"),
      this.getSectionContent("team"),
      this.getSectionContent("cta"),
      this.getTeamMembers(),
      this.getCaseStudies(),
      this.getMarketingAgents(),
      this.getFeatureContent("problems"),
      this.getFeatureContent("approach"),
      this.getFeatureContent("services"),
      this.getFeatureContent("metrics")
    ]);
    return {
      hero: heroContent,
      sections: {
        problem: problemContent,
        approach: approachContent,
        services: servicesContent,
        marketingAgents: marketingAgentsContent,
        results: resultsContent,
        stories: storiesContent,
        team: teamContent,
        cta: ctaContent
      },
      teamMembers,
      caseStudies,
      marketingAgents,
      features: {
        problems: problemsFeatures,
        approach: approachFeatures,
        services: servicesFeatures,
        metrics: metricsFeatures
      }
    };
  }
}
function getContentService() {
  return HomeContentService.getInstance();
}
const load = async ({ parent }) => {
  const parentData = await parent();
  const contentService = getContentService();
  try {
    const homeContent = await contentService.getAllHomeContent();
    return {
      ...parentData,
      homeContent,
      // Add metadata for SEO
      meta: {
        title: homeContent.hero?.frontmatter.title || "Robynn AI - Marketing Agents for Startups",
        description: homeContent.hero?.content || "Strategic marketing leadership that scales with your ambitions.",
        lastModified: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    console.error("Error loading home page content:", error);
    return {
      ...parentData,
      homeContent: {
        hero: null,
        sections: {
          problem: null,
          approach: null,
          services: null,
          marketingAgents: null,
          results: null,
          stories: null,
          team: null,
          cta: null
        },
        teamMembers: [],
        caseStudies: [],
        marketingAgents: [],
        features: {
          problems: null,
          approach: null,
          services: null,
          metrics: null
        }
      },
      meta: {
        title: "Robynn AI - Marketing Agents for Startups",
        description: "Strategic marketing leadership that scales with your ambitions.",
        lastModified: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  }
};
export {
  load
};
