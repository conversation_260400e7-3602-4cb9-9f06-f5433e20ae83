import "clsx";
import { K as sanitize_props, M as spread_props, N as slot, T as copy_payload, U as assign_payload, E as pop, A as push } from "../../../../chunks/index.js";
import { s as superForm } from "../../../../chunks/superForm.js";
import "../../../../chunks/formData.js";
import "../../../../chunks/index2.js";
import { z as zod } from "../../../../chunks/zod.js";
import { b as environmentSchema } from "../../../../chunks/schemas.js";
import "../../../../chunks/create.js";
import "../../../../chunks/index5.js";
import "../../../../chunks/client.js";
import { g as getEnvironmentState } from "../../../../chunks/environment.svelte.js";
import { I as Icon } from "../../../../chunks/Icon.js";
function Loader_circle($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      { "d": "M21 12a9 9 0 1 1-6.219-8.56" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "loader-circle" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function _page($$payload, $$props) {
  push();
  let { data, form: actionForm } = $$props;
  getEnvironmentState();
  const form = superForm(data.form, { validators: zod(environmentSchema) });
  const { form: formData, enhance, errors, delayed } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="max-w-xl mx-auto mt-8">`;
    {
      $$payload2.out += "<!--[!-->";
      Loader_circle($$payload2, { class: "animate-spin" });
    }
    $$payload2.out += `<!--]--></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
