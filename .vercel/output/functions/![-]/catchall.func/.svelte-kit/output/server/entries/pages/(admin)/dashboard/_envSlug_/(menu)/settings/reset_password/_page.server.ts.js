import "clsx";
import "../../../../../../../../chunks/client.js";
import "../../../../../../../../chunks/formData.js";
import "../../../../../../../../chunks/superForm.js";
import "../../../../../../../../chunks/index2.js";
import { s as superValidate } from "../../../../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../../../../chunks/zod.js";
import { c as changePasswordSchema } from "../../../../../../../../chunks/schemas.js";
const load = async () => {
  const form = await superValidate(zod(changePasswordSchema));
  return { form };
};
export {
  load
};
