import { f as fail } from "../../../../chunks/index2.js";
import "clsx";
import "../../../../chunks/client.js";
import "../../../../chunks/formData.js";
import "../../../../chunks/superForm.js";
import { s as superValidate, a as setError } from "../../../../chunks/superValidate.js";
import { z as zod } from "../../../../chunks/zod.js";
import { s as sendAdminEmail } from "../../../../chunks/mailer.js";
import { a as contactSchema } from "../../../../chunks/schemas.js";
const load = async () => {
  const form = await superValidate(zod(contactSchema));
  return { form };
};
const actions = {
  submitContactUs: async ({ request, locals: { supabaseServiceRole } }) => {
    const form = await superValidate(request, zod(contactSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const { error: insertError } = await supabaseServiceRole.from("contact_requests").insert(form.data);
    if (insertError) {
      console.warn({ insertError });
      return setError(form, "Something went wrong", { status: 500 });
    }
    await sendAdminEmail({
      subject: "New contact request",
      body: `New contact request from ${form.data.first_name} ${form.data.last_name}.

Email: ${form.data.email}

Phone: ${form.data.phone}

Company: ${form.data.company_name}

Message: ${form.data.message_body}`
    });
    return { form };
  }
};
export {
  actions,
  load
};
