import "clsx";
import "../../../../../../../../chunks/client.js";
import "../../../../../../../../chunks/formData.js";
import "../../../../../../../../chunks/superForm.js";
import "../../../../../../../../chunks/index2.js";
import { s as superValidate } from "../../../../../../../../chunks/superValidate.js";
import { z as zod } from "../../../../../../../../chunks/zod.js";
import { e as emailSchema } from "../../../../../../../../chunks/schemas.js";
const load = async ({ locals: { auth } }) => {
  const form = await superValidate(
    { email: auth.user?.email },
    zod(emailSchema)
  );
  return { form };
};
export {
  load
};
