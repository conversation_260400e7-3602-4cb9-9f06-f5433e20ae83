import { I as head, X as attr_class, a4 as clsx, E as pop, A as push } from "../../../../chunks/index.js";
import { b as buttonVariants } from "../../../../chunks/index5.js";
function _page($$payload, $$props) {
  push();
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Log In</title>`;
  });
  $$payload.out += `<div><h1 class="text-xl font-bold mb-2">Get Started</h1> <a href="/login/sign_up"${attr_class(clsx(buttonVariants({ size: "lg" })))}>Sign Up</a> <h1 class="text-xl mt-6 mb-2">Already have an account?</h1> <a href="/login/sign_in"${attr_class(clsx(buttonVariants({ size: "lg", variant: "outline" })))}>Sign In</a></div>`;
  pop();
}
export {
  _page as default
};
