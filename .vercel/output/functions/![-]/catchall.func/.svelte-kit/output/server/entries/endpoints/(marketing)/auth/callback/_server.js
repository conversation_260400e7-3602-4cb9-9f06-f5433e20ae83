import { r as redirect } from "../../../../../chunks/index2.js";
import { isAuthApiError } from "@supabase/supabase-js";
const GET = async ({ url, locals: { supabase } }) => {
  const code = url.searchParams.get("code");
  const error_description = url.searchParams.get("error_description");
  if (error_description) {
    console.error("Auth callback error:", error_description);
    redirect(303, "/login/sign_in?error=auth_failed");
  }
  if (code) {
    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      if (error) {
        console.error("Session exchange error:", error);
        redirect(303, "/login/sign_in?error=session_failed");
      }
      if (data.session && data.user) {
        if (data.user.email_confirmed_at) {
          redirect(303, "/login/sign_in?verified=true");
        }
      }
    } catch (error) {
      console.error("Auth callback error:", error);
      if (isAuthApiError(error)) {
        redirect(303, "/login/sign_in?verified=true");
      } else {
        redirect(303, "/login/sign_in?error=unexpected");
      }
    }
  }
  const next = url.searchParams.get("next");
  if (next) {
    redirect(303, next);
  }
  redirect(303, "/find-env");
};
export {
  GET
};
