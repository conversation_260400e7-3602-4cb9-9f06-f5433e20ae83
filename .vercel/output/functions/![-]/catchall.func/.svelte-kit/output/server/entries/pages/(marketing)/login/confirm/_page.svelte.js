import { T as copy_payload, U as assign_payload, E as pop, A as push, I as head, Y as attr, F as store_get, $ as invalid_default_snippet, M as spread_props, a6 as store_mutate, J as escape_html, G as unsubscribe_stores } from "../../../../../chunks/index.js";
import { F as Form_field, C as Control, a as Form_label, b as Form_field_errors } from "../../../../../chunks/index8.js";
import { C as Card, a as Card_content } from "../../../../../chunks/card-content.js";
import { C as Card_header, a as Card_title, b as Card_description } from "../../../../../chunks/card-title.js";
import "clsx";
import "../../../../../chunks/client.js";
import "../../../../../chunks/formData.js";
import { s as superForm } from "../../../../../chunks/superForm.js";
import "../../../../../chunks/index2.js";
import { a as zodClient } from "../../../../../chunks/zod.js";
import { o as otpCodeSchema } from "../../../../../chunks/schemas.js";
import { I as Input } from "../../../../../chunks/input.js";
import "../../../../../chunks/index5.js";
import { B as Button } from "../../../../../chunks/button.js";
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  const form = superForm(data.form, { validators: zodClient(otpCodeSchema) });
  const {
    form: formData,
    enhance,
    delayed,
    errors,
    constraints
  } = form;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    head($$payload2, ($$payload3) => {
      $$payload3.title = `<title>Confirmation</title>`;
    });
    $$payload2.out += `<!---->`;
    Card($$payload2, {
      class: "mt-6",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              class: "text-2xl font-bold text-center",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Enter your verification code`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                if (data.type === "signup") {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `A 6-digit confirmation code has been sent to your email. Please verify
        your email to complete your account setup.`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  $$payload5.out += `A 6-digit confirmation code has been sent to your email to confirm your
        email change.`;
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<form method="post" class="grid gap-4"><input name="email"${attr("value", store_get($$store_subs ??= {}, "$formData", formData).email)} hidden/> <!---->`;
            Form_field($$payload4, {
              form,
              name: "code",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Control($$payload5, {
                  children: invalid_default_snippet,
                  $$slots: {
                    default: ($$payload6, { attrs }) => {
                      $$payload6.out += `<!---->`;
                      Form_label($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->6-digit code`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Input($$payload6, spread_props([
                        attrs,
                        store_get($$store_subs ??= {}, "$constraints", constraints).code,
                        {
                          get value() {
                            return store_get($$store_subs ??= {}, "$formData", formData).code;
                          },
                          set value($$value) {
                            store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).code = $$value);
                            $$settled = false;
                          }
                        }
                      ]));
                      $$payload6.out += `<!---->`;
                    }
                  }
                });
                $$payload5.out += `<!----> <!---->`;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors)._errors) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-destructive text-sm font-bold mt-1">${escape_html(store_get($$store_subs ??= {}, "$errors", errors)._errors[0])}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            Button($$payload4, {
              type: "submit",
              disabled: store_get($$store_subs ??= {}, "$delayed", delayed),
              class: "w-full",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$delayed", delayed)) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `...`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  $$payload5.out += `Verify`;
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
