"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Field = void 0;
const types_1 = require("@smithy/types");
class Field {
    constructor({ name, kind = types_1.FieldPosition.HEADER, values = [] }) {
        this.name = name;
        this.kind = kind;
        this.values = values;
    }
    add(value) {
        this.values.push(value);
    }
    set(values) {
        this.values = values;
    }
    remove(value) {
        this.values = this.values.filter((v) => v !== value);
    }
    toString() {
        return this.values.map((v) => (v.includes(",") || v.includes(" ") ? `"${v}"` : v)).join(", ");
    }
    get() {
        return this.values;
    }
}
exports.Field = Field;
