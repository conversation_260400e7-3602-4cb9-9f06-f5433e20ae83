{"version": 3, "routes": [{"src": "/_app/immutable/.+", "headers": {"cache-control": "public, immutable, max-age=********"}}, {"handle": "filesystem"}, {"src": "^/?(?:/__data.json)?$", "dest": "/"}, {"src": "^/account/?(?:/__data.json)?$", "dest": "/(marketing)/account"}, {"src": "^/account/api/?(?:/__data.json)?$", "dest": "/(marketing)/account/api"}, {"src": "^/account/billing/?(?:/__data.json)?$", "dest": "/(marketing)/account/billing"}, {"src": "^/account/billing/manage/?(?:/__data.json)?$", "dest": "/(marketing)/account/billing/manage"}, {"src": "^/account/select_plan/?(?:/__data.json)?$", "dest": "/(marketing)/account/select_plan"}, {"src": "^/account/sign_out/?(?:/__data.json)?$", "dest": "/(marketing)/account/sign_out"}, {"src": "^/account/subscribe/([^/]+?)/?(?:/__data.json)?$", "dest": "/(marketing)/account/subscribe/[slug]"}, {"src": "^/api/?(?:/__data.json)?$", "dest": "/(admin)/api"}, {"src": "^/api/contact/?(?:/__data.json)?$", "dest": "/api/contact"}, {"src": "^/api/demo/researcher/?(?:/__data.json)?$", "dest": "/api/demo/researcher"}, {"src": "^/api/demo/seo/?(?:/__data.json)?$", "dest": "/api/demo/seo"}, {"src": "^/auth/callback/?(?:/__data.json)?$", "dest": "/(marketing)/auth/callback"}, {"src": "^/blog/rss\\.xml/?(?:/__data.json)?$", "dest": "/(marketing)/blog/rss.xml"}, {"src": "^/contact_us/?(?:/__data.json)?$", "dest": "/(marketing)/contact_us"}, {"src": "^/dashboard/([^/]+?)/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)"}, {"src": "^/dashboard/([^/]+?)/agent-seo/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/agent-seo"}, {"src": "^/dashboard/([^/]+?)/billing/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/billing"}, {"src": "^/dashboard/([^/]+?)/billing/manage/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/billing/manage"}, {"src": "^/dashboard/([^/]+?)/create_profile/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/create_profile"}, {"src": "^/dashboard/([^/]+?)/researcher/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/researcher"}, {"src": "^/dashboard/([^/]+?)/select_plan/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/select_plan"}, {"src": "^/dashboard/([^/]+?)/settings/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings"}, {"src": "^/dashboard/([^/]+?)/settings/change_email_subscription/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email_subscription"}, {"src": "^/dashboard/([^/]+?)/settings/change_email/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email"}, {"src": "^/dashboard/([^/]+?)/settings/change_password/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings/change_password"}, {"src": "^/dashboard/([^/]+?)/settings/delete_account/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings/delete_account"}, {"src": "^/dashboard/([^/]+?)/settings/edit_profile/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings/edit_profile"}, {"src": "^/dashboard/([^/]+?)/settings/reset_password/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/(menu)/settings/reset_password"}, {"src": "^/dashboard/([^/]+?)/subscribe/([^/]+?)/?(?:/__data.json)?$", "dest": "/(admin)/dashboard/[envSlug]/subscribe/[slug]"}, {"src": "^/find-env/?(?:/__data.json)?$", "dest": "/(marketing)/find-env"}, {"src": "^/login/?(?:/__data.json)?$", "dest": "/(marketing)/login"}, {"src": "^/login/check_email/?(?:/__data.json)?$", "dest": "/(marketing)/login/check_email"}, {"src": "^/login/confirm/?(?:/__data.json)?$", "dest": "/(marketing)/login/confirm"}, {"src": "^/login/current_password_error/?(?:/__data.json)?$", "dest": "/(marketing)/login/current_password_error"}, {"src": "^/login/forgot_password/?(?:/__data.json)?$", "dest": "/(marketing)/login/forgot_password"}, {"src": "^/login/sign_in/?(?:/__data.json)?$", "dest": "/(marketing)/login/sign_in"}, {"src": "^/login/sign_up/?(?:/__data.json)?$", "dest": "/(marketing)/login/sign_up"}, {"src": "^/onboarding/?(?:/__data.json)?$", "dest": "/(marketing)/onboarding"}, {"src": "^/search/api\\.json/?(?:/__data.json)?$", "dest": "/(marketing)/search/api.json"}, {"src": "^/sign_out/?(?:/__data.json)?$", "dest": "/(admin)/sign_out"}, {"src": "/.*", "dest": "/![-]/catchall"}], "overrides": {}}