import"../chunks/CWj6FrbW.js";import{o as n}from"../chunks/CfBaWyh2.js";import{p as m,f as p,t as f,a as g,g as u,k as l,J as h,c,r as d,j as x}from"../chunks/wnqW1tdD.js";import{s as v}from"../chunks/CDPCzm7q.js";import{i as _,g as b}from"../chunks/COZ5WnQL.js";var w=p('<h1 class="text-2xl font-bold m-6"> </h1>');function O(e,a){m(a,!0);let{supabase:o}=a.data,s=h("Signing out....");n(()=>{o.auth.signOut().then(async({error:r})=>{r?l(s,"There was an issue signing out."):(await _("data:init"),b("/"))})});var t=w(),i=c(t,!0);d(t),f(()=>v(i,x(s))),g(e,t),u()}export{O as component};
