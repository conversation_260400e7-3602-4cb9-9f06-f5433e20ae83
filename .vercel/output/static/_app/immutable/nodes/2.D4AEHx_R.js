import{c as o,e as s}from"../chunks/BH_5by7C.js";import{L as m}from"../chunks/BYyWxyJW.js";const u=async({data:e,depends:a})=>{a("supabase:auth");const t=o(s.PUBLIC_SUPABASE_URL,s.PUBLIC_SUPABASE_ANON_KEY),{data:{session:r}}=await t.auth.getSession(),n=e.profile;return{...e,supabase:t,session:r,profile:n}},l=e=>!(!e||!e.full_name||!e.company_name||!e.website),f=Object.freeze(Object.defineProperty({__proto__:null,_hasFullProfile:l,load:u},Symbol.toStringTag,{value:"Module"}));export{m as component,f as universal};
