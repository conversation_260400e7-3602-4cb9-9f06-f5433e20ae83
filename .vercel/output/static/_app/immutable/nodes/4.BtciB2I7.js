var kt=Object.defineProperty;var Ot=(f,e,O)=>e in f?kt(f,e,{enumerable:!0,configurable:!0,writable:!0,value:O}):f[e]=O;var Ke=(f,e,O)=>Ot(f,typeof e!="symbol"?e+"":e,O);import"../chunks/CWj6FrbW.js";import{b as de,e as $,a as d,b6 as Tt,ar as Et,as as St,p as Ce,l as Z,aM as ce,h as ze,g as we,k as ge,m as Me,j as t,f as H,c as F,r as A,Y as s,d as vt,i as gt,s as oe,n as Pt,u as Le,J as Ue,a7 as Dt,t as Oe,v as Ze}from"../chunks/wnqW1tdD.js";import{e as l,d as At,s as We}from"../chunks/CDPCzm7q.js";import{s as Ft}from"../chunks/yk44OJLy.js";import{i as q}from"../chunks/BjRbZGyQ.js";import{e as it,i as st}from"../chunks/CsnEE4l9.js";import{c as $e}from"../chunks/ojdN50pv.js";import{a as ue,c as jt,s as pe}from"../chunks/rh_XW2Tv.js";import{s as je}from"../chunks/Bz0_kaay.js";import{s as xe,a as Ae}from"../chunks/D5ITLM2v.js";import{f as Nt,a as zt}from"../chunks/CiB29Aqe.js";import"../chunks/uGb05Syq.js";import"../chunks/Cvx8ZW61.js";import{o as It,w as Rt,m as Se,e as et,a as Ye,k as qe,f as Mt,u as lt,n as Pe,s as dt,g as Ne,p as Vt,i as ct,h as Ht}from"../chunks/DQ0GGkgp.js";import{s as te}from"../chunks/BDqVm3Gq.js";import{i as Te}from"../chunks/BxG_UISn.js";import{l as ae,s as Ve,p as _}from"../chunks/Cmdkv-7M.js";import{w as Bt,d as bt}from"../chunks/BvpDAKCq.js";import{t as ut,g as Kt,o as Lt,r as Ut,a as Wt}from"../chunks/EOfJYBcM.js";import{u as Yt,c as qt,g as Gt,a as Jt,h as ft,r as Xt,S as Qt,M as Zt}from"../chunks/DE9gdxIe.js";import{c as $t,a as ht}from"../chunks/DPaidA8O.js";import{a as fe}from"../chunks/Cet13GU7.js";import{b as ve}from"../chunks/D5U2DSnR.js";import{p as ea}from"../chunks/B6j3ckjV.js";import{g as ta}from"../chunks/DyGaIYLH.js";import{t as ye}from"../chunks/sBNKiCwy.js";import{b as g}from"../chunks/B2uh23P-.js";import{X as aa}from"../chunks/BAo0SQ6-.js";import{c as Ge,f as ra}from"../chunks/BMdVdstb.js";import{C as na}from"../chunks/ZAWXEYb0.js";import{I as mt}from"../chunks/CX_t0Ed_.js";import{S as oa}from"../chunks/BzxySnFN.js";function ia(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=[["path",{d:"m15 18-6-6 6-6"}]];mt(f,Ve({name:"chevron-left"},()=>O,{get iconNode(){return y},children:(p,z)=>{var b=de(),a=$(b);te(a,e,"default",{},null),d(p,b)},$$slots:{default:!0}}))}function sa(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}],["polyline",{points:"16 17 21 12 16 7"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12"}]];mt(f,Ve({name:"log-out"},()=>O,{get iconNode(){return y},children:(p,z)=>{var b=de(),a=$(b);te(a,e,"default",{},null),d(p,b)},$$slots:{default:!0}}))}const{name:De}=Mt("dialog"),la={preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,role:"dialog",defaultOpen:!1,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},da=["content","title","description"];function ca(f){const e={...la,...f},O=ut(It(e,"ids")),{preventScroll:y,closeOnEscape:p,closeOnOutsideClick:z,role:b,portal:a,forceVisible:o,openFocus:S,closeFocus:X,onOutsideClick:I}=O,T=Rt.writable(null),x=ut({...Kt(da),...e.ids}),U=e.open??Bt(e.defaultOpen),C=Lt(U,e==null?void 0:e.onOpenChange),w=bt([C,o],([n,j])=>n||j);let k=Pe;function h(n){const j=n.currentTarget,u=n.currentTarget;!ct(j)||!ct(u)||(C.set(!0),T.set(u))}function c(){C.set(!1),ft({prop:X.get(),defaultEl:T.get()})}const P=Se(De("trigger"),{stores:[C],returned:([n])=>({"aria-haspopup":"dialog","aria-expanded":n,type:"button"}),action:n=>({destroy:et(Ye(n,"click",u=>{h(u)}),Ye(n,"keydown",u=>{u.key!==qe.ENTER&&u.key!==qe.SPACE||(u.preventDefault(),h(u))}))})}),B=Se(De("overlay"),{stores:[w,C],returned:([n,j])=>({hidden:n?void 0:!0,tabindex:-1,style:dt({display:n?void 0:"none"}),"aria-hidden":!0,"data-state":j?"open":"closed"}),action:n=>{let j=Pe;if(p.get()){const u=lt(n,{handler:()=>{c()}});u&&u.destroy&&(j=u.destroy)}return{destroy(){j()}}}}),ee=Se(De("content"),{stores:[w,x.content,x.description,x.title,C],returned:([n,j,u,G,R])=>({id:j,role:b.get(),"aria-describedby":u,"aria-labelledby":G,"aria-modal":n?"true":void 0,"data-state":R?"open":"closed",tabindex:-1,hidden:n?void 0:!0,style:dt({display:n?void 0:"none"})}),action:n=>{let j=Pe,u=Pe;const G=et(Ne([C,z,p],([R,M,W])=>{if(!R)return;const m=qt({immediate:!1,escapeDeactivates:W,clickOutsideDeactivates:M,allowOutsideClick:!0,returnFocusOnDeactivate:!1,fallbackFocus:n});j=m.activate,u=m.deactivate;const Y=m.useFocusTrap(n);return Y&&Y.destroy?Y.destroy:m.deactivate}),Ne([z,C],([R,M])=>Yt(n,{open:M,closeOnInteractOutside:R,onClose(){c()},shouldCloseOnInteractOutside(W){var m;return(m=I.get())==null||m(W),!W.defaultPrevented}}).destroy),Ne([p],([R])=>R?lt(n,{handler:c}).destroy:Pe),Ne([w],([R])=>{Tt().then(()=>{R?j():u()})}));return{destroy:()=>{k(),G()}}}}),re=Se(De("portalled"),{stores:a,returned:n=>({"data-portal":Vt(n)}),action:n=>{const j=Ne([a],([u])=>{if(u===null)return Pe;const G=Gt(n,u);return G===null?Pe:Jt(n,G).destroy});return{destroy(){j()}}}}),Ee=Se(De("title"),{stores:[x.title],returned:([n])=>({id:n})}),be=Se(De("description"),{stores:[x.description],returned:([n])=>({id:n})}),me=Se(De("close"),{returned:()=>({type:"button"}),action:n=>({destroy:et(Ye(n,"click",()=>{c()}),Ye(n,"keydown",u=>{u.key!==qe.SPACE&&u.key!==qe.ENTER||(u.preventDefault(),c())}))})});return Ne([C,y],([n,j])=>{if(Ht){if(j&&n&&(k=Xt()),n){const u=document.getElementById(x.content.get());ft({prop:S.get(),defaultEl:u})}return()=>{o.get()||k()}}}),{ids:x,elements:{content:ee,trigger:P,title:Ee,description:be,overlay:B,close:me,portalled:re},states:{open:C},options:O}}function _t(){return{NAME:"dialog",PARTS:["close","content","description","overlay","portal","title","trigger"]}}function ua(f){const{NAME:e,PARTS:O}=_t(),y=$t(e,O),p={...ca({...Ut(f),role:"dialog",forceVisible:!0}),getAttrs:y};return St(e,p),{...p,updateOption:Wt(p.options)}}function He(){const{NAME:f}=_t();return Et(f)}function fa(f,e){Ce(e,!1);const[O,y]=Ae(),p=()=>xe(k,"$idValues",O);let z=_(e,"preventScroll",24,()=>{}),b=_(e,"closeOnEscape",24,()=>{}),a=_(e,"closeOnOutsideClick",24,()=>{}),o=_(e,"portal",24,()=>{}),S=_(e,"open",28,()=>{}),X=_(e,"onOpenChange",24,()=>{}),I=_(e,"openFocus",24,()=>{}),T=_(e,"closeFocus",24,()=>{}),x=_(e,"onOutsideClick",24,()=>{});const{states:{open:U},updateOption:C,ids:w}=ua({closeOnEscape:b(),preventScroll:z(),closeOnOutsideClick:a(),portal:o(),forceVisible:!0,defaultOpen:S(),openFocus:I(),closeFocus:T(),onOutsideClick:x(),onOpenChange:({next:P})=>{var B;return S()!==P&&((B=X())==null||B(P),S(P)),P}}),k=bt([w.content,w.description,w.title],([P,B,ee])=>({content:P,description:B,title:ee}));Z(()=>ce(S()),()=>{S()!==void 0&&U.set(S())}),Z(()=>ce(z()),()=>{C("preventScroll",z())}),Z(()=>ce(b()),()=>{C("closeOnEscape",b())}),Z(()=>ce(a()),()=>{C("closeOnOutsideClick",a())}),Z(()=>ce(o()),()=>{C("portal",o())}),Z(()=>ce(I()),()=>{C("openFocus",I())}),Z(()=>ce(T()),()=>{C("closeFocus",T())}),Z(()=>ce(x()),()=>{C("onOutsideClick",x())}),ze(),Te();var h=de(),c=$(h);te(c,e,"default",{get ids(){return p()}},null),d(f,h),we(),y()}var va=H("<button><!></button>");function ga(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["asChild","el"]);Ce(e,!1);const[p,z]=Ae(),b=()=>xe(X,"$close",p),a=Me();let o=_(e,"asChild",8,!1),S=_(e,"el",28,()=>{});const{elements:{close:X},getAttrs:I}=He(),T=ht(),x=I("close");Z(()=>b(),()=>{ge(a,b())}),Z(()=>t(a),()=>{Object.assign(t(a),x)}),ze(),Te();var U=de(),C=$(U);{var w=h=>{var c=de(),P=$(c);te(P,e,"default",{get builder(){return t(a)}},null),d(h,c)},k=h=>{var c=va();ue(c,()=>({...t(a),type:"button",...y}));var P=F(c);te(P,e,"default",{get builder(){return t(a)}},null),A(c),ve(c,B=>S(B),()=>S()),fe(c,B=>{var ee,re;return(re=(ee=t(a)).action)==null?void 0:re.call(ee,B)}),s(()=>l("m-click",c,T)),s(()=>l("m-keydown",c,T)),d(h,c)};q(C,h=>{o()?h(w):h(k,!1)})}d(f,U),we(),z()}var ba=H("<div><!></div>");function ha(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["asChild","el"]);Ce(e,!1);const[p,z]=Ae(),b=()=>xe(X,"$portalled",p),a=Me();let o=_(e,"asChild",8,!1),S=_(e,"el",28,()=>{});const{elements:{portalled:X},getAttrs:I}=He(),T=I("portal");Z(()=>b(),()=>{ge(a,b())}),Z(()=>t(a),()=>{Object.assign(t(a),T)}),ze(),Te();var x=de(),U=$(x);{var C=k=>{var h=de(),c=$(h);te(c,e,"default",{get builder(){return t(a)}},null),d(k,h)},w=k=>{var h=ba();ue(h,()=>({...t(a),...y}));var c=F(h);te(c,e,"default",{get builder(){return t(a)}},null),A(h),ve(h,P=>S(P),()=>S()),fe(h,P=>{var B,ee;return(ee=(B=t(a)).action)==null?void 0:ee.call(B,P)}),d(k,h)};q(U,k=>{o()?k(C):k(w,!1)})}d(f,x),we(),z()}var ma=H("<div><!></div>"),_a=H("<div><!></div>"),pa=H("<div><!></div>"),ya=H("<div><!></div>"),xa=H("<div><!></div>");function Ca(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","el"]);Ce(e,!1);const[p,z]=Ae(),b=()=>xe(h,"$content",p),a=()=>xe(c,"$open",p),o=Me();let S=_(e,"transition",24,()=>{}),X=_(e,"transitionConfig",24,()=>{}),I=_(e,"inTransition",24,()=>{}),T=_(e,"inTransitionConfig",24,()=>{}),x=_(e,"outTransition",24,()=>{}),U=_(e,"outTransitionConfig",24,()=>{}),C=_(e,"asChild",8,!1),w=_(e,"id",24,()=>{}),k=_(e,"el",28,()=>{});const{elements:{content:h},states:{open:c},ids:P,getAttrs:B}=He(),ee=B("content");Z(()=>ce(w()),()=>{w()&&P.content.set(w())}),Z(()=>b(),()=>{ge(o,b())}),Z(()=>t(o),()=>{Object.assign(t(o),ee)}),ze(),Te();var re=de(),Ee=$(re);{var be=n=>{var j=de(),u=$(j);te(u,e,"default",{get builder(){return t(o)}},null),d(n,j)},me=(n,j)=>{{var u=R=>{var M=ma();ue(M,()=>({...t(o),...y}));var W=F(M);te(W,e,"default",{get builder(){return t(o)}},null),A(M),ve(M,m=>k(m),()=>k()),fe(M,m=>{var Y,V;return(V=(Y=t(o)).action)==null?void 0:V.call(Y,m)}),s(()=>l("pointerdown",M,function(m){g.call(this,e,m)})),s(()=>l("pointermove",M,function(m){g.call(this,e,m)})),s(()=>l("pointerup",M,function(m){g.call(this,e,m)})),s(()=>l("touchcancel",M,function(m){g.call(this,e,m)})),s(()=>l("touchend",M,function(m){g.call(this,e,m)})),s(()=>l("touchmove",M,function(m){g.call(this,e,m)},void 0,!1)),s(()=>l("touchstart",M,function(m){g.call(this,e,m)},void 0,!1)),ye(3,M,S,X),d(R,M)},G=(R,M)=>{{var W=Y=>{var V=_a();ue(V,()=>({...t(o),...y}));var ne=F(V);te(ne,e,"default",{get builder(){return t(o)}},null),A(V),ve(V,E=>k(E),()=>k()),fe(V,E=>{var J,K;return(K=(J=t(o)).action)==null?void 0:K.call(J,E)}),s(()=>l("pointerdown",V,function(E){g.call(this,e,E)})),s(()=>l("pointermove",V,function(E){g.call(this,e,E)})),s(()=>l("pointerup",V,function(E){g.call(this,e,E)})),s(()=>l("touchcancel",V,function(E){g.call(this,e,E)})),s(()=>l("touchend",V,function(E){g.call(this,e,E)})),s(()=>l("touchmove",V,function(E){g.call(this,e,E)},void 0,!1)),s(()=>l("touchstart",V,function(E){g.call(this,e,E)},void 0,!1)),ye(1,V,I,T),ye(2,V,x,U),d(Y,V)},m=(Y,V)=>{{var ne=J=>{var K=pa();ue(K,()=>({...t(o),...y}));var ie=F(K);te(ie,e,"default",{get builder(){return t(o)}},null),A(K),ve(K,D=>k(D),()=>k()),fe(K,D=>{var Q,r;return(r=(Q=t(o)).action)==null?void 0:r.call(Q,D)}),s(()=>l("pointerdown",K,function(D){g.call(this,e,D)})),s(()=>l("pointermove",K,function(D){g.call(this,e,D)})),s(()=>l("pointerup",K,function(D){g.call(this,e,D)})),s(()=>l("touchcancel",K,function(D){g.call(this,e,D)})),s(()=>l("touchend",K,function(D){g.call(this,e,D)})),s(()=>l("touchmove",K,function(D){g.call(this,e,D)},void 0,!1)),s(()=>l("touchstart",K,function(D){g.call(this,e,D)},void 0,!1)),ye(1,K,I,T),d(J,K)},E=(J,K)=>{{var ie=Q=>{var r=ya();ue(r,()=>({...t(o),...y}));var v=F(r);te(v,e,"default",{get builder(){return t(o)}},null),A(r),ve(r,i=>k(i),()=>k()),fe(r,i=>{var L,se;return(se=(L=t(o)).action)==null?void 0:se.call(L,i)}),s(()=>l("pointerdown",r,function(i){g.call(this,e,i)})),s(()=>l("pointermove",r,function(i){g.call(this,e,i)})),s(()=>l("pointerup",r,function(i){g.call(this,e,i)})),s(()=>l("touchcancel",r,function(i){g.call(this,e,i)})),s(()=>l("touchend",r,function(i){g.call(this,e,i)})),s(()=>l("touchmove",r,function(i){g.call(this,e,i)},void 0,!1)),s(()=>l("touchstart",r,function(i){g.call(this,e,i)},void 0,!1)),ye(2,r,x,U),d(Q,r)},D=(Q,r)=>{{var v=i=>{var L=xa();ue(L,()=>({...t(o),...y}));var se=F(L);te(se,e,"default",{get builder(){return t(o)}},null),A(L),ve(L,N=>k(N),()=>k()),fe(L,N=>{var le,he;return(he=(le=t(o)).action)==null?void 0:he.call(le,N)}),s(()=>l("pointerdown",L,function(N){g.call(this,e,N)})),s(()=>l("pointermove",L,function(N){g.call(this,e,N)})),s(()=>l("pointerup",L,function(N){g.call(this,e,N)})),s(()=>l("touchcancel",L,function(N){g.call(this,e,N)})),s(()=>l("touchend",L,function(N){g.call(this,e,N)})),s(()=>l("touchmove",L,function(N){g.call(this,e,N)},void 0,!1)),s(()=>l("touchstart",L,function(N){g.call(this,e,N)},void 0,!1)),d(i,L)};q(Q,i=>{a()&&i(v)},r)}};q(J,Q=>{x()&&a()?Q(ie):Q(D,!1)},K)}};q(Y,J=>{I()&&a()?J(ne):J(E,!1)},V)}};q(R,Y=>{I()&&x()&&a()?Y(W):Y(m,!1)},M)}};q(n,R=>{S()&&a()?R(u):R(G,!1)},j)}};q(Ee,n=>{C()&&a()?n(be):n(me,!1)})}d(f,re),we(),z()}var wa=H("<div></div>"),ka=H("<div></div>"),Oa=H("<div></div>"),Ta=H("<div></div>"),Ea=H("<div></div>");function Sa(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]);Ce(e,!1);const[p,z]=Ae(),b=()=>xe(k,"$overlay",p),a=()=>xe(h,"$open",p),o=Me();let S=_(e,"transition",24,()=>{}),X=_(e,"transitionConfig",24,()=>{}),I=_(e,"inTransition",24,()=>{}),T=_(e,"inTransitionConfig",24,()=>{}),x=_(e,"outTransition",24,()=>{}),U=_(e,"outTransitionConfig",24,()=>{}),C=_(e,"asChild",8,!1),w=_(e,"el",28,()=>{});const{elements:{overlay:k},states:{open:h},getAttrs:c}=He(),P=c("overlay");Z(()=>b(),()=>{ge(o,b())}),Z(()=>t(o),()=>{Object.assign(t(o),P)}),ze(),Te();var B=de(),ee=$(B);{var re=be=>{var me=de(),n=$(me);te(n,e,"default",{get builder(){return t(o)}},null),d(be,me)},Ee=(be,me)=>{{var n=u=>{var G=wa();ue(G,()=>({...t(o),...y})),s(()=>l("mouseup",G,function(R){g.call(this,e,R)})),ve(G,R=>w(R),()=>w()),fe(G,R=>{var M,W;return(W=(M=t(o)).action)==null?void 0:W.call(M,R)}),ye(3,G,S,X),d(u,G)},j=(u,G)=>{{var R=W=>{var m=ka();ue(m,()=>({...t(o),...y})),ve(m,Y=>w(Y),()=>w()),fe(m,Y=>{var V,ne;return(ne=(V=t(o)).action)==null?void 0:ne.call(V,Y)}),s(()=>l("mouseup",m,function(Y){g.call(this,e,Y)})),ye(1,m,I,T),ye(2,m,x,U),d(W,m)},M=(W,m)=>{{var Y=ne=>{var E=Oa();ue(E,()=>({...t(o),...y})),ve(E,J=>w(J),()=>w()),fe(E,J=>{var K,ie;return(ie=(K=t(o)).action)==null?void 0:ie.call(K,J)}),s(()=>l("mouseup",E,function(J){g.call(this,e,J)})),ye(1,E,I,T),d(ne,E)},V=(ne,E)=>{{var J=ie=>{var D=Ta();ue(D,()=>({...t(o),...y})),ve(D,Q=>w(Q),()=>w()),fe(D,Q=>{var r,v;return(v=(r=t(o)).action)==null?void 0:v.call(r,Q)}),s(()=>l("mouseup",D,function(Q){g.call(this,e,Q)})),ye(2,D,x,U),d(ie,D)},K=(ie,D)=>{{var Q=r=>{var v=Ea();ue(v,()=>({...t(o),...y})),ve(v,i=>w(i),()=>w()),fe(v,i=>{var L,se;return(se=(L=t(o)).action)==null?void 0:se.call(L,i)}),s(()=>l("mouseup",v,function(i){g.call(this,e,i)})),d(r,v)};q(ie,r=>{a()&&r(Q)},D)}};q(ne,ie=>{x()&&a()?ie(J):ie(K,!1)},E)}};q(W,ne=>{I()&&a()?ne(Y):ne(V,!1)},m)}};q(u,W=>{I()&&x()&&a()?W(R):W(M,!1)},G)}};q(be,u=>{S()&&a()?u(n):u(j,!1)},me)}};q(ee,be=>{C()&&a()?be(re):be(Ee,!1)})}d(f,B),we(),z()}var Pa=H("<button><!></button>");function Da(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["asChild","el"]);Ce(e,!1);const[p,z]=Ae(),b=()=>xe(X,"$trigger",p),a=Me();let o=_(e,"asChild",8,!1),S=_(e,"el",28,()=>{});const{elements:{trigger:X},getAttrs:I}=He(),T=ht(),x=I("trigger");Z(()=>b(),()=>{ge(a,b())}),Z(()=>t(a),()=>{Object.assign(t(a),x)}),ze(),Te();var U=de(),C=$(U);{var w=h=>{var c=de(),P=$(c);te(P,e,"default",{get builder(){return t(a)}},null),d(h,c)},k=h=>{var c=Pa();ue(c,()=>({...t(a),type:"button",...y}));var P=F(c);te(P,e,"default",{get builder(){return t(a)}},null),A(c),ve(c,B=>S(B),()=>S()),fe(c,B=>{var ee,re;return(re=(ee=t(a)).action)==null?void 0:re.call(ee,B)}),s(()=>l("m-click",c,T)),s(()=>l("m-keydown",c,T)),d(h,c)};q(C,h=>{o()?h(w):h(k,!1)})}d(f,U),we(),z()}function Aa(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,[]);ha(f,Ve(()=>y,{children:(p,z)=>{var b=de(),a=$(b);te(a,e,"default",{},null),d(p,b)},$$slots:{default:!0}}))}function Fa(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["class","transition","transitionConfig"]);Ce(e,!1);let p=_(e,"class",8,void 0),z=_(e,"transition",8,Nt),b=_(e,"transitionConfig",24,()=>({duration:150}));Te();const a=vt(()=>(ce(Ge),ce(p()),gt(()=>Ge("bg-background/80 fixed inset-0 z-50 backdrop-blur-sm",p()))));Sa(f,Ve({get transition(){return z()},get transitionConfig(){return b()},get class(){return t(a)}},()=>y)),we()}var ja=H('<!> <span class="sr-only">Close</span>',1),Na=H("<!> <!>",1),za=H("<!> <!>",1);function Ia(f,e){const O=ae(e,["children","$$slots","$$events","$$legacy"]),y=ae(O,["class","transition","transitionConfig"]);Ce(e,!1);let p=_(e,"class",8,void 0),z=_(e,"transition",8,ra),b=_(e,"transitionConfig",24,()=>({duration:200}));Te(),Aa(f,{children:(a,o)=>{var S=za(),X=$(S);Fa(X,{});var I=oe(X,2);const T=vt(()=>(ce(Ge),ce(p()),gt(()=>Ge("bg-background fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg sm:rounded-lg md:w-full",p()))));Ca(I,Ve({get transition(){return z()},get transitionConfig(){return b()},get class(){return t(T)}},()=>y,{children:(x,U)=>{var C=Na(),w=$(C);te(w,e,"default",{},null);var k=oe(w,2);ga(k,{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none",children:(h,c)=>{var P=ja(),B=$(P);aa(B,{class:"h-4 w-4"}),Pt(2),d(h,P)},$$slots:{default:!0}}),d(x,C)},$$slots:{default:!0}})),d(a,S)},$$slots:{default:!0}}),we()}const Ra=fa,Ma=Da;var Va=H('<button aria-label="open navigation" class="p-2 text-sidebar-foreground hover:text-sidebar-primary"><!></button>'),Ha=(f,e)=>ge(e,!1),Ba=H("<li><a> </a></li>"),Ka=(f,e)=>ge(e,!1),La=H("<a> </a>"),Ua=(f,e)=>ge(e,!1),Wa=H('<div class="p-4 border-b-2 border-sidebar-border"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a></div></div> <ul class="flex flex-col p-4 space-y-1"><!> <div class="flex-grow"></div> <li class="pt-4 border-t-2 border-sidebar-border"><div class="flex items-center justify-between px-3 py-2"><!> <a class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors">Sign Out</a></div></li></ul>',1),Ya=H("<!> <!>",1),qa=H('<a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a>'),Ga=(f,e)=>ge(e,!t(e)),Ja=H('<span class="block text-center">🏠</span>'),Xa=H("<a><!></a>"),Qa=H("<a><!></a>"),Za=H(`<div class="text-sm bg-primary text-primary-foreground sticky px-4 py-3 text-center border-2 border-border shadow-brutal mb-6 font-bold">You're signed in as an anonymous user. <a href="/login/sign_up" class="underline font-bold hover:opacity-70">Sign Up to persist your changes</a></div>`),$a=H('<div class="grid grid-rows-[auto_1fr] lg:grid-rows-1 overflow-hidden top-0 bottom-0 right-0 left-0 absolute"><nav><div class="flex items-center space-x-2 inline lg:hidden"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-lg font-black">Robynn.ai</a></div> <!> <ul class="hidden flex-col h-full lg:flex"><li class="mb-8"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <!></div> <button class="p-1.5 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors rounded border-2 border-transparent hover:border-sidebar-border"><!></button></div></li> <nav class="space-y-1"></nav> <div class="flex-grow"></div> <div class="border-t-2 border-sidebar-border pt-4"><div><!> <a class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors border-2 border-transparent p-1.5 rounded hover:border-sidebar-border"><!></a></div></div></ul></nav> <div class="px-6 lg:px-12 py-6 overflow-y-scroll relative bg-background min-h-full flex flex-col"><div class="flex-1"><!> <!></div> <div class="mt-auto"><!></div></div></div>');function jr(f,e){Ce(e,!0);const[O,y]=Ae(),p=()=>xe(ea,"$page",O);let{session:z}=e.data,b=Ue(!1),a=Ue(!1);Le(()=>{if(typeof window<"u"){const r=localStorage.getItem("sidebar-collapsed");r!==null&&ge(a,r==="true")}}),Le(()=>{typeof window<"u"&&localStorage.setItem("sidebar-collapsed",String(t(a)))});const o="/dashboard/",S=ta();class X{constructor(v,i,L){Ke(this,"href");Ke(this,"label");Ke(this,"active");this.href=v,this.label=i,this.active=L(this.href)}}let I=Ue(Dt([]));Le(()=>{var r;ge(I,[new X(`${o}${(r=S.value)==null?void 0:r.slug}`,"Home",v=>p().url.pathname===v)],!0)});let T=Ue(void 0);Le(()=>{var r;ge(T,new X(`${o}${(r=S.value)==null?void 0:r.slug}/settings`,"⚙️",v=>p().url.pathname.startsWith(v)),!0)});var x=$a(),U=F(x);let C;var w=oe(F(U),2);$e(w,()=>Ra,(r,v)=>{v(r,{get open(){return t(b)},set open(i){ge(b,i,!0)},children:(i,L)=>{var se=Ya(),N=$(se);$e(N,()=>Ma,(he,Je)=>{Je(he,{class:"lg:hidden",children:(Ie,pt)=>{var Fe=Va(),Be=F(Fe);Zt(Be,{class:"h-5 w-5"}),A(Fe),d(Ie,Fe)},$$slots:{default:!0}})});var le=oe(N,2);$e(le,()=>Ia,(he,Je)=>{Je(he,{transition:Ie=>zt(Ie,{x:300,duration:300}),class:"left-auto right-0 flex h-dvh max-h-screen w-full max-w-sm translate-x-1 flex-col overflow-y-scroll border-y-0 sm:rounded-none bg-sidebar",children:(Ie,pt)=>{var Fe=Wa(),Be=oe($(Fe),2),tt=F(Be);it(tt,17,()=>t(I),st,(ke,_e)=>{let Xe=()=>t(_e).href,xt=()=>t(_e).label,Ct=()=>t(_e).active;var Qe=Ba(),Re=F(Qe);Re.__click=[Ha,b];var wt=F(Re,!0);A(Re),A(Qe),Oe(()=>{pe(Re,"href",Xe()),je(Re,1,`block w-full px-3 py-2 text-sm font-bold transition-colors border-2 ${Ct()?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),We(wt,xt())}),d(ke,Qe)});var at=oe(tt,4),rt=F(at),nt=F(rt);{var yt=ke=>{var _e=La();_e.__click=[Ka,b];var Xe=F(_e,!0);A(_e),Oe(()=>{pe(_e,"href",t(T).href),je(_e,1,`text-sm font-bold transition-colors border-2 p-1 rounded ${t(T).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),We(Xe,t(T).label)}),d(ke,_e)};q(nt,ke=>{t(T)&&ke(yt)})}var ot=oe(nt,2);ot.__click=[Ua,b],A(rt),A(at),A(Be),Oe(()=>{var ke;return pe(ot,"href",`/dashboard/${((ke=S.value)==null?void 0:ke.slug)??""}/../../sign_out`)}),d(Ie,Fe)},$$slots:{default:!0}})}),d(i,se)},$$slots:{default:!0}})});var k=oe(w,2),h=F(k),c=F(h),P=F(c),B=oe(F(P),2);{var ee=r=>{var v=qa();d(r,v)};q(B,r=>{t(a)||r(ee)})}A(P);var re=oe(P,2);re.__click=[Ga,a];var Ee=F(re);{var be=r=>{na(r,{class:"h-4 w-4"})},me=r=>{ia(r,{class:"h-4 w-4"})};q(Ee,r=>{t(a)?r(be):r(me,!1)})}A(re),A(c),A(h);var n=oe(h,2);it(n,21,()=>t(I),st,(r,v)=>{var i=Xa(),L=F(i);{var se=le=>{var he=Ja();d(le,he)},N=le=>{var he=Ze();Oe(()=>We(he,t(v).label)),d(le,he)};q(L,le=>{t(a)?le(se):le(N,!1)})}A(i),Oe(()=>{pe(i,"href",t(v).href),je(i,1,`block px-3 py-2 text-sm font-bold transition-colors border-2 ${t(v).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),pe(i,"title",t(a)?t(v).label:void 0)}),d(r,i)}),A(n);var j=oe(n,4),u=F(j);let G;var R=F(u);{var M=r=>{var v=Qa(),i=F(v);{var L=N=>{oa(N,{class:"h-4 w-4"})},se=N=>{var le=Ze();Oe(()=>We(le,t(T).label)),d(N,le)};q(i,N=>{t(a)?N(L):N(se,!1)})}A(v),Oe(()=>{pe(v,"href",t(T).href),je(v,1,`text-sm font-bold transition-colors border-2 p-1.5 rounded ${t(T).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),pe(v,"title",t(a)?"Settings":void 0)}),d(r,v)};q(R,r=>{t(T)&&r(M)})}var W=oe(R,2),m=F(W);{var Y=r=>{sa(r,{class:"h-4 w-4"})},V=r=>{var v=Ze("Sign Out");d(r,v)};q(m,r=>{t(a)?r(Y):r(V,!1)})}A(W),A(u),A(j),A(k),A(U);var ne=oe(U,2),E=F(ne),J=F(E);{var K=r=>{var v=Za();d(r,v)};q(J,r=>{z!=null&&z.user.is_anonymous&&r(K)})}var ie=oe(J,2);Ft(ie,()=>e.children),A(E);var D=oe(E,2),Q=F(D);Qt(Q,{}),A(D),A(ne),A(x),Oe((r,v)=>{var i;jt(x,`grid-template-columns: ${t(a)?"4.5rem":"18rem"} 1fr`),C=je(U,1,"w-full h-16 flex items-center justify-between lg:block lg:h-dvh p-4 bg-sidebar border-r-2 border-sidebar-border text-sidebar-foreground transition-all duration-300",null,C,r),pe(re,"aria-label",t(a)?"Expand sidebar":"Collapse sidebar"),G=je(u,1,"flex items-center gap-2 px-3 py-2",null,G,v),pe(W,"href",`/dashboard/${((i=S.value)==null?void 0:i.slug)??""}/../../sign_out`),pe(W,"title",t(a)?"Sign Out":void 0)},[()=>({"lg:w-[4.5rem]":t(a),"lg:w-72":!t(a)}),()=>({"justify-center":t(a)})]),d(f,x),we(),y()}At(["click"]);export{jr as component};
