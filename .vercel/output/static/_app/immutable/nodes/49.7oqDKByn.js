import"../chunks/CWj6FrbW.js";import{o as De}from"../chunks/CfBaWyh2.js";import{p as Me,J as Q,a7 as ce,u as _e,f as K,a as P,g as ye,aT as ve,$ as we,s as S,c as j,k as z,j as C,r as O,n as Ie,e as Se,t as $e}from"../chunks/wnqW1tdD.js";import{e as be,h as Le,s as Z}from"../chunks/CDPCzm7q.js";import{i as U}from"../chunks/BjRbZGyQ.js";import{e as ke,i as Re}from"../chunks/CsnEE4l9.js";import{s as oe}from"../chunks/rh_XW2Tv.js";import{s as Oe,a as Ne}from"../chunks/D5ITLM2v.js";import{p as Te}from"../chunks/B6j3ckjV.js";import{g as Pe}from"../chunks/COZ5WnQL.js";import{L as je,I as Ke}from"../chunks/CViLe_-d.js";function $(t){return Array.isArray?Array.isArray(t):Ae(t)==="[object Array]"}function We(t){if(typeof t=="string")return t;let e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function ze(t){return t==null?"":We(t)}function v(t){return typeof t=="string"}function ge(t){return typeof t=="number"}function He(t){return t===!0||t===!1||Ge(t)&&Ae(t)=="[object Boolean]"}function pe(t){return typeof t=="object"}function Ge(t){return pe(t)&&t!==null}function M(t){return t!=null}function q(t){return!t.trim().length}function Ae(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const Ye="Incorrect 'index' type",Qe=t=>`Invalid value for key ${t}`,Ue=t=>`Pattern length exceeds max of ${t}.`,Ve=t=>`Missing ${t} property in key`,Je=t=>`Property 'weight' in key '${t}' must be a positive integer`,ae=Object.prototype.hasOwnProperty;class Xe{constructor(e){this._keys=[],this._keyMap={};let s=0;e.forEach(r=>{let n=me(r);this._keys.push(n),this._keyMap[n.id]=n,s+=n.weight}),this._keys.forEach(r=>{r.weight/=s})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function me(t){let e=null,s=null,r=null,n=1,i=null;if(v(t)||$(t))r=t,e=he(t),s=ee(t);else{if(!ae.call(t,"name"))throw new Error(Ve("name"));const u=t.name;if(r=u,ae.call(t,"weight")&&(n=t.weight,n<=0))throw new Error(Je(u));e=he(u),s=ee(u),i=t.getFn}return{path:e,id:s,weight:n,src:r,getFn:i}}function he(t){return $(t)?t:t.split(".")}function ee(t){return $(t)?t.join("."):t}function Ze(t,e){let s=[],r=!1;const n=(i,u,c)=>{if(M(i))if(!u[c])s.push(i);else{let o=u[c];const a=i[o];if(!M(a))return;if(c===u.length-1&&(v(a)||ge(a)||He(a)))s.push(ze(a));else if($(a)){r=!0;for(let h=0,d=a.length;h<d;h+=1)n(a[h],u,c+1)}else u.length&&n(a,u,c+1)}};return n(t,v(e)?e.split("."):e,0),r?s:s[0]}const qe={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},et={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1},tt={location:0,threshold:.6,distance:100},st={useExtendedSearch:!1,getFn:Ze,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var l={...et,...qe,...tt,...st};const rt=/[^ ]+/g;function nt(t=1,e=3){const s=new Map,r=Math.pow(10,e);return{get(n){const i=n.match(rt).length;if(s.has(i))return s.get(i);const u=1/Math.pow(i,.5*t),c=parseFloat(Math.round(u*r)/r);return s.set(i,c),c},clear(){s.clear()}}}class ue{constructor({getFn:e=l.getFn,fieldNormWeight:s=l.fieldNormWeight}={}){this.norm=nt(s,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((s,r)=>{this._keysMap[s.id]=r})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,v(this.docs[0])?this.docs.forEach((e,s)=>{this._addString(e,s)}):this.docs.forEach((e,s)=>{this._addObject(e,s)}),this.norm.clear())}add(e){const s=this.size();v(e)?this._addString(e,s):this._addObject(e,s)}removeAt(e){this.records.splice(e,1);for(let s=e,r=this.size();s<r;s+=1)this.records[s].i-=1}getValueForItemAtKeyId(e,s){return e[this._keysMap[s]]}size(){return this.records.length}_addString(e,s){if(!M(e)||q(e))return;let r={v:e,i:s,n:this.norm.get(e)};this.records.push(r)}_addObject(e,s){let r={i:s,$:{}};this.keys.forEach((n,i)=>{let u=n.getFn?n.getFn(e):this.getFn(e,n.path);if(M(u)){if($(u)){let c=[];const o=[{nestedArrIndex:-1,value:u}];for(;o.length;){const{nestedArrIndex:a,value:h}=o.pop();if(M(h))if(v(h)&&!q(h)){let d={v:h,i:a,n:this.norm.get(h)};c.push(d)}else $(h)&&h.forEach((d,g)=>{o.push({nestedArrIndex:g,value:d})})}r.$[i]=c}else if(v(u)&&!q(u)){let c={v:u,n:this.norm.get(u)};r.$[i]=c}}}),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function Ee(t,e,{getFn:s=l.getFn,fieldNormWeight:r=l.fieldNormWeight}={}){const n=new ue({getFn:s,fieldNormWeight:r});return n.setKeys(t.map(me)),n.setSources(e),n.create(),n}function it(t,{getFn:e=l.getFn,fieldNormWeight:s=l.fieldNormWeight}={}){const{keys:r,records:n}=t,i=new ue({getFn:e,fieldNormWeight:s});return i.setKeys(r),i.setIndexRecords(n),i}function V(t,{errors:e=0,currentLocation:s=0,expectedLocation:r=0,distance:n=l.distance,ignoreLocation:i=l.ignoreLocation}={}){const u=e/t.length;if(i)return u;const c=Math.abs(r-s);return n?u+c/n:c?1:u}function ut(t=[],e=l.minMatchCharLength){let s=[],r=-1,n=-1,i=0;for(let u=t.length;i<u;i+=1){let c=t[i];c&&r===-1?r=i:!c&&r!==-1&&(n=i-1,n-r+1>=e&&s.push([r,n]),r=-1)}return t[i-1]&&i-r>=e&&s.push([r,i-1]),s}const N=32;function ct(t,e,s,{location:r=l.location,distance:n=l.distance,threshold:i=l.threshold,findAllMatches:u=l.findAllMatches,minMatchCharLength:c=l.minMatchCharLength,includeMatches:o=l.includeMatches,ignoreLocation:a=l.ignoreLocation}={}){if(e.length>N)throw new Error(Ue(N));const h=e.length,d=t.length,g=Math.max(0,Math.min(r,d));let p=i,m=g;const B=c>1||o,F=B?Array(d):[];let _;for(;(_=t.indexOf(e,m))>-1;){let f=V(e,{currentLocation:_,expectedLocation:g,distance:n,ignoreLocation:a});if(p=Math.min(f,p),m=_+h,B){let E=0;for(;E<h;)F[_+E]=1,E+=1}}m=-1;let y=[],w=1,I=h+d;const H=1<<h-1;for(let f=0;f<h;f+=1){let E=0,x=I;for(;E<x;)V(e,{errors:f,currentLocation:g+x,expectedLocation:g,distance:n,ignoreLocation:a})<=p?E=x:I=x,x=Math.floor((I-E)/2+E);I=x;let W=Math.max(1,g-x+1),k=u?d:Math.min(g+x,d)+h,b=Array(k+2);b[k+1]=(1<<f)-1;for(let D=k;D>=W;D-=1){let R=D-1,Y=s[t.charAt(R)];if(B&&(F[R]=+!!Y),b[D]=(b[D+1]<<1|1)&Y,f&&(b[D]|=(y[D+1]|y[D])<<1|1|y[D+1]),b[D]&H&&(w=V(e,{errors:f,currentLocation:R,expectedLocation:g,distance:n,ignoreLocation:a}),w<=p)){if(p=w,m=R,m<=g)break;W=Math.max(1,2*g-m)}}if(V(e,{errors:f+1,currentLocation:g,expectedLocation:g,distance:n,ignoreLocation:a})>p)break;y=b}const A={isMatch:m>=0,score:Math.max(.001,w)};if(B){const f=ut(F,c);f.length?o&&(A.indices=f):A.isMatch=!1}return A}function ot(t){let e={};for(let s=0,r=t.length;s<r;s+=1){const n=t.charAt(s);e[n]=(e[n]||0)|1<<r-s-1}return e}const J=String.prototype.normalize?t=>t.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):t=>t;class Ce{constructor(e,{location:s=l.location,threshold:r=l.threshold,distance:n=l.distance,includeMatches:i=l.includeMatches,findAllMatches:u=l.findAllMatches,minMatchCharLength:c=l.minMatchCharLength,isCaseSensitive:o=l.isCaseSensitive,ignoreDiacritics:a=l.ignoreDiacritics,ignoreLocation:h=l.ignoreLocation}={}){if(this.options={location:s,threshold:r,distance:n,includeMatches:i,findAllMatches:u,minMatchCharLength:c,isCaseSensitive:o,ignoreDiacritics:a,ignoreLocation:h},e=o?e:e.toLowerCase(),e=a?J(e):e,this.pattern=e,this.chunks=[],!this.pattern.length)return;const d=(p,m)=>{this.chunks.push({pattern:p,alphabet:ot(p),startIndex:m})},g=this.pattern.length;if(g>N){let p=0;const m=g%N,B=g-m;for(;p<B;)d(this.pattern.substr(p,N),p),p+=N;if(m){const F=g-N;d(this.pattern.substr(F),F)}}else d(this.pattern,0)}searchIn(e){const{isCaseSensitive:s,ignoreDiacritics:r,includeMatches:n}=this.options;if(e=s?e:e.toLowerCase(),e=r?J(e):e,this.pattern===e){let B={isMatch:!0,score:0};return n&&(B.indices=[[0,e.length-1]]),B}const{location:i,distance:u,threshold:c,findAllMatches:o,minMatchCharLength:a,ignoreLocation:h}=this.options;let d=[],g=0,p=!1;this.chunks.forEach(({pattern:B,alphabet:F,startIndex:_})=>{const{isMatch:y,score:w,indices:I}=ct(e,B,F,{location:i+_,distance:u,threshold:c,findAllMatches:o,minMatchCharLength:a,includeMatches:n,ignoreLocation:h});y&&(p=!0),g+=w,y&&I&&(d=[...d,...I])});let m={isMatch:p,score:p?g/this.chunks.length:1};return p&&n&&(m.indices=d),m}}class L{constructor(e){this.pattern=e}static isMultiMatch(e){return le(e,this.multiRegex)}static isSingleMatch(e){return le(e,this.singleRegex)}search(){}}function le(t,e){const s=t.match(e);return s?s[1]:null}class at extends L{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){const s=e===this.pattern;return{isMatch:s,score:s?0:1,indices:[0,this.pattern.length-1]}}}class ht extends L{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){const r=e.indexOf(this.pattern)===-1;return{isMatch:r,score:r?0:1,indices:[0,e.length-1]}}}class lt extends L{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){const s=e.startsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,this.pattern.length-1]}}}class dt extends L{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){const s=!e.startsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,e.length-1]}}}class ft extends L{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){const s=e.endsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[e.length-this.pattern.length,e.length-1]}}}class gt extends L{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){const s=!e.endsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,e.length-1]}}}class xe extends L{constructor(e,{location:s=l.location,threshold:r=l.threshold,distance:n=l.distance,includeMatches:i=l.includeMatches,findAllMatches:u=l.findAllMatches,minMatchCharLength:c=l.minMatchCharLength,isCaseSensitive:o=l.isCaseSensitive,ignoreDiacritics:a=l.ignoreDiacritics,ignoreLocation:h=l.ignoreLocation}={}){super(e),this._bitapSearch=new Ce(e,{location:s,threshold:r,distance:n,includeMatches:i,findAllMatches:u,minMatchCharLength:c,isCaseSensitive:o,ignoreDiacritics:a,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class Be extends L{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let s=0,r;const n=[],i=this.pattern.length;for(;(r=e.indexOf(this.pattern,s))>-1;)s=r+i,n.push([r,s-1]);const u=!!n.length;return{isMatch:u,score:u?0:1,indices:n}}}const te=[at,Be,lt,dt,gt,ft,ht,xe],de=te.length,pt=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,At="|";function mt(t,e={}){return t.split(At).map(s=>{let r=s.trim().split(pt).filter(i=>i&&!!i.trim()),n=[];for(let i=0,u=r.length;i<u;i+=1){const c=r[i];let o=!1,a=-1;for(;!o&&++a<de;){const h=te[a];let d=h.isMultiMatch(c);d&&(n.push(new h(d,e)),o=!0)}if(!o)for(a=-1;++a<de;){const h=te[a];let d=h.isSingleMatch(c);if(d){n.push(new h(d,e));break}}}return n})}const Et=new Set([xe.type,Be.type]);class Ct{constructor(e,{isCaseSensitive:s=l.isCaseSensitive,ignoreDiacritics:r=l.ignoreDiacritics,includeMatches:n=l.includeMatches,minMatchCharLength:i=l.minMatchCharLength,ignoreLocation:u=l.ignoreLocation,findAllMatches:c=l.findAllMatches,location:o=l.location,threshold:a=l.threshold,distance:h=l.distance}={}){this.query=null,this.options={isCaseSensitive:s,ignoreDiacritics:r,includeMatches:n,minMatchCharLength:i,findAllMatches:c,ignoreLocation:u,location:o,threshold:a,distance:h},e=s?e:e.toLowerCase(),e=r?J(e):e,this.pattern=e,this.query=mt(this.pattern,this.options)}static condition(e,s){return s.useExtendedSearch}searchIn(e){const s=this.query;if(!s)return{isMatch:!1,score:1};const{includeMatches:r,isCaseSensitive:n,ignoreDiacritics:i}=this.options;e=n?e:e.toLowerCase(),e=i?J(e):e;let u=0,c=[],o=0;for(let a=0,h=s.length;a<h;a+=1){const d=s[a];c.length=0,u=0;for(let g=0,p=d.length;g<p;g+=1){const m=d[g],{isMatch:B,indices:F,score:_}=m.search(e);if(B){if(u+=1,o+=_,r){const y=m.constructor.type;Et.has(y)?c=[...c,...F]:c.push(F)}}else{o=0,u=0,c.length=0;break}}if(u){let g={isMatch:!0,score:o/u};return r&&(g.indices=c),g}}return{isMatch:!1,score:1}}}const se=[];function xt(...t){se.push(...t)}function re(t,e){for(let s=0,r=se.length;s<r;s+=1){let n=se[s];if(n.condition(t,e))return new n(t,e)}return new Ce(t,e)}const X={AND:"$and",OR:"$or"},ne={PATH:"$path",PATTERN:"$val"},ie=t=>!!(t[X.AND]||t[X.OR]),Bt=t=>!!t[ne.PATH],Ft=t=>!$(t)&&pe(t)&&!ie(t),fe=t=>({[X.AND]:Object.keys(t).map(e=>({[e]:t[e]}))});function Fe(t,e,{auto:s=!0}={}){const r=n=>{let i=Object.keys(n);const u=Bt(n);if(!u&&i.length>1&&!ie(n))return r(fe(n));if(Ft(n)){const o=u?n[ne.PATH]:i[0],a=u?n[ne.PATTERN]:n[o];if(!v(a))throw new Error(Qe(o));const h={keyId:ee(o),pattern:a};return s&&(h.searcher=re(a,e)),h}let c={children:[],operator:i[0]};return i.forEach(o=>{const a=n[o];$(a)&&a.forEach(h=>{c.children.push(r(h))})}),c};return ie(t)||(t=fe(t)),r(t)}function Dt(t,{ignoreFieldNorm:e=l.ignoreFieldNorm}){t.forEach(s=>{let r=1;s.matches.forEach(({key:n,norm:i,score:u})=>{const c=n?n.weight:null;r*=Math.pow(u===0&&c?Number.EPSILON:u,(c||1)*(e?1:i))}),s.score=r})}function Mt(t,e){const s=t.matches;e.matches=[],M(s)&&s.forEach(r=>{if(!M(r.indices)||!r.indices.length)return;const{indices:n,value:i}=r;let u={indices:n,value:i};r.key&&(u.key=r.key.src),r.idx>-1&&(u.refIndex=r.idx),e.matches.push(u)})}function _t(t,e){e.score=t.score}function yt(t,e,{includeMatches:s=l.includeMatches,includeScore:r=l.includeScore}={}){const n=[];return s&&n.push(Mt),r&&n.push(_t),t.map(i=>{const{idx:u}=i,c={item:e[u],refIndex:u};return n.length&&n.forEach(o=>{o(i,c)}),c})}class T{constructor(e,s={},r){this.options={...l,...s},this.options.useExtendedSearch,this._keyStore=new Xe(this.options.keys),this.setCollection(e,r)}setCollection(e,s){if(this._docs=e,s&&!(s instanceof ue))throw new Error(Ye);this._myIndex=s||Ee(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){M(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const s=[];for(let r=0,n=this._docs.length;r<n;r+=1){const i=this._docs[r];e(i,r)&&(this.removeAt(r),r-=1,n-=1,s.push(i))}return s}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:s=-1}={}){const{includeMatches:r,includeScore:n,shouldSort:i,sortFn:u,ignoreFieldNorm:c}=this.options;let o=v(e)?v(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return Dt(o,{ignoreFieldNorm:c}),i&&o.sort(u),ge(s)&&s>-1&&(o=o.slice(0,s)),yt(o,this._docs,{includeMatches:r,includeScore:n})}_searchStringList(e){const s=re(e,this.options),{records:r}=this._myIndex,n=[];return r.forEach(({v:i,i:u,n:c})=>{if(!M(i))return;const{isMatch:o,score:a,indices:h}=s.searchIn(i);o&&n.push({item:i,idx:u,matches:[{score:a,value:i,norm:c,indices:h}]})}),n}_searchLogical(e){const s=Fe(e,this.options),r=(c,o,a)=>{if(!c.children){const{keyId:d,searcher:g}=c,p=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(o,d),searcher:g});return p&&p.length?[{idx:a,item:o,matches:p}]:[]}const h=[];for(let d=0,g=c.children.length;d<g;d+=1){const p=c.children[d],m=r(p,o,a);if(m.length)h.push(...m);else if(c.operator===X.AND)return[]}return h},n=this._myIndex.records,i={},u=[];return n.forEach(({$:c,i:o})=>{if(M(c)){let a=r(s,c,o);a.length&&(i[o]||(i[o]={idx:o,item:c,matches:[]},u.push(i[o])),a.forEach(({matches:h})=>{i[o].matches.push(...h)}))}}),u}_searchObjectList(e){const s=re(e,this.options),{keys:r,records:n}=this._myIndex,i=[];return n.forEach(({$:u,i:c})=>{if(!M(u))return;let o=[];r.forEach((a,h)=>{o.push(...this._findMatches({key:a,value:u[h],searcher:s}))}),o.length&&i.push({idx:c,item:u,matches:o})}),i}_findMatches({key:e,value:s,searcher:r}){if(!M(s))return[];let n=[];if($(s))s.forEach(({v:i,i:u,n:c})=>{if(!M(i))return;const{isMatch:o,score:a,indices:h}=r.searchIn(i);o&&n.push({score:a,key:e,value:i,idx:u,norm:c,indices:h})});else{const{v:i,n:u}=s,{isMatch:c,score:o,indices:a}=r.searchIn(i);c&&n.push({score:o,key:e,value:i,norm:u,indices:a})}return n}}T.version="7.1.0";T.createIndex=Ee;T.parseIndex=it;T.config=l;T.parseQuery=Fe;xt(Ct);var vt=K('<meta name="description" content="Search our website."/>'),wt=K('<div class="text-center mt-10 text-xl">Loading...</div>'),It=K('<div class="text-center mt-10 text-xl">Error connecting to search. Please try again later.</div>'),St=K('<div class="text-center mt-10 text-xl">No results found</div> <!>',1),$t=K('<a class="card my-6 bg-white shadow-xl flex-row overflow-hidden focus:border"><div class="flex-none w-6 md:w-32 bg-secondary"></div> <div class="py-6 px-6"><div class="text-xl"> </div> <div class="text-sm"> </div> <div class="text-muted-foreground"> </div></div></a>'),bt=K('<div class="py-8 lg:py-12 px-6 max-w-lg mx-auto"><div class="text-3xl lg:text-5xl font-medium text-primary flex gap-3 items-baseline text-center place-content-center"><div class="text-center leading-relaxed font-bold text-primary">Search</div></div> <!> <!> <!> <!> <div></div> <div></div></div>');function Ht(t,e){Me(e,!0);const[s,r]=Ne(),n=()=>Oe(Te,"$page",s),i={keys:[{name:"title",weight:3},{name:"description",weight:2},{name:"body",weight:1}],ignoreLocation:!0,threshold:.3};let u,c=Q(!0),o=Q(!1);De(async()=>{var A;try{const f=await fetch("/search/api.json");if(!f.ok)throw new Error(`HTTP error! status: ${f.status}`);const E=await f.json();if(E&&E.index&&E.indexData){const x=T.parseIndex(E.index);u=new T(E.indexData,i,x)}}catch(f){console.error("Failed to load search data",f),z(o,!0)}finally{z(c,!1),(A=document.getElementById("search-input"))==null||A.focus()}});let a=Q(ce([])),h=Q(ce(decodeURIComponent(n().url.hash.slice(1)??"")));_e(()=>{u&&z(a,u.search(C(h)),!0),window.location.hash.slice(1)!==C(h)&&Pe("#"+C(h),{keepFocus:!0})});let d=0;function g(A){var f,E;A.key==="Escape"?z(h,""):(A.key==="ArrowDown"||A.key==="ArrowUp")&&(d+=A.key==="ArrowDown"?1:-1,d<0?d=0:d>C(a).length&&(d=C(a).length),d===0?(f=document.getElementById("search-input"))==null||f.focus():(E=document.getElementById(`search-result-${d}`))==null||E.focus())}var p=bt();be("keydown",ve,g),Le(A=>{var f=vt();we.title="Search",P(A,f)});var m=S(j(p),2);je(m,{class:"flex items-center gap-2 mt-10",children:(A,f)=>{Ke(A,{id:"search-input",type:"text",class:"grow",placeholder:"Search",onfocus:()=>d=0,"aria-label":"Search input",get value(){return C(h)},set value(E){z(h,E,!0)}})},$$slots:{default:!0}});var B=S(m,2);{var F=A=>{var f=wt();P(A,f)};U(B,A=>{C(c)&&C(h).length>0&&A(F)})}var _=S(B,2);{var y=A=>{var f=It();P(A,f)};U(_,A=>{C(o)&&A(y)})}var w=S(_,2);{var I=A=>{var f=St(),E=S(Se(f),2);U(E,x=>{}),P(A,f)};U(w,A=>{!C(c)&&C(h).length>0&&C(a).length===0&&!C(o)&&A(I)})}var H=S(w,2);ke(H,21,()=>C(a),Re,(A,f,E)=>{var x=$t();oe(x,"id",`search-result-${E+1}`);var W=S(j(x),2),k=j(W),b=j(k,!0);O(k);var G=S(k,2),D=j(G,!0);O(G);var R=S(G,2),Y=j(R,!0);O(R),O(W),O(x),$e(()=>{oe(x,"href",C(f).item.path||"/"),Z(b,C(f).item.title),Z(D,C(f).item.path),Z(Y,C(f).item.description)}),P(A,x)}),O(H),Ie(2),O(p),P(t,p),ye(),r()}export{Ht as component};
