import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as Be,e as _e,a as Fe,b6 as xe,p as je,f as Ke,t as qe,g as Ge,s as de,c as I,d as We,r as C,n as fe}from"./wnqW1tdD.js";import{s as Ue}from"./BDqVm3Gq.js";import{l as Ve,s as Ye}from"./Cmdkv-7M.js";import{I as He}from"./CX_t0Ed_.js";import{n as W,i as Y,b as Xe,c as ie,e as ve,d as R}from"./DQ0GGkgp.js";import{a as be,w as pe}from"./BvpDAKCq.js";import{s as he}from"./CDPCzm7q.js";import{i as ze}from"./BxG_UISn.js";import{W as me}from"./QNe64B_9.js";function Zt(r,e){const t=Ve(e,["children","$$slots","$$events","$$legacy"]),a=[["line",{x1:"4",x2:"20",y1:"12",y2:"12"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18"}]];He(r,Ye({name:"menu"},()=>t,{get iconNode(){return a},children:(o,u)=>{var n=Be(),d=_e(n);Ue(d,e,"default",{},null),Fe(o,n)},$$slots:{default:!0}}))}function Ze(r){return r[r.length-1]}function Jt(r,e){return r.map((t,a)=>r[(e+a)%r.length])}function Pe(r){return new Promise(e=>setTimeout(e,r))}const ke=()=>typeof window<"u";function Je(){const r=navigator.userAgentData;return(r==null?void 0:r.platform)??navigator.platform}const De=r=>ke()&&r.test(Je().toLowerCase()),Qe=()=>ke()&&!!navigator.maxTouchPoints,et=()=>De(/^mac/)&&!Qe(),tt=()=>De(/mac|iphone|ipad|ipod/i),rt=()=>tt()&&!et(),te="data-melt-scroll-lock";function ye(r,e){if(!r)return;const t=r.style.cssText;return Object.assign(r.style,e),()=>{r.style.cssText=t}}function at(r,e,t){if(!r)return;const a=r.style.getPropertyValue(e);return r.style.setProperty(e,t),()=>{a?r.style.setProperty(e,a):r.style.removeProperty(e)}}function nt(r){const e=r.getBoundingClientRect().left;return Math.round(e)+r.scrollLeft?"paddingLeft":"paddingRight"}function Qt(r){const e=document,t=e.defaultView??window,{documentElement:a,body:o}=e;if(o.hasAttribute(te))return W;o.setAttribute(te,"");const n=t.innerWidth-a.clientWidth,d=()=>at(a,"--scrollbar-width",`${n}px`),l=nt(a),f=t.getComputedStyle(o)[l],m=()=>ye(o,{overflow:"hidden",[l]:`calc(${f} + ${n}px)`}),b=()=>{const{scrollX:S,scrollY:y,visualViewport:F}=t,k=(F==null?void 0:F.offsetLeft)??0,x=(F==null?void 0:F.offsetTop)??0,P=ye(o,{position:"fixed",overflow:"hidden",top:`${-(y-Math.floor(x))}px`,left:`${-(S-Math.floor(k))}px`,right:"0",[l]:`calc(${f} + ${n}px)`});return()=>{P==null||P(),t.scrollTo(S,y)}},T=[d(),rt()?b():m()];return()=>{T.forEach(S=>S==null?void 0:S()),o.removeAttribute(te)}}function it(r){let e=r.parentElement;for(;Y(e)&&!e.hasAttribute("data-portal");)e=e.parentElement;return e||"body"}function er(r,e){return e!==void 0?e:it(r)==="body"?document.body:null}async function tr(r){const{prop:e,defaultEl:t}=r;if(await Promise.all([Pe(1),xe]),e===void 0){t==null||t.focus();return}const a=Xe(e)?e(t):e;if(typeof a=="string"){const o=document.querySelector(a);if(!Y(o))return;o.focus()}else Y(a)&&a.focus()}/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var Ae=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],H=Ae.join(","),Ie=typeof Element>"u",M=Ie?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,X=!Ie&&Element.prototype.getRootNode?function(r){var e;return r==null||(e=r.getRootNode)===null||e===void 0?void 0:e.call(r)}:function(r){return r==null?void 0:r.ownerDocument},z=function r(e,t){var a;t===void 0&&(t=!0);var o=e==null||(a=e.getAttribute)===null||a===void 0?void 0:a.call(e,"inert"),u=o===""||o==="true",n=u||t&&e&&r(e.parentNode);return n},ot=function(e){var t,a=e==null||(t=e.getAttribute)===null||t===void 0?void 0:t.call(e,"contenteditable");return a===""||a==="true"},Ce=function(e,t,a){if(z(e))return[];var o=Array.prototype.slice.apply(e.querySelectorAll(H));return t&&M.call(e,H)&&o.unshift(e),o=o.filter(a),o},Oe=function r(e,t,a){for(var o=[],u=Array.from(e);u.length;){var n=u.shift();if(!z(n,!1))if(n.tagName==="SLOT"){var d=n.assignedElements(),l=d.length?d:n.children,f=r(l,!0,a);a.flatten?o.push.apply(o,f):o.push({scopeParent:n,candidates:f})}else{var m=M.call(n,H);m&&a.filter(n)&&(t||!e.includes(n))&&o.push(n);var b=n.shadowRoot||typeof a.getShadowRoot=="function"&&a.getShadowRoot(n),T=!z(b,!1)&&(!a.shadowRootFilter||a.shadowRootFilter(n));if(b&&T){var S=r(b===!0?n.children:b.children,!0,a);a.flatten?o.push.apply(o,S):o.push({scopeParent:n,candidates:S})}else u.unshift.apply(u,n.children)}}return o},Re=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},L=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||ot(e))&&!Re(e)?0:e.tabIndex},st=function(e,t){var a=L(e);return a<0&&t&&!Re(e)?0:a},ut=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},Le=function(e){return e.tagName==="INPUT"},ct=function(e){return Le(e)&&e.type==="hidden"},lt=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(a){return a.tagName==="SUMMARY"});return t},dt=function(e,t){for(var a=0;a<e.length;a++)if(e[a].checked&&e[a].form===t)return e[a]},ft=function(e){if(!e.name)return!0;var t=e.form||X(e),a=function(d){return t.querySelectorAll('input[type="radio"][name="'+d+'"]')},o;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")o=a(window.CSS.escape(e.name));else try{o=a(e.name)}catch(n){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",n.message),!1}var u=dt(o,e.form);return!u||u===e},vt=function(e){return Le(e)&&e.type==="radio"},bt=function(e){return vt(e)&&!ft(e)},pt=function(e){var t,a=e&&X(e),o=(t=a)===null||t===void 0?void 0:t.host,u=!1;if(a&&a!==e){var n,d,l;for(u=!!((n=o)!==null&&n!==void 0&&(d=n.ownerDocument)!==null&&d!==void 0&&d.contains(o)||e!=null&&(l=e.ownerDocument)!==null&&l!==void 0&&l.contains(e));!u&&o;){var f,m,b;a=X(o),o=(f=a)===null||f===void 0?void 0:f.host,u=!!((m=o)!==null&&m!==void 0&&(b=m.ownerDocument)!==null&&b!==void 0&&b.contains(o))}}return u},ge=function(e){var t=e.getBoundingClientRect(),a=t.width,o=t.height;return a===0&&o===0},ht=function(e,t){var a=t.displayCheck,o=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var u=M.call(e,"details>summary:first-of-type"),n=u?e.parentElement:e;if(M.call(n,"details:not([open]) *"))return!0;if(!a||a==="full"||a==="legacy-full"){if(typeof o=="function"){for(var d=e;e;){var l=e.parentElement,f=X(e);if(l&&!l.shadowRoot&&o(l)===!0)return ge(e);e.assignedSlot?e=e.assignedSlot:!l&&f!==e.ownerDocument?e=f.host:e=l}e=d}if(pt(e))return!e.getClientRects().length;if(a!=="legacy-full")return!0}else if(a==="non-zero-area")return ge(e);return!1},mt=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var a=0;a<t.children.length;a++){var o=t.children.item(a);if(o.tagName==="LEGEND")return M.call(t,"fieldset[disabled] *")?!0:!o.contains(e)}return!0}t=t.parentElement}return!1},Z=function(e,t){return!(t.disabled||z(t)||ct(t)||ht(t,e)||lt(t)||mt(t))},ae=function(e,t){return!(bt(t)||L(t)<0||!Z(e,t))},yt=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},gt=function r(e){var t=[],a=[];return e.forEach(function(o,u){var n=!!o.scopeParent,d=n?o.scopeParent:o,l=st(d,n),f=n?r(o.candidates):d;l===0?n?t.push.apply(t,f):t.push(d):a.push({documentOrder:u,tabIndex:l,item:o,isScope:n,content:f})}),a.sort(ut).reduce(function(o,u){return u.isScope?o.push.apply(o,u.content):o.push(u.content),o},[]).concat(t)},wt=function(e,t){t=t||{};var a;return t.getShadowRoot?a=Oe([e],t.includeContainer,{filter:ae.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:yt}):a=Ce(e,t.includeContainer,ae.bind(null,t)),gt(a)},Tt=function(e,t){t=t||{};var a;return t.getShadowRoot?a=Oe([e],t.includeContainer,{filter:Z.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):a=Ce(e,t.includeContainer,Z.bind(null,t)),a},$=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return M.call(e,H)===!1?!1:ae(t,e)},Nt=Ae.concat("iframe").join(","),re=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return M.call(e,Nt)===!1?!1:Z(t,e)};/*!
* focus-trap 7.6.5
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function ne(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,a=Array(e);t<e;t++)a[t]=r[t];return a}function Et(r){if(Array.isArray(r))return ne(r)}function St(r,e,t){return(e=Dt(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Ft(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function xt(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function we(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(r);e&&(a=a.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),t.push.apply(t,a)}return t}function Te(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?we(Object(t),!0).forEach(function(a){St(r,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):we(Object(t)).forEach(function(a){Object.defineProperty(r,a,Object.getOwnPropertyDescriptor(t,a))})}return r}function Pt(r){return Et(r)||Ft(r)||At(r)||xt()}function kt(r,e){if(typeof r!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var a=t.call(r,e);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function Dt(r){var e=kt(r,"string");return typeof e=="symbol"?e:e+""}function At(r,e){if(r){if(typeof r=="string")return ne(r,e);var t={}.toString.call(r).slice(8,-1);return t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set"?Array.from(r):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?ne(r,e):void 0}}var Ne={activateTrap:function(e,t){if(e.length>0){var a=e[e.length-1];a!==t&&a._setPausedState(!0)}var o=e.indexOf(t);o===-1||e.splice(o,1),e.push(t)},deactivateTrap:function(e,t){var a=e.indexOf(t);a!==-1&&e.splice(a,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)}},It=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},Ct=function(e){return(e==null?void 0:e.key)==="Escape"||(e==null?void 0:e.key)==="Esc"||(e==null?void 0:e.keyCode)===27},G=function(e){return(e==null?void 0:e.key)==="Tab"||(e==null?void 0:e.keyCode)===9},Ot=function(e){return G(e)&&!e.shiftKey},Rt=function(e){return G(e)&&e.shiftKey},Ee=function(e){return setTimeout(e,0)},q=function(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),o=1;o<t;o++)a[o-1]=arguments[o];return typeof e=="function"?e.apply(void 0,a):e},U=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Lt=[],Mt=function(e,t){var a=(t==null?void 0:t.document)||document,o=(t==null?void 0:t.trapStack)||Lt,u=Te({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Ot,isKeyBackward:Rt},t),n={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},d,l=function(i,s,c){return i&&i[s]!==void 0?i[s]:u[c||s]},f=function(i,s){var c=typeof(s==null?void 0:s.composedPath)=="function"?s.composedPath():void 0;return n.containerGroups.findIndex(function(p){var h=p.container,w=p.tabbableNodes;return h.contains(i)||(c==null?void 0:c.includes(h))||w.find(function(v){return v===i})})},m=function(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},c=s.hasFallback,p=c===void 0?!1:c,h=s.params,w=h===void 0?[]:h,v=u[i];if(typeof v=="function"&&(v=v.apply(void 0,Pt(w))),v===!0&&(v=void 0),!v){if(v===void 0||v===!1)return v;throw new Error("`".concat(i,"` was specified but was not a node, or did not return a node"))}var N=v;if(typeof v=="string"){try{N=a.querySelector(v)}catch(E){throw new Error("`".concat(i,'` appears to be an invalid selector; error="').concat(E.message,'"'))}if(!N&&!p)throw new Error("`".concat(i,"` as selector refers to no known node"))}return N},b=function(){var i=m("initialFocus",{hasFallback:!0});if(i===!1)return!1;if(i===void 0||i&&!re(i,u.tabbableOptions))if(f(a.activeElement)>=0)i=a.activeElement;else{var s=n.tabbableGroups[0],c=s&&s.firstTabbableNode;i=c||m("fallbackFocus")}else i===null&&(i=m("fallbackFocus"));if(!i)throw new Error("Your focus-trap needs to have at least one focusable element");return i},T=function(){if(n.containerGroups=n.containers.map(function(i){var s=wt(i,u.tabbableOptions),c=Tt(i,u.tabbableOptions),p=s.length>0?s[0]:void 0,h=s.length>0?s[s.length-1]:void 0,w=c.find(function(E){return $(E)}),v=c.slice().reverse().find(function(E){return $(E)}),N=!!s.find(function(E){return L(E)>0});return{container:i,tabbableNodes:s,focusableNodes:c,posTabIndexesFound:N,firstTabbableNode:p,lastTabbableNode:h,firstDomTabbableNode:w,lastDomTabbableNode:v,nextTabbableNode:function(O){var j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,A=s.indexOf(O);return A<0?j?c.slice(c.indexOf(O)+1).find(function(K){return $(K)}):c.slice(0,c.indexOf(O)).reverse().find(function(K){return $(K)}):s[A+(j?1:-1)]}}}),n.tabbableGroups=n.containerGroups.filter(function(i){return i.tabbableNodes.length>0}),n.tabbableGroups.length<=0&&!m("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(n.containerGroups.find(function(i){return i.posTabIndexesFound})&&n.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},S=function(i){var s=i.activeElement;if(s)return s.shadowRoot&&s.shadowRoot.activeElement!==null?S(s.shadowRoot):s},y=function(i){if(i!==!1&&i!==S(document)){if(!i||!i.focus){y(b());return}i.focus({preventScroll:!!u.preventScroll}),n.mostRecentlyFocusedNode=i,It(i)&&i.select()}},F=function(i){var s=m("setReturnFocus",{params:[i]});return s||(s===!1?!1:i)},k=function(i){var s=i.target,c=i.event,p=i.isBackward,h=p===void 0?!1:p;s=s||U(c),T();var w=null;if(n.tabbableGroups.length>0){var v=f(s,c),N=v>=0?n.containerGroups[v]:void 0;if(v<0)h?w=n.tabbableGroups[n.tabbableGroups.length-1].lastTabbableNode:w=n.tabbableGroups[0].firstTabbableNode;else if(h){var E=n.tabbableGroups.findIndex(function(Q){var ee=Q.firstTabbableNode;return s===ee});if(E<0&&(N.container===s||re(s,u.tabbableOptions)&&!$(s,u.tabbableOptions)&&!N.nextTabbableNode(s,!1))&&(E=v),E>=0){var O=E===0?n.tabbableGroups.length-1:E-1,j=n.tabbableGroups[O];w=L(s)>=0?j.lastTabbableNode:j.lastDomTabbableNode}else G(c)||(w=N.nextTabbableNode(s,!1))}else{var A=n.tabbableGroups.findIndex(function(Q){var ee=Q.lastTabbableNode;return s===ee});if(A<0&&(N.container===s||re(s,u.tabbableOptions)&&!$(s,u.tabbableOptions)&&!N.nextTabbableNode(s))&&(A=v),A>=0){var K=A===n.tabbableGroups.length-1?0:A+1,le=n.tabbableGroups[K];w=L(s)>=0?le.firstTabbableNode:le.firstDomTabbableNode}else G(c)||(w=N.nextTabbableNode(s))}}else w=m("fallbackFocus");return w},x=function(i){var s=U(i);if(!(f(s,i)>=0)){if(q(u.clickOutsideDeactivates,i)){d.deactivate({returnFocus:u.returnFocusOnDeactivate});return}q(u.allowOutsideClick,i)||i.preventDefault()}},P=function(i){var s=U(i),c=f(s,i)>=0;if(c||s instanceof Document)c&&(n.mostRecentlyFocusedNode=s);else{i.stopImmediatePropagation();var p,h=!0;if(n.mostRecentlyFocusedNode)if(L(n.mostRecentlyFocusedNode)>0){var w=f(n.mostRecentlyFocusedNode),v=n.containerGroups[w].tabbableNodes;if(v.length>0){var N=v.findIndex(function(E){return E===n.mostRecentlyFocusedNode});N>=0&&(u.isKeyForward(n.recentNavEvent)?N+1<v.length&&(p=v[N+1],h=!1):N-1>=0&&(p=v[N-1],h=!1))}}else n.containerGroups.some(function(E){return E.tabbableNodes.some(function(O){return L(O)>0})})||(h=!1);else h=!1;h&&(p=k({target:n.mostRecentlyFocusedNode,isBackward:u.isKeyBackward(n.recentNavEvent)})),y(p||n.mostRecentlyFocusedNode||b())}n.recentNavEvent=void 0},D=function(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;n.recentNavEvent=i;var c=k({event:i,isBackward:s});c&&(G(i)&&i.preventDefault(),y(c))},B=function(i){(u.isKeyForward(i)||u.isKeyBackward(i))&&D(i,u.isKeyBackward(i))},oe=function(i){Ct(i)&&q(u.escapeDeactivates,i)!==!1&&(i.preventDefault(),d.deactivate())},se=function(i){var s=U(i);f(s,i)>=0||q(u.clickOutsideDeactivates,i)||q(u.allowOutsideClick,i)||(i.preventDefault(),i.stopImmediatePropagation())},ue=function(){if(n.active)return Ne.activateTrap(o,d),n.delayInitialFocusTimer=u.delayInitialFocus?Ee(function(){y(b())}):y(b()),a.addEventListener("focusin",P,!0),a.addEventListener("mousedown",x,{capture:!0,passive:!1}),a.addEventListener("touchstart",x,{capture:!0,passive:!1}),a.addEventListener("click",se,{capture:!0,passive:!1}),a.addEventListener("keydown",B,{capture:!0,passive:!1}),a.addEventListener("keydown",oe),d},ce=function(){if(n.active)return a.removeEventListener("focusin",P,!0),a.removeEventListener("mousedown",x,!0),a.removeEventListener("touchstart",x,!0),a.removeEventListener("click",se,!0),a.removeEventListener("keydown",B,!0),a.removeEventListener("keydown",oe),d},$e=function(i){var s=i.some(function(c){var p=Array.from(c.removedNodes);return p.some(function(h){return h===n.mostRecentlyFocusedNode})});s&&y(b())},J=typeof window<"u"&&"MutationObserver"in window?new MutationObserver($e):void 0,_=function(){J&&(J.disconnect(),n.active&&!n.paused&&n.containers.map(function(i){J.observe(i,{subtree:!0,childList:!0})}))};return d={get active(){return n.active},get paused(){return n.paused},activate:function(i){if(n.active)return this;var s=l(i,"onActivate"),c=l(i,"onPostActivate"),p=l(i,"checkCanFocusTrap");p||T(),n.active=!0,n.paused=!1,n.nodeFocusedBeforeActivation=S(a),s==null||s();var h=function(){p&&T(),ue(),_(),c==null||c()};return p?(p(n.containers.concat()).then(h,h),this):(h(),this)},deactivate:function(i){if(!n.active)return this;var s=Te({onDeactivate:u.onDeactivate,onPostDeactivate:u.onPostDeactivate,checkCanReturnFocus:u.checkCanReturnFocus},i);clearTimeout(n.delayInitialFocusTimer),n.delayInitialFocusTimer=void 0,ce(),n.active=!1,n.paused=!1,_(),Ne.deactivateTrap(o,d);var c=l(s,"onDeactivate"),p=l(s,"onPostDeactivate"),h=l(s,"checkCanReturnFocus"),w=l(s,"returnFocus","returnFocusOnDeactivate");c==null||c();var v=function(){Ee(function(){w&&y(F(n.nodeFocusedBeforeActivation)),p==null||p()})};return w&&h?(h(F(n.nodeFocusedBeforeActivation)).then(v,v),this):(v(),this)},pause:function(i){return n.active?(n.manuallyPaused=!0,this._setPausedState(!0,i)):this},unpause:function(i){return n.active?(n.manuallyPaused=!1,o[o.length-1]!==this?this:this._setPausedState(!1,i)):this},updateContainerElements:function(i){var s=[].concat(i).filter(Boolean);return n.containers=s.map(function(c){return typeof c=="string"?a.querySelector(c):c}),n.active&&T(),_(),this}},Object.defineProperties(d,{_isManuallyPaused:{value:function(){return n.manuallyPaused}},_setPausedState:{value:function(i,s){if(n.paused===i)return this;if(n.paused=i,i){var c=l(s,"onPause"),p=l(s,"onPostPause");c==null||c(),ce(),_(),p==null||p()}else{var h=l(s,"onUnpause"),w=l(s,"onPostUnpause");h==null||h(),T(),ue(),_(),w==null||w()}return this}}}),d.updateContainerElements(e),d};function rr(r={}){let e;const{immediate:t,...a}=r,o=pe(!1),u=pe(!1),n=b=>e==null?void 0:e.activate(b),d=b=>{e==null||e.deactivate(b)},l=()=>{e&&(e.pause(),u.set(!0))},f=()=>{e&&(e.unpause(),u.set(!1))};return{useFocusTrap:b=>(e=Mt(b,{...a,onActivate(){var T;o.set(!0),(T=r.onActivate)==null||T.call(r)},onDeactivate(){var T;o.set(!1),(T=r.onDeactivate)==null||T.call(r)}}),t&&n(),{destroy(){d(),e=void 0}}),hasFocus:be(o),isPaused:be(u),activate:n,deactivate:d,pause:l,unpause:f}}const V=[],ar=(r,e)=>{let t=W;function a(){const u=V.indexOf(r);u>=0&&V.splice(u,1)}function o(u){t();const{open:n,onClose:d,shouldCloseOnInteractOutside:l,closeOnInteractOutside:f}=u;Pe(100).then(()=>{n?V.push(r):a()});function m(){return Ze(V)===r}function b(){m()&&d&&(d(),a())}function T(y){const F=y.target;ie(F)&&F&&m()&&(y.preventDefault(),y.stopPropagation(),y.stopImmediatePropagation())}function S(y){l!=null&&l(y)&&m()&&(y.preventDefault(),y.stopPropagation(),y.stopImmediatePropagation(),b())}t=$t(r,{onInteractOutsideStart:T,onInteractOutside:f?S:void 0,enabled:n}).destroy}return o(e),{update:o,destroy(){a(),t()}}},nr=(r,e="body")=>{let t;if(!Y(e)&&typeof e!="string")return{destroy:W};async function a(u){if(e=u,typeof e=="string"){if(t=document.querySelector(e),t===null&&(await xe(),t=document.querySelector(e)),t===null)throw new Error(`No element found matching css selector: "${e}"`)}else if(e instanceof HTMLElement)t=e;else throw new TypeError(`Unknown portal target type: ${e===null?"null":typeof e}. Allowed types: string (CSS selector) or HTMLElement.`);r.dataset.portal="",t.appendChild(r),r.hidden=!1}function o(){r.remove()}return a(e),{update:a,destroy:o}},$t=(r,e)=>{let t=W,a=W,o=!1,u=!1,n=!1;function d(m){t(),a();const{onInteractOutside:b,onInteractOutsideStart:T,enabled:S}=m;if(!S)return;function y(x){b&&Se(x,r)&&(T==null||T(x));const P=x.target;ie(P)&&Me(r,P)&&(u=!0),o=!0}function F(x){b==null||b(x)}const k=Bt(r);if(typeof PointerEvent<"u"){const x=P=>{a();const D=B=>{l(B)&&F(B),f()};if(P.pointerType==="touch"){a=R(k,"click",D,{capture:!0,once:!0});return}D(P)};t=ve(R(k,"pointerdown",y,!0),R(k,"pointerup",x,!0))}else{const x=D=>{n?n=!1:l(D)&&F(D),f()},P=D=>{n=!0,l(D)&&F(D),f()};t=ve(R(k,"mousedown",y,!0),R(k,"mouseup",x,!0),R(k,"touchstart",y,!0),R(k,"touchend",P,!0))}}function l(m){return!!(o&&!u&&Se(m,r))}function f(){o=!1,u=!1}return d(e),{update:d,destroy(){t(),a()}}};function Se(r,e){if("button"in r&&r.button>0)return!1;const t=r.target;if(!ie(t))return!1;const a=t.ownerDocument;return!a||!a.documentElement.contains(t)?!1:e&&!Me(e,t)}function Me(r,e){return r===e||r.contains(e)}function Bt(r){return(r==null?void 0:r.ownerDocument)??document}var _t=Ke('<footer class="border-t-2 border-border bg-black"><div class="max-w-6xl mx-auto px-6 py-12"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="space-y-4"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-background shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <h3 class="font-black text-white"> </h3></div> <p class="text-sm text-gray-400 font-medium">AI-powered marketing specialists that grow your business.</p></div> <div class="space-y-4"><h4 class="font-bold text-white">Product</h4> <ul class="space-y-2 text-sm"><li><a href="/" class="text-gray-400 hover:text-white transition-colors font-medium">Overview</a></li> <li><a href="/blog" class="text-gray-400 hover:text-white transition-colors font-medium">Blog</a></li></ul></div> <div class="space-y-4"><h4 class="font-bold text-white">Support</h4> <ul class="space-y-2 text-sm"><li><a href="/contact_us" class="text-gray-400 hover:text-white transition-colors font-medium">Contact Us</a></li> <li><a href="/login" class="text-gray-400 hover:text-white transition-colors font-medium">Sign In</a></li></ul></div> <div class="space-y-4"><h4 class="font-bold text-white">Connect</h4> <div class="flex gap-4"><a href="https://github.com/madhukarkumar/saas-starter" aria-label="GitHub" class="text-gray-400 hover:text-white transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"></path></svg></a></div></div></div> <div class="border-t-2 border-gray-800 mt-8 pt-8 text-center"><p class="text-sm text-gray-400 font-medium"> </p></div></div></footer>');function ir(r,e){je(e,!1),ze();var t=_t(),a=I(t),o=I(a),u=I(o),n=I(u),d=de(I(n),2),l=I(d,!0);C(d),C(n),fe(2),C(u),fe(6),C(o);var f=de(o,2),m=I(f),b=I(m);C(m),C(f),C(a),C(t),qe(T=>{he(l,me),he(b,`Copyright © ${T??""} ${me}. All rights reserved.`)},[()=>new Date().getFullYear()],We),Fe(r,t),Ge()}export{Zt as M,ir as S,nr as a,rr as c,er as g,tr as h,Qt as r,Pe as s,ar as u,Jt as w};
