import{_ as H,w as p,I as O,aF as F,am as m,an as T,ao as W,y as C,L as z,ai as G,aC as K,au as j,K as X,b1 as J,C as A,R as x,B as S,U as g,F as L,A as h,b2 as R,H as N,x as Q,aw as Z,D as ee,b3 as te,ay as re,b4 as ae,N as ne,p as oe,aI as ie,E as se,g as ue}from"./wnqW1tdD.js";function ge(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const le=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function we(e){return le.includes(e)}const ce={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function be(e){return e=e.toLowerCase(),ce[e]??e}const de=["touchstart","touchmove"];function fe(e){return de.includes(e)}const _e=["textarea","script","style","title"];function me(e){return _e.includes(e)}function Te(e,t){if(t){const r=document.body;e.autofocus=!0,H(()=>{document.activeElement===r&&e.focus()})}}function Le(e){p&&O(e)!==null&&F(e)}let V=!1;function he(){V||(V=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function B(e){var t=W,r=C;m(null),T(null);try{return e()}finally{m(t),T(r)}}function ke(e,t,r,n=r){e.addEventListener(t,()=>B(r));const o=e.__on_r;o?e.__on_r=()=>{o(),n(!0)}:e.__on_r=()=>n(!0),he()}const Y=new Set,D=new Set;function pe(e,t,r,n={}){function o(a){if(n.capture||w.call(t,a),!a.cancelBubble)return B(()=>r==null?void 0:r.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?H(()=>{t.addEventListener(e,o,n)}):t.addEventListener(e,o,n),o}function Ne(e,t,r,n,o){var a={capture:n,passive:o},l=pe(e,t,r,a);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&z(()=>{t.removeEventListener(e,l,a)})}function Ae(e){for(var t=0;t<e.length;t++)Y.add(e[t]);for(var r of D)r(e)}function w(e){var P;var t=this,r=t.ownerDocument,n=e.type,o=((P=e.composedPath)==null?void 0:P.call(e))||[],a=o[0]||e.target,l=0,v=e.__root;if(v){var f=o.indexOf(v);if(f!==-1&&(t===document||t===window)){e.__root=t;return}var y=o.indexOf(t);if(y===-1)return;f<=y&&(l=f)}if(a=o[l]||e.target,a!==t){G(e,"currentTarget",{configurable:!0,get(){return a||r}});var k=W,c=C;m(null),T(null);try{for(var i,s=[];a!==null;){var d=a.assignedSlot||a.parentNode||a.host||null;try{var _=a["__"+n];if(_!=null&&(!a.disabled||e.target===a))if(K(_)){var[U,...$]=_;U.apply(a,[e,...$])}else _.call(a,e)}catch(b){i?s.push(b):i=b}if(e.cancelBubble||d===t||d===null)break;a=d}if(i){for(let b of s)queueMicrotask(()=>{throw b});throw i}}finally{e.__root=t,delete e.currentTarget,m(k),T(c)}}}let u;function ve(){u=void 0}function Se(e){let t=null,r=p;var n;if(p){for(t=h,u===void 0&&(u=O(document.head));u!==null&&(u.nodeType!==A||u.data!==x);)u=S(u);u===null?g(!1):u=L(S(u))}p||(n=document.head.appendChild(j()));try{X(()=>e(n),J)}finally{r&&(g(!0),u=h,L(t))}}let I=!0;function Re(e){I=e}function De(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=r,e.nodeValue=r+"")}function ye(e,t){return q(e,t)}function Ie(e,t){R(),t.intro=t.intro??!1;const r=t.target,n=p,o=h;try{for(var a=O(r);a&&(a.nodeType!==A||a.data!==x);)a=S(a);if(!a)throw N;g(!0),L(a),Q();const l=q(e,{...t,anchor:a});if(h===null||h.nodeType!==A||h.data!==Z)throw ee(),N;return g(!1),l}catch(l){if(l===N)return t.recover===!1&&te(),R(),F(r),g(!1),ye(e,t);throw l}finally{g(n),L(o),ve()}}const E=new Map;function q(e,{target:t,anchor:r,props:n={},events:o,context:a,intro:l=!0}){R();var v=new Set,f=c=>{for(var i=0;i<c.length;i++){var s=c[i];if(!v.has(s)){v.add(s);var d=fe(s);t.addEventListener(s,w,{passive:d});var _=E.get(s);_===void 0?(document.addEventListener(s,w,{passive:d}),E.set(s,1)):E.set(s,_+1)}}};f(re(Y)),D.add(f);var y=void 0,k=ae(()=>{var c=r??t.appendChild(j());return ne(()=>{if(a){oe({});var i=ie;i.c=a}o&&(n.$$events=o),p&&se(c,null),I=l,y=e(c,n)||{},I=!0,p&&(C.nodes_end=h),a&&ue()}),()=>{var d;for(var i of v){t.removeEventListener(i,w);var s=E.get(i);--s===0?(document.removeEventListener(i,w),E.delete(i)):E.set(i,s)}D.delete(f),c!==r&&((d=c.parentNode)==null||d.removeChild(c))}});return M.set(y,k),y}let M=new WeakMap;function Me(e,t){const r=M.get(e);return r?(M.delete(e),r(t)):Promise.resolve()}export{I as a,Re as b,ge as c,Ae as d,Ne as e,pe as f,Te as g,Se as h,me as i,he as j,we as k,ke as l,Ie as m,be as n,ye as o,Le as r,De as s,Me as u,B as w};
