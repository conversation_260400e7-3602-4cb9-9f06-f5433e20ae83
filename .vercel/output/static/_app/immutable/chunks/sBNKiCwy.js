import{y as R,M as j,aj as q,ak as x,Y as B,i as U,al as G,af as K,_ as P,O as N,am as S,an as L,ao as W,ap as Y,aq as g}from"./wnqW1tdD.js";import{l as k}from"./BGGTUj09.js";import{a as z,w as D}from"./CDPCzm7q.js";function O(r,a){D(()=>{r.dispatchEvent(new CustomEvent(a))})}function H(r){if(r==="float")return"cssFloat";if(r==="offset")return"cssOffset";if(r.startsWith("--"))return r;const a=r.split("-");return a.length===1?a[0]:a[0]+a.slice(1).map(i=>i[0].toUpperCase()+i.slice(1)).join("")}function M(r){const a={},i=r.split(";");for(const t of i){const[n,_]=t.split(":");if(!n||_===void 0)break;const c=H(n.trim());a[c]=_.trim()}return a}const J=r=>r;function Z(r,a,i,t){var n=(r&Y)!==0,_=(r&g)!==0,c=n&&_,w=(r&G)!==0,p=c?"both":n?"in":"out",l,o=a.inert,b=a.style.overflow,s,e;function h(){var f=W,y=R;S(null),L(null);try{return l??(l=i()(a,(t==null?void 0:t())??{},{direction:p}))}finally{S(f),L(y)}}var v={is_global:w,in(){var f;if(a.inert=o,!n){e==null||e.abort(),(f=e==null?void 0:e.reset)==null||f.call(e);return}_||s==null||s.abort(),O(a,"introstart"),s=F(a,h(),e,1,()=>{O(a,"introend"),s==null||s.abort(),s=l=void 0,a.style.overflow=b})},out(f){if(!_){f==null||f(),l=void 0;return}a.inert=!0,O(a,"outrostart"),e=F(a,h(),s,0,()=>{O(a,"outroend"),f==null||f()})},stop:()=>{s==null||s.abort(),e==null||e.abort()}},d=R;if((d.transitions??(d.transitions=[])).push(v),n&&z){var T=w;if(!T){for(var u=d.parent;u&&u.f&j;)for(;(u=u.parent)&&!(u.f&q););T=!u||(u.f&x)!==0}T&&B(()=>{U(()=>v.in())})}}function F(r,a,i,t,n){var _=t===1;if(K(a)){var c,w=!1;return P(()=>{if(!w){var d=a({direction:_?"in":"out"});c=F(r,d,i,t,n)}}),{abort:()=>{w=!0,c==null||c.abort()},deactivate:()=>c.deactivate(),reset:()=>c.reset(),t:()=>c.t()}}if(i==null||i.deactivate(),!(a!=null&&a.duration))return n(),{abort:N,deactivate:N,reset:N,t:()=>t};const{delay:p=0,css:l,tick:o,easing:b=J}=a;var s=[];if(_&&i===void 0&&(o&&o(0,1),l)){var e=M(l(0,1));s.push(e,e)}var h=()=>1-t,v=r.animate(s,{duration:p,fill:"forwards"});return v.onfinish=()=>{v.cancel();var d=(i==null?void 0:i.t())??1-t;i==null||i.abort();var T=t-d,u=a.duration*Math.abs(T),f=[];if(u>0){var y=!1;if(l)for(var I=Math.ceil(u/16.666666666666668),A=0;A<=I;A+=1){var m=d+T*b(A/I),C=M(l(m,1-m));f.push(C),y||(y=C.overflow==="hidden")}y&&(r.style.overflow="hidden"),h=()=>{var E=v.currentTime;return d+T*b(E/u)},o&&k(()=>{if(v.playState!=="running")return!1;var E=h();return o(E,1-E),!0})}v=r.animate(f,{duration:u,fill:"forwards"}),v.onfinish=()=>{h=()=>t,o==null||o(t,1-t),n()}},{abort:()=>{v&&(v.cancel(),v.effect=null,v.onfinish=N)},deactivate:()=>{n=N},reset:()=>{t===0&&(o==null||o(1,0))},t:()=>h()}}export{Z as t};
