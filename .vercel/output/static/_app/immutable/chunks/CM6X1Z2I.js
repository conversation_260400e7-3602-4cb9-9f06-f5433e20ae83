import{w as t,x as u,A as l,aO as y,K as h,M as N,N as x,aP as A,E as T,I as b,au as C,U as c,F as d,y as P,W as S,V as w,P as F}from"./wnqW1tdD.js";import{i as M,b as m}from"./CDPCzm7q.js";function D(E,g,p,i,O,R){let v=t;t&&u();var n,r,e=null;t&&l.nodeType===y&&(e=l,u());var _=t?l:E,s;h(()=>{const a=g()||null;var o=p||a==="svg"?A:null;a!==n&&(s&&(a===null?S(s,()=>{s=null,r=null}):a===r?w(s):(F(s),m(!1))),a&&a!==r&&(s=x(()=>{if(e=t?e:o?document.createElementNS(o,a):document.createElement(a),T(e,e),i){t&&M(a)&&e.append(document.createComment(""));var f=t?b(e):e.appendChild(C());t&&(f===null?c(!1):d(f)),i(e,f)}P.nodes_end=e,_.before(e)})),n=a,n&&(r=n),m(!0))},N),v&&(c(!0),d(_))}export{D as e};
