import{s as b,g as c}from"./BvpDAKCq.js";import{O as i,m as o,j as l,L as d,ai as p,k as _}from"./wnqW1tdD.js";let s=!1,t=Symbol();function m(e,n,r){const u=r[n]??(r[n]={store:null,source:o(void 0),unsubscribe:i});if(u.store!==e&&!(t in r))if(u.unsubscribe(),u.store=e??null,e==null)u.source.v=void 0,u.unsubscribe=i;else{var a=!0;u.unsubscribe=b(e,f=>{a?u.source.v=f:_(u.source,f)}),a=!1}return e&&t in r?c(e):l(u.source)}function y(e,n,r){let u=r[n];return u&&u.store!==e&&(u.unsubscribe(),u.unsubscribe=i),e}function N(){const e={};function n(){d(()=>{for(var r in e)e[r].unsubscribe();p(e,t,{enumerable:!1,value:!0})})}return[e,n]}function O(e,n,r){return e.set(r),n}function S(e){var n=s;try{return s=!1,[e(),s]}finally{s=n}}export{N as a,O as b,S as c,y as d,m as s};
