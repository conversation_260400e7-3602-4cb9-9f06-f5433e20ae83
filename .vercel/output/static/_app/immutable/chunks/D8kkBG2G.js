import{t as u,w as o,x as l,y as g,z as y,A as h,C as p,B as w,D as E,H as O,E as c,F as R,G as b,I as f}from"./wnqW1tdD.js";function N(m,v,i=!1,_=!1,C=!1){var n=m,t="";u(()=>{var s=g;if(t===(t=v()??"")){o&&l();return}if(s.nodes_start!==null&&(y(s.nodes_start,s.nodes_end),s.nodes_start=s.nodes_end=null),t!==""){if(o){h.data;for(var e=l(),d=e;e!==null&&(e.nodeType!==p||e.data!=="");)d=e,e=w(e);if(e===null)throw E(),O;c(h,d),n=R(e);return}var r=t+"";i?r=`<svg>${r}</svg>`:_&&(r=`<math>${r}</math>`);var a=b(r);if((i||_)&&(a=f(a)),c(f(a),a.lastChild),i||_)for(;f(a);)n.before(f(a));else n.before(a)}})}export{N as h};
