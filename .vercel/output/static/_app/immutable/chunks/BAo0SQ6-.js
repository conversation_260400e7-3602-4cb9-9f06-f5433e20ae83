import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as p,e as i,a as m}from"./wnqW1tdD.js";import{s as l}from"./BDqVm3Gq.js";import{l as d,s as c}from"./Cmdkv-7M.js";import{I as $}from"./CX_t0Ed_.js";function y(r,o){const s=d(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M18 6 6 18"}],["path",{d:"m6 6 12 12"}]];$(r,c({name:"x"},()=>s,{get iconNode(){return e},children:(a,f)=>{var t=p(),n=i(t);l(n,o,"default",{},null),m(a,t)},$$slots:{default:!0}}))}export{y as X};
