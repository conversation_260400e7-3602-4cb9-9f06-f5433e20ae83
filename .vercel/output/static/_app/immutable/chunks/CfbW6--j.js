import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as l,e as i,a as c}from"./wnqW1tdD.js";import{s as d}from"./BDqVm3Gq.js";import{l as p,s as $}from"./Cmdkv-7M.js";import{I as m}from"./CX_t0Ed_.js";function v(t,e){const n=p(e,["children","$$slots","$$events","$$legacy"]),o=[["line",{x1:"12",x2:"12",y1:"20",y2:"10"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16"}]];m(t,$({name:"chart-no-axes-column-increasing"},()=>n,{get iconNode(){return o},children:(s,u)=>{var a=l(),r=i(a);d(r,e,"default",{},null),c(s,a)},$$slots:{default:!0}}))}function M(t,e){const n=p(e,["children","$$slots","$$events","$$legacy"]),o=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"}],["path",{d:"M20 3v4"}],["path",{d:"M22 5h-4"}],["path",{d:"M4 17v2"}],["path",{d:"M5 18H3"}]];m(t,$({name:"sparkles"},()=>n,{get iconNode(){return o},children:(s,u)=>{var a=l(),r=i(a);d(r,e,"default",{},null),c(s,a)},$$slots:{default:!0}}))}export{v as C,M as S};
