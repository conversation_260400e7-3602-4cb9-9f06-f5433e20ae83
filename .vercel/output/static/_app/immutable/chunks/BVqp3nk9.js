import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as l,e as i,a as p}from"./wnqW1tdD.js";import{s as d}from"./BDqVm3Gq.js";import{l as m,s as c}from"./Cmdkv-7M.js";import{I as $}from"./CX_t0Ed_.js";function x(t,o){const a=m(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["polyline",{points:"7 10 12 15 17 10"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3"}]];$(t,c({name:"download"},()=>a,{get iconNode(){return e},children:(r,f)=>{var s=l(),n=i(s);d(n,o,"default",{},null),p(r,s)},$$slots:{default:!0}}))}export{x as D};
