var oe=t=>{throw TypeError(t)};var Ke=(t,e,n)=>e.has(t)||oe("Cannot "+n);var A=(t,e,n)=>(Ke(t,e,"read from private field"),n?n.call(t):e.get(t)),C=(t,e,n)=>e.has(t)?oe("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n);import{o as se,s as Je}from"./CfBaWyh2.js";import{w as qt}from"./BvpDAKCq.js";import{J as P,j as O,k as N}from"./wnqW1tdD.js";new URL("sveltekit-internal://");function We(t,e){return t==="/"||e==="ignore"?t:e==="never"?t.endsWith("/")?t.slice(0,-1):t:e==="always"&&!t.endsWith("/")?t+"/":t}function Ye(t){return t.split("%25").map(decodeURI).join("%25")}function ze(t){for(const e in t)t[e]=decodeURIComponent(t[e]);return t}function Ct({href:t}){return t.split("#")[0]}function Xe(t,e,n,a=!1){const r=new URL(t);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(i,o){if(o==="get"||o==="getAll"||o==="has")return l=>(n(l),i[o](l));e();const c=Reflect.get(i,o);return typeof c=="function"?c.bind(i):c}}),enumerable:!0,configurable:!0});const s=["href","pathname","search","toString","toJSON"];a&&s.push("hash");for(const i of s)Object.defineProperty(r,i,{get(){return e(),t[i]},enumerable:!0,configurable:!0});return r}function Ze(...t){let e=5381;for(const n of t)if(typeof n=="string"){let a=n.length;for(;a;)e=e*33^n.charCodeAt(--a)}else if(ArrayBuffer.isView(n)){const a=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let r=a.length;for(;r;)e=e*33^a[--r]}else throw new TypeError("value must be a string or TypedArray");return(e>>>0).toString(36)}function Qe(t){const e=atob(t),n=new Uint8Array(e.length);for(let a=0;a<e.length;a++)n[a]=e.charCodeAt(a);return n.buffer}const tn=window.fetch;window.fetch=(t,e)=>((t instanceof Request?t.method:(e==null?void 0:e.method)||"GET")!=="GET"&&W.delete(Gt(t)),tn(t,e));const W=new Map;function en(t,e){const n=Gt(t,e),a=document.querySelector(n);if(a!=null&&a.textContent){let{body:r,...s}=JSON.parse(a.textContent);const i=a.getAttribute("data-ttl");return i&&W.set(n,{body:r,init:s,ttl:1e3*Number(i)}),a.getAttribute("data-b64")!==null&&(r=Qe(r)),Promise.resolve(new Response(r,s))}return window.fetch(t,e)}function nn(t,e,n){if(W.size>0){const a=Gt(t,n),r=W.get(a);if(r){if(performance.now()<r.ttl&&["default","force-cache","only-if-cached",void 0].includes(n==null?void 0:n.cache))return new Response(r.body,r.init);W.delete(a)}}return window.fetch(e,n)}function Gt(t,e){let a=`script[data-sveltekit-fetched][data-url=${JSON.stringify(t instanceof Request?t.url:t)}]`;if(e!=null&&e.headers||e!=null&&e.body){const r=[];e.headers&&r.push([...new Headers(e.headers)].join(",")),e.body&&(typeof e.body=="string"||ArrayBuffer.isView(e.body))&&r.push(e.body),a+=`[data-hash="${Ze(...r)}"]`}return a}const rn=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function an(t){const e=[];return{pattern:t==="/"?/^\/$/:new RegExp(`^${sn(t).map(a=>{const r=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(a);if(r)return e.push({name:r[1],matcher:r[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const s=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(a);if(s)return e.push({name:s[1],matcher:s[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!a)return;const i=a.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,l)=>{if(l%2){if(c.startsWith("x+"))return Pt(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return Pt(String.fromCharCode(...c.slice(2).split("-").map(d=>parseInt(d,16))));const p=rn.exec(c),[,u,y,f,m]=p;return e.push({name:f,matcher:m,optional:!!u,rest:!!y,chained:y?l===1&&i[0]==="":!1}),y?"(.*?)":u?"([^/]*)?":"([^/]+?)"}return Pt(c)}).join("")}).join("")}/?$`),params:e}}function on(t){return!/^\([^)]+\)$/.test(t)}function sn(t){return t.slice(1).split("/").filter(on)}function cn(t,e,n){const a={},r=t.slice(1),s=r.filter(o=>o!==void 0);let i=0;for(let o=0;o<e.length;o+=1){const c=e[o];let l=r[o-i];if(c.chained&&c.rest&&i&&(l=r.slice(o-i,o+1).filter(p=>p).join("/"),i=0),l===void 0){c.rest&&(a[c.name]="");continue}if(!c.matcher||n[c.matcher](l)){a[c.name]=l;const p=e[o+1],u=r[o+1];p&&!p.rest&&p.optional&&u&&c.chained&&(i=0),!p&&!u&&Object.keys(a).length===s.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return a}function Pt(t){return t.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function ln({nodes:t,server_loads:e,dictionary:n,matchers:a}){const r=new Set(e);return Object.entries(n).map(([o,[c,l,p]])=>{const{pattern:u,params:y}=an(o),f={id:o,exec:m=>{const d=u.exec(m);if(d)return cn(d,y,a)},errors:[1,...p||[]].map(m=>t[m]),layouts:[0,...l||[]].map(i),leaf:s(c)};return f.errors.length=f.layouts.length=Math.max(f.errors.length,f.layouts.length),f});function s(o){const c=o<0;return c&&(o=~o),[c,t[o]]}function i(o){return o===void 0?o:[r.has(o),t[o]]}}function be(t,e=JSON.parse){try{return e(sessionStorage[t])}catch{}}function ie(t,e,n=JSON.stringify){const a=n(e);try{sessionStorage[t]=a}catch{}}var me;const x=((me=globalThis.__sveltekit_g4gg6z)==null?void 0:me.base)??"";var ye;const fn=((ye=globalThis.__sveltekit_g4gg6z)==null?void 0:ye.assets)??x,un="1752536001709",Ae="sveltekit:snapshot",Se="sveltekit:scroll",ke="sveltekit:states",dn="sveltekit:pageurl",q="sveltekit:history",Z="sveltekit:navigation",B={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},ht=location.origin;function Ht(t){if(t instanceof URL)return t;let e=document.baseURI;if(!e){const n=document.getElementsByTagName("base");e=n.length?n[0].href:document.URL}return new URL(t,e)}function kt(){return{x:pageXOffset,y:pageYOffset}}function M(t,e){return t.getAttribute(`data-sveltekit-${e}`)}const ce={...B,"":B.hover};function Ee(t){let e=t.assignedSlot??t.parentNode;return(e==null?void 0:e.nodeType)===11&&(e=e.host),e}function Re(t,e){for(;t&&t!==e;){if(t.nodeName.toUpperCase()==="A"&&t.hasAttribute("href"))return t;t=Ee(t)}}function $t(t,e,n){let a;try{if(a=new URL(t instanceof SVGAElement?t.href.baseVal:t.href,document.baseURI),n&&a.hash.match(/^#[^/]/)){const o=location.hash.split("#")[1]||"/";a.hash=`#${o}${a.hash}`}}catch{}const r=t instanceof SVGAElement?t.target.baseVal:t.target,s=!a||!!r||Et(a,e,n)||(t.getAttribute("rel")||"").split(/\s+/).includes("external"),i=(a==null?void 0:a.origin)===ht&&t.hasAttribute("download");return{url:a,external:s,target:r,download:i}}function yt(t){let e=null,n=null,a=null,r=null,s=null,i=null,o=t;for(;o&&o!==document.documentElement;)a===null&&(a=M(o,"preload-code")),r===null&&(r=M(o,"preload-data")),e===null&&(e=M(o,"keepfocus")),n===null&&(n=M(o,"noscroll")),s===null&&(s=M(o,"reload")),i===null&&(i=M(o,"replacestate")),o=Ee(o);function c(l){switch(l){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:ce[a??"off"],preload_data:ce[r??"off"],keepfocus:c(e),noscroll:c(n),reload:c(s),replace_state:c(i)}}function le(t){const e=qt(t);let n=!0;function a(){n=!0,e.update(i=>i)}function r(i){n=!1,e.set(i)}function s(i){let o;return e.subscribe(c=>{(o===void 0||n&&c!==o)&&i(o=c)})}return{notify:a,set:r,subscribe:s}}const Ue={v:()=>{}};function hn(){const{set:t,subscribe:e}=qt(!1);let n;async function a(){clearTimeout(n);try{const r=await fetch(`${fn}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!r.ok)return!1;const i=(await r.json()).version!==un;return i&&(t(!0),Ue.v(),clearTimeout(n)),i}catch{return!1}}return{subscribe:e,check:a}}function Et(t,e,n){return t.origin!==ht||!t.pathname.startsWith(e)?!0:n?!(t.pathname===e+"/"||t.pathname===e+"/index.html"||t.protocol==="file:"&&t.pathname.replace(/\/[^/]+\.html?$/,"")===e):!1}function Wn(t){}function Yn(t){const e=new DataView(t);let n="";for(let a=0;a<t.byteLength;a++)n+=String.fromCharCode(e.getUint8(a));return gn(n)}function fe(t){const e=pn(t),n=new ArrayBuffer(e.length),a=new DataView(n);for(let r=0;r<n.byteLength;r++)a.setUint8(r,e.charCodeAt(r));return n}const Ie="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function pn(t){t.length%4===0&&(t=t.replace(/==?$/,""));let e="",n=0,a=0;for(let r=0;r<t.length;r++)n<<=6,n|=Ie.indexOf(t[r]),a+=6,a===24&&(e+=String.fromCharCode((n&16711680)>>16),e+=String.fromCharCode((n&65280)>>8),e+=String.fromCharCode(n&255),n=a=0);return a===12?(n>>=4,e+=String.fromCharCode(n)):a===18&&(n>>=2,e+=String.fromCharCode((n&65280)>>8),e+=String.fromCharCode(n&255)),e}function gn(t){let e="";for(let n=0;n<t.length;n+=3){const a=[void 0,void 0,void 0,void 0];a[0]=t.charCodeAt(n)>>2,a[1]=(t.charCodeAt(n)&3)<<4,t.length>n+1&&(a[1]|=t.charCodeAt(n+1)>>4,a[2]=(t.charCodeAt(n+1)&15)<<2),t.length>n+2&&(a[2]|=t.charCodeAt(n+2)>>6,a[3]=t.charCodeAt(n+2)&63);for(let r=0;r<a.length;r++)typeof a[r]>"u"?e+="=":e+=Ie[a[r]]}return e}const mn=-1,yn=-2,wn=-3,_n=-4,vn=-5,bn=-6;function zn(t,e){return Le(JSON.parse(t),e)}function Le(t,e){if(typeof t=="number")return r(t,!0);if(!Array.isArray(t)||t.length===0)throw new Error("Invalid input");const n=t,a=Array(n.length);function r(s,i=!1){if(s===mn)return;if(s===wn)return NaN;if(s===_n)return 1/0;if(s===vn)return-1/0;if(s===bn)return-0;if(i)throw new Error("Invalid input");if(s in a)return a[s];const o=n[s];if(!o||typeof o!="object")a[s]=o;else if(Array.isArray(o))if(typeof o[0]=="string"){const c=o[0],l=e==null?void 0:e[c];if(l)return a[s]=l(r(o[1]));switch(c){case"Date":a[s]=new Date(o[1]);break;case"Set":const p=new Set;a[s]=p;for(let f=1;f<o.length;f+=1)p.add(r(o[f]));break;case"Map":const u=new Map;a[s]=u;for(let f=1;f<o.length;f+=2)u.set(r(o[f]),r(o[f+1]));break;case"RegExp":a[s]=new RegExp(o[1],o[2]);break;case"Object":a[s]=Object(o[1]);break;case"BigInt":a[s]=BigInt(o[1]);break;case"null":const y=Object.create(null);a[s]=y;for(let f=1;f<o.length;f+=2)y[o[f]]=r(o[f+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const f=globalThis[c],m=o[1],d=fe(m),h=new f(d);a[s]=h;break}case"ArrayBuffer":{const f=o[1],m=fe(f);a[s]=m;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(o.length);a[s]=c;for(let l=0;l<o.length;l+=1){const p=o[l];p!==yn&&(c[l]=r(p))}}else{const c={};a[s]=c;for(const l in o){const p=o[l];c[l]=r(p)}}return a[s]}return r(0)}const Te=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...Te];const An=new Set([...Te]);[...An];function Sn(t){return t.filter(e=>e!=null)}class Rt{constructor(e,n){this.status=e,typeof n=="string"?this.body={message:n}:n?this.body=n:this.body={message:`Error: ${e}`}}toString(){return JSON.stringify(this.body)}}class Kt{constructor(e,n){this.status=e,this.location=n}}class Jt extends Error{constructor(e,n,a){super(a),this.status=e,this.text=n}}const kn="x-sveltekit-invalidated",En="x-sveltekit-trailing-slash";function wt(t){return t instanceof Rt||t instanceof Jt?t.status:500}function Rn(t){return t instanceof Jt?t.text:"Internal Error"}let k,Q,Ot;const Un=se.toString().includes("$$")||/function \w+\(\) \{\}/.test(se.toString());var rt,at,ot,st,it,ct,lt,ft,we,ut,_e,dt,ve;Un?(k={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},Q={current:null},Ot={current:!1}):(k=new(we=class{constructor(){C(this,rt,P({}));C(this,at,P(null));C(this,ot,P(null));C(this,st,P({}));C(this,it,P({id:null}));C(this,ct,P({}));C(this,lt,P(-1));C(this,ft,P(new URL("https://example.com")))}get data(){return O(A(this,rt))}set data(e){N(A(this,rt),e)}get form(){return O(A(this,at))}set form(e){N(A(this,at),e)}get error(){return O(A(this,ot))}set error(e){N(A(this,ot),e)}get params(){return O(A(this,st))}set params(e){N(A(this,st),e)}get route(){return O(A(this,it))}set route(e){N(A(this,it),e)}get state(){return O(A(this,ct))}set state(e){N(A(this,ct),e)}get status(){return O(A(this,lt))}set status(e){N(A(this,lt),e)}get url(){return O(A(this,ft))}set url(e){N(A(this,ft),e)}},rt=new WeakMap,at=new WeakMap,ot=new WeakMap,st=new WeakMap,it=new WeakMap,ct=new WeakMap,lt=new WeakMap,ft=new WeakMap,we),Q=new(_e=class{constructor(){C(this,ut,P(null))}get current(){return O(A(this,ut))}set current(e){N(A(this,ut),e)}},ut=new WeakMap,_e),Ot=new(ve=class{constructor(){C(this,dt,P(!1))}get current(){return O(A(this,dt))}set current(e){N(A(this,dt),e)}},dt=new WeakMap,ve),Ue.v=()=>Ot.current=!0);function Wt(t){Object.assign(k,t)}const In="/__data.json",Ln=".html__data.json";function Tn(t){return t.endsWith(".html")?t.replace(/\.html$/,Ln):t.replace(/\/$/,"")+In}const{onMount:xn,tick:Dt}=Je,Cn=new Set(["icon","shortcut icon","apple-touch-icon"]),F=be(Se)??{},tt=be(Ae)??{},D={url:le({}),page:le({}),navigating:qt(null),updated:hn()};function Yt(t){F[t]=kt()}function Pn(t,e){let n=t+1;for(;F[n];)delete F[n],n+=1;for(n=e+1;tt[n];)delete tt[n],n+=1}function K(t){return location.href=t.href,new Promise(()=>{})}async function xe(){if("serviceWorker"in navigator){const t=await navigator.serviceWorker.getRegistration(x||"/");t&&await t.update()}}function ue(){}let zt,Vt,_t,j,Bt,S;const vt=[],bt=[];let L=null;const mt=new Map,Xt=new Set,On=new Set,Y=new Set;let w={branch:[],error:null,url:null},Zt=!1,At=!1,de=!0,et=!1,J=!1,Ce=!1,Ut=!1,G,R,T,$;const z=new Set;let Nt;async function tr(t,e,n){var s,i,o,c;document.URL!==location.href&&(location.href=location.href),S=t,await((i=(s=t.hooks).init)==null?void 0:i.call(s)),zt=ln(t),j=document.documentElement,Bt=e,Vt=t.nodes[0],_t=t.nodes[1],Vt(),_t(),R=(o=history.state)==null?void 0:o[q],T=(c=history.state)==null?void 0:c[Z],R||(R=T=Date.now(),history.replaceState({...history.state,[q]:R,[Z]:T},""));const a=F[R];function r(){a&&(history.scrollRestoration="manual",scrollTo(a.x,a.y))}n?(r(),await Mn(Bt,n)):(await X({type:"enter",url:Ht(S.hash?qn(new URL(location.href)):location.href),replace_state:!0}),r()),Fn()}async function Pe(){if(await(Nt||(Nt=Promise.resolve())),!Nt)return;Nt=null;const t=$={},e=await gt(w.url,!0);L=null;const n=e&&await ee(e);if(!(!n||t!==$)){if(n.type==="redirect")return pt(new URL(n.location,w.url).href,{},1,t);n.props.page&&Object.assign(k,n.props.page),w=n.state,Oe(),G.$set(n.props),Wt(n.props.page)}}function Oe(){vt.length=0,Ut=!1}function Ne(t){bt.some(e=>e==null?void 0:e.snapshot)&&(tt[t]=bt.map(e=>{var n;return(n=e==null?void 0:e.snapshot)==null?void 0:n.capture()}))}function je(t){var e;(e=tt[t])==null||e.forEach((n,a)=>{var r,s;(s=(r=bt[a])==null?void 0:r.snapshot)==null||s.restore(n)})}function he(){Yt(R),ie(Se,F),Ne(T),ie(Ae,tt)}async function pt(t,e,n,a){return X({type:"goto",url:Ht(t),keepfocus:e.keepFocus,noscroll:e.noScroll,replace_state:e.replaceState,state:e.state,redirect_count:n,nav_token:a,accept:()=>{e.invalidateAll&&(Ut=!0),e.invalidate&&e.invalidate.forEach(Me)}})}async function Nn(t){if(t.id!==(L==null?void 0:L.id)){const e={};z.add(e),L={id:t.id,token:e,promise:ee({...t,preload:e}).then(n=>(z.delete(e),n.type==="loaded"&&n.state.error&&(L=null),n))}}return L.promise}async function jt(t){var n;const e=(n=await gt(t,!1))==null?void 0:n.route;e&&await Promise.all([...e.layouts,e.leaf].map(a=>a==null?void 0:a[1]()))}function $e(t,e,n){var r;w=t.state;const a=document.querySelector("style[data-sveltekit]");if(a&&a.remove(),Object.assign(k,t.props.page),G=new S.root({target:e,props:{...t.props,stores:D,components:bt},hydrate:n,sync:!1}),je(T),n){const s={from:null,to:{params:w.params,route:{id:((r=w.route)==null?void 0:r.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};Y.forEach(i=>i(s))}At=!0}function nt({url:t,params:e,branch:n,status:a,error:r,route:s,form:i}){let o="never";if(x&&(t.pathname===x||t.pathname===x+"/"))o="always";else for(const f of n)(f==null?void 0:f.slash)!==void 0&&(o=f.slash);t.pathname=We(t.pathname,o),t.search=t.search;const c={type:"loaded",state:{url:t,params:e,branch:n,error:r,route:s},props:{constructors:Sn(n).map(f=>f.node.component),page:Lt(k)}};i!==void 0&&(c.props.form=i);let l={},p=!k,u=0;for(let f=0;f<Math.max(n.length,w.branch.length);f+=1){const m=n[f],d=w.branch[f];(m==null?void 0:m.data)!==(d==null?void 0:d.data)&&(p=!0),m&&(l={...l,...m.data},p&&(c.props[`data_${u}`]=l),u+=1)}return(!w.url||t.href!==w.url.href||w.error!==r||i!==void 0&&i!==k.form||p)&&(c.props.page={error:r,params:e,route:{id:(s==null?void 0:s.id)??null},state:{},status:a,url:new URL(t),form:i??null,data:p?l:k.data}),c}async function Qt({loader:t,parent:e,url:n,params:a,route:r,server_data_node:s}){var p,u,y;let i=null,o=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},l=await t();if((p=l.universal)!=null&&p.load){let f=function(...d){for(const h of d){const{href:v}=new URL(h,n);c.dependencies.add(v)}};const m={route:new Proxy(r,{get:(d,h)=>(o&&(c.route=!0),d[h])}),params:new Proxy(a,{get:(d,h)=>(o&&c.params.add(h),d[h])}),data:(s==null?void 0:s.data)??null,url:Xe(n,()=>{o&&(c.url=!0)},d=>{o&&c.search_params.add(d)},S.hash),async fetch(d,h){d instanceof Request&&(h={body:d.method==="GET"||d.method==="HEAD"?void 0:await d.blob(),cache:d.cache,credentials:d.credentials,headers:[...d.headers].length>0?d==null?void 0:d.headers:void 0,integrity:d.integrity,keepalive:d.keepalive,method:d.method,mode:d.mode,redirect:d.redirect,referrer:d.referrer,referrerPolicy:d.referrerPolicy,signal:d.signal,...h});const{resolved:v,promise:U}=De(d,h,n);return o&&f(v.href),U},setHeaders:()=>{},depends:f,parent(){return o&&(c.parent=!0),e()},untrack(d){o=!1;try{return d()}finally{o=!0}}};i=await l.universal.load.call(null,m)??null}return{node:l,loader:t,server:s,universal:(u=l.universal)!=null&&u.load?{type:"data",data:i,uses:c}:null,data:i??(s==null?void 0:s.data)??null,slash:((y=l.universal)==null?void 0:y.trailingSlash)??(s==null?void 0:s.slash)}}function De(t,e,n){let a=t instanceof Request?t.url:t;const r=new URL(a,n);r.origin===n.origin&&(a=r.href.slice(n.origin.length));const s=At?nn(a,r.href,e):en(a,e);return{resolved:r,promise:s}}function pe(t,e,n,a,r,s){if(Ut)return!0;if(!r)return!1;if(r.parent&&t||r.route&&e||r.url&&n)return!0;for(const i of r.search_params)if(a.has(i))return!0;for(const i of r.params)if(s[i]!==w.params[i])return!0;for(const i of r.dependencies)if(vt.some(o=>o(new URL(i))))return!0;return!1}function te(t,e){return(t==null?void 0:t.type)==="data"?t:(t==null?void 0:t.type)==="skip"?e??null:null}function jn(t,e){if(!t)return new Set(e.searchParams.keys());const n=new Set([...t.searchParams.keys(),...e.searchParams.keys()]);for(const a of n){const r=t.searchParams.getAll(a),s=e.searchParams.getAll(a);r.every(i=>s.includes(i))&&s.every(i=>r.includes(i))&&n.delete(a)}return n}function ge({error:t,url:e,route:n,params:a}){return{type:"loaded",state:{error:t,url:e,route:n,params:a,branch:[]},props:{page:Lt(k),constructors:[]}}}async function ee({id:t,invalidating:e,url:n,params:a,route:r,preload:s}){if((L==null?void 0:L.id)===t)return z.delete(L.token),L.promise;const{errors:i,layouts:o,leaf:c}=r,l=[...o,c];i.forEach(g=>g==null?void 0:g().catch(()=>{})),l.forEach(g=>g==null?void 0:g[1]().catch(()=>{}));let p=null;const u=w.url?t!==St(w.url):!1,y=w.route?r.id!==w.route.id:!1,f=jn(w.url,n);let m=!1;const d=l.map((g,_)=>{var V;const b=w.branch[_],E=!!(g!=null&&g[0])&&((b==null?void 0:b.loader)!==g[1]||pe(m,y,u,f,(V=b.server)==null?void 0:V.uses,a));return E&&(m=!0),E});if(d.some(Boolean)){try{p=await qe(n,d)}catch(g){const _=await H(g,{url:n,params:a,route:{id:t}});return z.has(s)?ge({error:_,url:n,params:a,route:r}):It({status:wt(g),error:_,url:n,route:r})}if(p.type==="redirect")return p}const h=p==null?void 0:p.nodes;let v=!1;const U=l.map(async(g,_)=>{var Tt;if(!g)return;const b=w.branch[_],E=h==null?void 0:h[_];if((!E||E.type==="skip")&&g[1]===(b==null?void 0:b.loader)&&!pe(v,y,u,f,(Tt=b.universal)==null?void 0:Tt.uses,a))return b;if(v=!0,(E==null?void 0:E.type)==="error")throw E;return Qt({loader:g[1],url:n,params:a,route:r,parent:async()=>{var ae;const re={};for(let xt=0;xt<_;xt+=1)Object.assign(re,(ae=await U[xt])==null?void 0:ae.data);return re},server_data_node:te(E===void 0&&g[0]?{type:"skip"}:E??null,g[0]?b==null?void 0:b.server:void 0)})});for(const g of U)g.catch(()=>{});const I=[];for(let g=0;g<l.length;g+=1)if(l[g])try{I.push(await U[g])}catch(_){if(_ instanceof Kt)return{type:"redirect",location:_.location};if(z.has(s))return ge({error:await H(_,{params:a,url:n,route:{id:r.id}}),url:n,params:a,route:r});let b=wt(_),E;if(h!=null&&h.includes(_))b=_.status??b,E=_.error;else if(_ instanceof Rt)E=_.body;else{if(await D.updated.check())return await xe(),await K(n);E=await H(_,{params:a,url:n,route:{id:r.id}})}const V=await Ve(g,I,i);return V?nt({url:n,params:a,branch:I.slice(0,V.idx).concat(V.node),status:b,error:E,route:r}):await Fe(n,{id:r.id},E,b)}else I.push(void 0);return nt({url:n,params:a,branch:I,status:200,error:null,route:r,form:e?void 0:null})}async function Ve(t,e,n){for(;t--;)if(n[t]){let a=t;for(;!e[a];)a-=1;try{return{idx:a+1,node:{node:await n[t](),loader:n[t],data:{},server:null,universal:null}}}catch{continue}}}async function It({status:t,error:e,url:n,route:a}){const r={};let s=null;if(S.server_loads[0]===0)try{const o=await qe(n,[!0]);if(o.type!=="data"||o.nodes[0]&&o.nodes[0].type!=="data")throw 0;s=o.nodes[0]??null}catch{(n.origin!==ht||n.pathname!==location.pathname||Zt)&&await K(n)}try{const o=await Qt({loader:Vt,url:n,params:r,route:a,parent:()=>Promise.resolve({}),server_data_node:te(s)}),c={node:await _t(),loader:_t,universal:null,server:null,data:null};return nt({url:n,params:r,branch:[o,c],status:t,error:e,route:null})}catch(o){if(o instanceof Kt)return pt(new URL(o.location,location.href),{},0);throw o}}async function $n(t){const e=t.href;if(mt.has(e))return mt.get(e);let n;try{const a=(async()=>{let r=await S.hooks.reroute({url:new URL(t),fetch:async(s,i)=>De(s,i,t).promise})??t;if(typeof r=="string"){const s=new URL(t);S.hash?s.hash=r:s.pathname=r,r=s}return r})();mt.set(e,a),n=await a}catch{mt.delete(e);return}return n}async function gt(t,e){if(t&&!Et(t,x,S.hash)){const n=await $n(t);if(!n)return;const a=Dn(n);for(const r of zt){const s=r.exec(a);if(s)return{id:St(t),invalidating:e,route:r,params:ze(s),url:t}}}}function Dn(t){return Ye(S.hash?t.hash.replace(/^#/,"").replace(/[?#].+/,""):t.pathname.slice(x.length))||"/"}function St(t){return(S.hash?t.hash.replace(/^#/,""):t.pathname)+t.search}function Be({url:t,type:e,intent:n,delta:a}){let r=!1;const s=ne(w,n,t,e);a!==void 0&&(s.navigation.delta=a);const i={...s.navigation,cancel:()=>{r=!0,s.reject(new Error("navigation cancelled"))}};return et||Xt.forEach(o=>o(i)),r?null:s}async function X({type:t,url:e,popped:n,keepfocus:a,noscroll:r,replace_state:s,state:i={},redirect_count:o=0,nav_token:c={},accept:l=ue,block:p=ue}){const u=$;$=c;const y=await gt(e,!1),f=t==="enter"?ne(w,y,e,t):Be({url:e,type:t,delta:n==null?void 0:n.delta,intent:y});if(!f){p(),$===c&&($=u);return}const m=R,d=T;l(),et=!0,At&&f.navigation.type!=="enter"&&D.navigating.set(Q.current=f.navigation);let h=y&&await ee(y);if(!h){if(Et(e,x,S.hash))return await K(e);h=await Fe(e,{id:null},await H(new Jt(404,"Not Found",`Not found: ${e.pathname}`),{url:e,params:{},route:{id:null}}),404)}if(e=(y==null?void 0:y.url)||e,$!==c)return f.reject(new Error("navigation aborted")),!1;if(h.type==="redirect")if(o>=20)h=await It({status:500,error:await H(new Error("Redirect loop"),{url:e,params:{},route:{id:null}}),url:e,route:{id:null}});else return await pt(new URL(h.location,e).href,{},o+1,c),!1;else h.props.page.status>=400&&await D.updated.check()&&(await xe(),await K(e));if(Oe(),Yt(m),Ne(d),h.props.page.url.pathname!==e.pathname&&(e.pathname=h.props.page.url.pathname),i=n?n.state:i,!n){const g=s?0:1,_={[q]:R+=g,[Z]:T+=g,[ke]:i};(s?history.replaceState:history.pushState).call(history,_,"",e),s||Pn(R,T)}if(L=null,h.props.page.state=i,At){w=h.state,h.props.page&&(h.props.page.url=e);const g=(await Promise.all(Array.from(On,_=>_(f.navigation)))).filter(_=>typeof _=="function");if(g.length>0){let _=function(){g.forEach(b=>{Y.delete(b)})};g.push(_),g.forEach(b=>{Y.add(b)})}G.$set(h.props),Wt(h.props.page),Ce=!0}else $e(h,Bt,!1);const{activeElement:v}=document;await Dt();const U=n?n.scroll:r?kt():null;if(de){const g=e.hash&&document.getElementById(He(e));U?scrollTo(U.x,U.y):g?g.scrollIntoView():scrollTo(0,0)}const I=document.activeElement!==v&&document.activeElement!==document.body;!a&&!I&&Mt(e),de=!0,h.props.page&&Object.assign(k,h.props.page),et=!1,t==="popstate"&&je(T),f.fulfil(void 0),Y.forEach(g=>g(f.navigation)),D.navigating.set(Q.current=null)}async function Fe(t,e,n,a){return t.origin===ht&&t.pathname===location.pathname&&!Zt?await It({status:a,error:n,url:t,route:e}):await K(t)}function Vn(){let t,e,n;j.addEventListener("mousemove",o=>{const c=o.target;clearTimeout(t),t=setTimeout(()=>{s(c,B.hover)},20)});function a(o){o.defaultPrevented||s(o.composedPath()[0],B.tap)}j.addEventListener("mousedown",a),j.addEventListener("touchstart",a,{passive:!0});const r=new IntersectionObserver(o=>{for(const c of o)c.isIntersecting&&(jt(new URL(c.target.href)),r.unobserve(c.target))},{threshold:0});async function s(o,c){const l=Re(o,j),p=l===e&&c>=n;if(!l||p)return;const{url:u,external:y,download:f}=$t(l,x,S.hash);if(y||f)return;const m=yt(l),d=u&&St(w.url)===St(u);if(!(m.reload||d))if(c<=m.preload_data){e=l,n=B.tap;const h=await gt(u,!1);if(!h)return;Nn(h)}else c<=m.preload_code&&(e=l,n=c,jt(u))}function i(){r.disconnect();for(const o of j.querySelectorAll("a")){const{url:c,external:l,download:p}=$t(o,x,S.hash);if(l||p)continue;const u=yt(o);u.reload||(u.preload_code===B.viewport&&r.observe(o),u.preload_code===B.eager&&jt(c))}}Y.add(i),i()}function H(t,e){if(t instanceof Rt)return t.body;const n=wt(t),a=Rn(t);return S.hooks.handleError({error:t,event:e,status:n,message:a})??{message:a}}function Bn(t,e){xn(()=>(t.add(e),()=>{t.delete(e)}))}function er(t){Bn(Xt,t)}function nr(t,e={}){return t=new URL(Ht(t)),t.origin!==ht?Promise.reject(new Error("goto: invalid URL")):pt(t,e,0)}function rr(t){return Me(t),Pe()}function Me(t){if(typeof t=="function")vt.push(t);else{const{href:e}=new URL(t,location.href);vt.push(n=>n.href===e)}}function ar(){return Ut=!0,Pe()}async function or(t){if(t.type==="error"){const e=new URL(location.href),{branch:n,route:a}=w;if(!a)return;const r=await Ve(w.branch.length,n,a.errors);if(r){const s=nt({url:e,params:w.params,branch:n.slice(0,r.idx).concat(r.node),status:t.status??500,error:t.error,route:a});w=s.state,G.$set(s.props),Wt(s.props.page),Dt().then(()=>Mt(w.url))}}else t.type==="redirect"?await pt(t.location,{invalidateAll:!0},0):(k.form=t.data,k.status=t.status,G.$set({form:null,page:Lt(k)}),await Dt(),G.$set({form:t.data}),t.type==="success"&&Mt(k.url))}function Fn(){var e;history.scrollRestoration="manual",addEventListener("beforeunload",n=>{let a=!1;if(he(),!et){const r=ne(w,void 0,null,"leave"),s={...r.navigation,cancel:()=>{a=!0,r.reject(new Error("navigation cancelled"))}};Xt.forEach(i=>i(s))}a?(n.preventDefault(),n.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&he()}),(e=navigator.connection)!=null&&e.saveData||Vn(),j.addEventListener("click",async n=>{if(n.button||n.which!==1||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||n.defaultPrevented)return;const a=Re(n.composedPath()[0],j);if(!a)return;const{url:r,external:s,target:i,download:o}=$t(a,x,S.hash);if(!r)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=yt(a);if(!(a instanceof SVGAElement)&&r.protocol!==location.protocol&&!(r.protocol==="https:"||r.protocol==="http:")||o)return;const[p,u]=(S.hash?r.hash.replace(/^#/,""):r.href).split("#"),y=p===Ct(location);if(s||c.reload&&(!y||!u)){Be({url:r,type:"link"})?et=!0:n.preventDefault();return}if(u!==void 0&&y){const[,f]=w.url.href.split("#");if(f===u){if(n.preventDefault(),u===""||u==="top"&&a.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const m=a.ownerDocument.getElementById(decodeURIComponent(u));m&&(m.scrollIntoView(),m.focus())}return}if(J=!0,Yt(R),t(r),!c.replace_state)return;J=!1}n.preventDefault(),await new Promise(f=>{requestAnimationFrame(()=>{setTimeout(f,0)}),setTimeout(f,100)}),await X({type:"link",url:r,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??r.href===location.href})}),j.addEventListener("submit",n=>{if(n.defaultPrevented)return;const a=HTMLFormElement.prototype.cloneNode.call(n.target),r=n.submitter;if(((r==null?void 0:r.formTarget)||a.target)==="_blank"||((r==null?void 0:r.formMethod)||a.method)!=="get")return;const o=new URL((r==null?void 0:r.hasAttribute("formaction"))&&(r==null?void 0:r.formAction)||a.action);if(Et(o,x,!1))return;const c=n.target,l=yt(c);if(l.reload)return;n.preventDefault(),n.stopPropagation();const p=new FormData(c),u=r==null?void 0:r.getAttribute("name");u&&p.append(u,(r==null?void 0:r.getAttribute("value"))??""),o.search=new URLSearchParams(p).toString(),X({type:"form",url:o,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??o.href===location.href})}),addEventListener("popstate",async n=>{var a;if(!Ft){if((a=n.state)!=null&&a[q]){const r=n.state[q];if($={},r===R)return;const s=F[r],i=n.state[ke]??{},o=new URL(n.state[dn]??location.href),c=n.state[Z],l=w.url?Ct(location)===Ct(w.url):!1;if(c===T&&(Ce||l)){i!==k.state&&(k.state=i),t(o),F[R]=kt(),s&&scrollTo(s.x,s.y),R=r;return}const u=r-R;await X({type:"popstate",url:o,popped:{state:i,scroll:s,delta:u},accept:()=>{R=r,T=c},block:()=>{history.go(-u)},nav_token:$})}else if(!J){const r=new URL(location.href);t(r),S.hash&&location.reload()}}}),addEventListener("hashchange",()=>{J&&(J=!1,history.replaceState({...history.state,[q]:++R,[Z]:T},"",location.href))});for(const n of document.querySelectorAll("link"))Cn.has(n.rel)&&(n.href=n.href);addEventListener("pageshow",n=>{n.persisted&&D.navigating.set(Q.current=null)});function t(n){w.url=k.url=n,D.page.set(Lt(k)),D.page.notify()}}async function Mn(t,{status:e=200,error:n,node_ids:a,params:r,route:s,server_route:i,data:o,form:c}){Zt=!0;const l=new URL(location.href);let p;({params:r={},route:s={id:null}}=await gt(l,!1)||{}),p=zt.find(({id:f})=>f===s.id);let u,y=!0;try{const f=a.map(async(d,h)=>{const v=o[h];return v!=null&&v.uses&&(v.uses=Ge(v.uses)),Qt({loader:S.nodes[d],url:l,params:r,route:s,parent:async()=>{const U={};for(let I=0;I<h;I+=1)Object.assign(U,(await f[I]).data);return U},server_data_node:te(v)})}),m=await Promise.all(f);if(p){const d=p.layouts;for(let h=0;h<d.length;h++)d[h]||m.splice(h,0,void 0)}u=nt({url:l,params:r,branch:m,status:e,error:n,form:c,route:p??null})}catch(f){if(f instanceof Kt){await K(new URL(f.location,location.href));return}u=await It({status:wt(f),error:await H(f,{url:l,params:r,route:s}),url:l,route:s}),t.textContent="",y=!1}u.props.page&&(u.props.page.state={}),$e(u,t,y)}async function qe(t,e){var s;const n=new URL(t);n.pathname=Tn(t.pathname),t.pathname.endsWith("/")&&n.searchParams.append(En,"1"),n.searchParams.append(kn,e.map(i=>i?"1":"0").join(""));const a=window.fetch,r=await a(n.href,{});if(!r.ok){let i;throw(s=r.headers.get("content-type"))!=null&&s.includes("application/json")?i=await r.json():r.status===404?i="Not Found":r.status===500&&(i="Internal Error"),new Rt(r.status,i)}return new Promise(async i=>{var y;const o=new Map,c=r.body.getReader(),l=new TextDecoder;function p(f){return Le(f,{...S.decoders,Promise:m=>new Promise((d,h)=>{o.set(m,{fulfil:d,reject:h})})})}let u="";for(;;){const{done:f,value:m}=await c.read();if(f&&!u)break;for(u+=!m&&u?`
`:l.decode(m,{stream:!0});;){const d=u.indexOf(`
`);if(d===-1)break;const h=JSON.parse(u.slice(0,d));if(u=u.slice(d+1),h.type==="redirect")return i(h);if(h.type==="data")(y=h.nodes)==null||y.forEach(v=>{(v==null?void 0:v.type)==="data"&&(v.uses=Ge(v.uses),v.data=p(v.data))}),i(h);else if(h.type==="chunk"){const{id:v,data:U,error:I}=h,g=o.get(v);o.delete(v),I?g.reject(p(I)):g.fulfil(p(U))}}}})}function Ge(t){return{dependencies:new Set((t==null?void 0:t.dependencies)??[]),params:new Set((t==null?void 0:t.params)??[]),parent:!!(t!=null&&t.parent),route:!!(t!=null&&t.route),url:!!(t!=null&&t.url),search_params:new Set((t==null?void 0:t.search_params)??[])}}let Ft=!1;function Mt(t){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const n=He(t);if(n&&document.getElementById(n)){const{x:r,y:s}=kt();setTimeout(()=>{const i=history.state;Ft=!0,location.replace(`#${n}`),S.hash&&location.replace(t.hash),history.replaceState(i,"",t.hash),scrollTo(r,s),Ft=!1})}else{const r=document.body,s=r.getAttribute("tabindex");r.tabIndex=-1,r.focus({preventScroll:!0,focusVisible:!1}),s!==null?r.setAttribute("tabindex",s):r.removeAttribute("tabindex")}const a=getSelection();if(a&&a.type!=="None"){const r=[];for(let s=0;s<a.rangeCount;s+=1)r.push(a.getRangeAt(s));setTimeout(()=>{if(a.rangeCount===r.length){for(let s=0;s<a.rangeCount;s+=1){const i=r[s],o=a.getRangeAt(s);if(i.commonAncestorContainer!==o.commonAncestorContainer||i.startContainer!==o.startContainer||i.endContainer!==o.endContainer||i.startOffset!==o.startOffset||i.endOffset!==o.endOffset)return}a.removeAllRanges()}})}}}function ne(t,e,n,a){var c,l;let r,s;const i=new Promise((p,u)=>{r=p,s=u});return i.catch(()=>{}),{navigation:{from:{params:t.params,route:{id:((c=t.route)==null?void 0:c.id)??null},url:t.url},to:n&&{params:(e==null?void 0:e.params)??null,route:{id:((l=e==null?void 0:e.route)==null?void 0:l.id)??null},url:n},willUnload:!e,type:a,complete:i},fulfil:r,reject:s}}function Lt(t){return{data:t.data,error:t.error,form:t.form,params:t.params,route:t.route,state:t.state,status:t.status,url:t.url}}function qn(t){const e=new URL(t);return e.hash=decodeURIComponent(t.hash),e}function He(t){let e;if(S.hash){const[,,n]=t.hash.split("#",3);e=n??""}else e=t.hash.slice(1);return decodeURIComponent(e)}export{Rt as H,wn as N,_n as P,mn as U,vn as a,bn as b,yn as c,S as d,Yn as e,ar as f,nr as g,or as h,rr as i,er as j,tr as k,Wn as l,zn as p,D as s};
