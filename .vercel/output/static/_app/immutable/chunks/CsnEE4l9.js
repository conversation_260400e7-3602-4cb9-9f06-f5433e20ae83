import{au as ee,K as ae,av as Q,F as y,w as m,I as re,x as ne,j as U,d as ie,Q as fe,S as le,T as Y,U as R,A as D,C as se,aw as ue,V as W,N as X,W as te,ax as g,ay as J,_ as ve,y as z,az as O,aA as G,aB as b,m as de,a8 as K,aC as _e,aD as oe,aE as ce,aF as he,aG as Ee,P as pe,aH as Ae,B as Te}from"./wnqW1tdD.js";function Ne(l,e){return e}function xe(l,e,a,u){for(var v=[],_=e.length,t=0;t<_;t++)ce(e[t].e,v,!0);var o=_>0&&v.length===0&&a!==null;if(o){var A=a.parentNode;he(A),A.append(a),u.clear(),C(l,e[0].prev,e[_-1].next)}Ee(v,()=>{for(var h=0;h<_;h++){var d=e[h];o||(u.delete(d.k),C(l,d.prev,d.next)),pe(d.e,!o)}})}function we(l,e,a,u,v,_=null){var t=l,o={flags:e,items:new Map,first:null},A=(e&Q)!==0;if(A){var h=l;t=m?y(re(h)):h.appendChild(ee())}m&&ne();var d=null,N=!1,i=ie(()=>{var s=a();return _e(s)?s:s==null?[]:J(s)});ae(()=>{var s=U(i),r=s.length;if(N&&r===0)return;N=r===0;let x=!1;if(m){var E=fe(t)===le;E!==(r===0)&&(t=Y(),y(t),R(!1),x=!0)}if(m){for(var p=null,T,c=0;c<r;c++){if(D.nodeType===se&&D.data===ue){t=D,x=!0,R(!1);break}var n=s[c],f=u(n,c);T=Z(D,o,p,null,n,f,c,v,e,a),o.items.set(f,T),p=T}r>0&&y(Y())}m||Ie(s,o,t,v,e,u,a),_!==null&&(r===0?d?W(d):d=X(()=>_(t)):d!==null&&te(d,()=>{d=null})),x&&R(!0),U(i)}),m&&(t=D)}function Ie(l,e,a,u,v,_,t){var L,B,V,q;var o=(v&oe)!==0,A=(v&(O|b))!==0,h=l.length,d=e.items,N=e.first,i=N,s,r=null,x,E=[],p=[],T,c,n,f;if(o)for(f=0;f<h;f+=1)T=l[f],c=_(T,f),n=d.get(c),n!==void 0&&((L=n.a)==null||L.measure(),(x??(x=new Set)).add(n));for(f=0;f<h;f+=1){if(T=l[f],c=_(T,f),n=d.get(c),n===void 0){var $=i?i.e.nodes_start:a;r=Z($,e,r,r===null?e.first:r.next,T,c,f,u,v,t),d.set(c,r),E=[],p=[],i=r.next;continue}if(A&&Ce(n,T,f,v),n.e.f&g&&(W(n.e),o&&((B=n.a)==null||B.unfix(),(x??(x=new Set)).delete(n))),n!==i){if(s!==void 0&&s.has(n)){if(E.length<p.length){var H=p[0],I;r=H.prev;var k=E[0],M=E[E.length-1];for(I=0;I<E.length;I+=1)P(E[I],H,a);for(I=0;I<p.length;I+=1)s.delete(p[I]);C(e,k.prev,M.next),C(e,r,k),C(e,M,H),i=H,r=M,f-=1,E=[],p=[]}else s.delete(n),P(n,i,a),C(e,n.prev,n.next),C(e,n,r===null?e.first:r.next),C(e,r,n),r=n;continue}for(E=[],p=[];i!==null&&i.k!==c;)i.e.f&g||(s??(s=new Set)).add(i),p.push(i),i=i.next;if(i===null)continue;n=i}E.push(n),r=n,i=n.next}if(i!==null||s!==void 0){for(var w=s===void 0?[]:J(s);i!==null;)i.e.f&g||w.push(i),i=i.next;var S=w.length;if(S>0){var j=v&Q&&h===0?a:null;if(o){for(f=0;f<S;f+=1)(V=w[f].a)==null||V.measure();for(f=0;f<S;f+=1)(q=w[f].a)==null||q.fix()}xe(e,w,j,d)}}o&&ve(()=>{var F;if(x!==void 0)for(n of x)(F=n.a)==null||F.apply()}),z.first=e.first&&e.first.e,z.last=r&&r.e}function Ce(l,e,a,u){u&O&&G(l.v,e),u&b?G(l.i,a):l.i=a}function Z(l,e,a,u,v,_,t,o,A,h){var d=(A&O)!==0,N=(A&Ae)===0,i=d?N?de(v,!1,!1):K(v):v,s=A&b?K(t):t,r={i:s,v:i,k:_,a:null,e:null,prev:a,next:u};try{return r.e=X(()=>o(l,i,s,h),m),r.e.prev=a&&a.e,r.e.next=u&&u.e,a===null?e.first=r:(a.next=r,a.e.next=r.e),u!==null&&(u.prev=r,u.e.prev=r.e),r}finally{}}function P(l,e,a){for(var u=l.next?l.next.e.nodes_start:a,v=e?e.e.nodes_start:a,_=l.e.nodes_start;_!==u;){var t=Te(_);v.before(_),_=t}}function C(l,e,a){e===null?l.first=a:(e.next=a,e.e.next=a&&a.e),a!==null&&(a.prev=e,a.e.prev=e&&e.e)}export{we as e,Ne as i};
