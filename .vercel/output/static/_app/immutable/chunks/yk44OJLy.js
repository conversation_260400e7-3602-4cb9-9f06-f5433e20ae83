import{K as _,w as i,x as d,G as l,E as u,L as h,M as m,N as v,O as y,P as g,A as c,I as E}from"./wnqW1tdD.js";function w(t,s,...n){var a=t,e=y,r;_(()=>{e!==(e=s())&&(r&&(g(r),r=null),r=v(()=>e(a,...n)))},m),i&&(a=c)}function x(t){return(s,...n)=>{var o;var a=t(...n),e;if(i)e=c,d();else{var r=a.render().trim(),p=l(r);e=E(p),s.before(e)}const f=(o=a.setup)==null?void 0:o.call(a,e);u(e,e),typeof f=="function"&&h(f)}}export{x as c,w as s};
