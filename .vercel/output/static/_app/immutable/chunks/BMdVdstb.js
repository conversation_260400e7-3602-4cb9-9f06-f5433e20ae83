import{a as d}from"./Bz0_kaay.js";import{t as x}from"./CVCfOWck.js";import{c as p}from"./BCJ65Txv.js";function A(...c){return x(d(c))}const S=(c,o={y:-8,x:0,start:.95,duration:150})=>{const a=getComputedStyle(c),i=a.transform==="none"?"":a.transform,s=(t,r,n)=>{const[e,f]=r,[u,l]=n;return(t-e)/(f-e)*(l-u)+u},m=t=>Object.keys(t).reduce((r,n)=>t[n]===void 0?r:r+`${n}:${t[n]};`,"");return{duration:o.duration??200,delay:0,css:t=>{const r=s(t,[0,1],[o.y??5,0]),n=s(t,[0,1],[o.x??0,0]),e=s(t,[0,1],[o.start??.95,1]);return m({transform:`${i} translate3d(${n}px, ${r}px, 0) scale(${e})`,opacity:t})},easing:p}};export{A as c,S as f};
