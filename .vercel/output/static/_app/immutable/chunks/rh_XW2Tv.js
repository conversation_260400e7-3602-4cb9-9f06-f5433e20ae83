import{K as Y,P as M,N as j,Y as I,w as k,L as D,aC as F,aV as G,aW as W,a4 as X,aX as Z,aY as J,aZ as Q,j as m,a_ as x,a$ as rr,U as $,b0 as ar}from"./wnqW1tdD.js";import{l as er,c as ir,f as sr,d as tr,g as ur,n as fr,j as lr,k as or}from"./CDPCzm7q.js";import{t as cr,c as nr,s as _r}from"./Bz0_kaay.js";function dr(r,a){var e=void 0,i;Y(()=>{e!==(e=a())&&(i&&(M(i),i=null),e&&(i=j(()=>{I(()=>e(r))})))})}function C(r,a={},e,i){for(var s in e){var f=e[s];a[s]!==f&&(e[s]==null?r.style.removeProperty(s):r.style.setProperty(s,f,i))}}function vr(r,a,e,i){var s=r.__style;if(k||s!==a){var f=cr(a,i);(!k||f!==r.getAttribute("style"))&&(f==null?r.removeAttribute("style"):r.style.cssText=f),r.__style=a}else i&&(Array.isArray(i)?(C(r,e==null?void 0:e[0],i[0]),C(r,e==null?void 0:e[1],i[1],"important")):C(r,e,i));return i}function L(r,a,e){if(r.multiple){if(a==null)return;if(!F(a))return G();for(var i of r.options)i.selected=a.includes(T(i));return}for(i of r.options){var s=T(i);if(W(s,a)){i.selected=!0;return}}(!e||a!==void 0)&&(r.selectedIndex=-1)}function U(r){var a=new MutationObserver(()=>{L(r,r.__value)});a.observe(r,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),D(()=>{a.disconnect()})}function Sr(r,a,e=a){var i=!0;er(r,"change",s=>{var f=s?"[selected]":":checked",d;if(r.multiple)d=[].map.call(r.querySelectorAll(f),T);else{var v=r.querySelector(f)??r.querySelector("option:not([disabled])");d=v&&T(v)}e(d)}),I(()=>{var s=a();if(L(r,s,i),i&&s===void 0){var f=r.querySelector(":checked");f!==null&&(s=T(f),e(s))}r.__value=s,i=!1}),U(r)}function T(r){return"__value"in r?r.__value:r.value}const w=Symbol("class"),N=Symbol("style"),V=Symbol("is custom element"),H=Symbol("is html");function wr(r){if(k){var a=!1,e=()=>{if(!a){if(a=!0,r.hasAttribute("value")){var i=r.value;p(r,"value",null),r.value=i}if(r.hasAttribute("checked")){var s=r.checked;p(r,"checked",null),r.checked=s}}};r.__on_r=e,ar(e),lr()}}function br(r,a){a?r.hasAttribute("selected")||r.setAttribute("selected",""):r.removeAttribute("selected")}function p(r,a,e,i){var s=K(r);k&&(s[a]=r.getAttribute(a),a==="src"||a==="srcset"||a==="href"&&r.nodeName==="LINK")||s[a]!==(s[a]=e)&&(a==="loading"&&(r[Z]=e),e==null?r.removeAttribute(a):typeof e!="string"&&R(r).includes(a)?r[a]=e:r.setAttribute(a,e))}function hr(r,a,e,i,s=!1){var f=K(r),d=f[V],v=!f[H];let b=k&&d;b&&$(!1);var l=a||{},y=r.tagName==="OPTION";for(var S in a)S in e||(e[S]=null);e.class?e.class=nr(e.class):(i||e[w])&&(e.class=null),e[N]&&(e.style??(e.style=null));var h=R(r);for(const t in e){let u=e[t];if(y&&t==="value"&&u==null){r.value=r.__value="",l[t]=u;continue}if(t==="class"){var E=r.namespaceURI==="http://www.w3.org/1999/xhtml";_r(r,E,u,i,a==null?void 0:a[w],e[w]),l[t]=u,l[w]=e[w];continue}if(t==="style"){vr(r,u,a==null?void 0:a[N],e[N]),l[t]=u,l[N]=e[N];continue}var A=l[t];if(!(u===A&&!(u===void 0&&r.hasAttribute(t)))){l[t]=u;var c=t[0]+t[1];if(c!=="$$")if(c==="on"){const n={},g="$$"+t;let o=t.slice(2);var O=or(o);if(ir(o)&&(o=o.slice(0,-7),n.capture=!0),!O&&A){if(u!=null)continue;r.removeEventListener(o,l[g],n),l[g]=null}if(u!=null)if(O)r[`__${o}`]=u,tr([o]);else{let B=function(z){l[t].call(this,z)};l[g]=sr(o,r,B,n)}else O&&(r[`__${o}`]=void 0)}else if(t==="style")p(r,t,u);else if(t==="autofocus")ur(r,!!u);else if(!d&&(t==="__value"||t==="value"&&u!=null))r.value=r.__value=u;else if(t==="selected"&&y)br(r,u);else{var _=t;v||(_=fr(_));var P=_==="defaultValue"||_==="defaultChecked";if(u==null&&!d&&!P)if(f[t]=null,_==="value"||_==="checked"){let n=r;const g=a===void 0;if(_==="value"){let o=n.defaultValue;n.removeAttribute(_),n.defaultValue=o,n.value=n.__value=g?o:null}else{let o=n.defaultChecked;n.removeAttribute(_),n.defaultChecked=o,n.checked=g?o:!1}}else r.removeAttribute(t);else P||h.includes(_)&&(d||typeof u!="string")?r[_]=u:typeof u!="function"&&p(r,_,u)}}}return b&&$(!0),l}function Nr(r,a,e=[],i,s=!1,f=X){const d=e.map(f);var v=void 0,b={},l=r.nodeName==="SELECT",y=!1;if(Y(()=>{var h=a(...d.map(m)),E=hr(r,v,h,i,s);y&&l&&"value"in h&&L(r,h.value,!1);for(let c of Object.getOwnPropertySymbols(b))h[c]||M(b[c]);for(let c of Object.getOwnPropertySymbols(h)){var A=h[c];c.description===x&&(!v||A!==v[c])&&(b[c]&&M(b[c]),b[c]=j(()=>dr(r,()=>A))),E[c]=A}v=E}),l){var S=r;I(()=>{L(S,v.value),U(S)})}y=!0}function K(r){return r.__attributes??(r.__attributes={[V]:r.nodeName.includes("-"),[H]:r.namespaceURI===J})}var q=new Map;function R(r){var a=q.get(r.nodeName);if(a)return a;q.set(r.nodeName,a=[]);for(var e,i=r,s=Element.prototype;s!==i;){e=rr(i);for(var f in e)e[f].set&&a.push(f);i=Q(i)}return a}export{Nr as a,Sr as b,vr as c,wr as r,p as s};
