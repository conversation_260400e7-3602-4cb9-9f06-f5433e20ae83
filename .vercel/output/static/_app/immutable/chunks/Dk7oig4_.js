import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{p as f,f as m,i as p,aM as e,c as v,r as u,a as _,g as $}from"./wnqW1tdD.js";import{s as g}from"./BDqVm3Gq.js";import{a as h}from"./rh_XW2Tv.js";import{i as b}from"./BxG_UISn.js";import{l as r,p as y}from"./Cmdkv-7M.js";import{c as o}from"./BMdVdstb.js";var C=m("<div><!></div>");function A(d,a){const i=r(a,["children","$$slots","$$events","$$legacy"]),l=r(i,["class"]);f(a,!1);let t=y(a,"class",8,void 0);b();var s=C();h(s,c=>({class:c,...l}),[()=>(e(o),e(t()),p(()=>o("bg-card text-card-foreground rounded-lg border shadow-sm",t())))]);var n=v(s);g(n,a,"default",{},null),u(s),_(d,s),$()}var x=m("<div><!></div>");function B(d,a){const i=r(a,["children","$$slots","$$events","$$legacy"]),l=r(i,["class"]);f(a,!1);let t=y(a,"class",8,void 0);b();var s=x();h(s,c=>({class:c,...l}),[()=>(e(o),e(t()),p(()=>o("p-6 pt-0",t())))]);var n=v(s);g(n,a,"default",{},null),u(s),_(d,s),$()}export{A as C,B as a};
