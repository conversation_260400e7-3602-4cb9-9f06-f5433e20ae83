import * as universal from '../entries/pages/(admin)/_layout.ts.js';
import * as server from '../entries/pages/(admin)/_layout.server.ts.js';

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/(admin)/+layout.ts";
export { server };
export const server_id = "src/routes/(admin)/+layout.server.ts";
export const imports = ["_app/immutable/nodes/2.D4AEHx_R.js","_app/immutable/chunks/BH_5by7C.js","_app/immutable/chunks/BODTVNWu.js","_app/immutable/chunks/C4iS2aBk.js","_app/immutable/chunks/BYyWxyJW.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/wnqW1tdD.js","_app/immutable/chunks/yk44OJLy.js"];
export const stylesheets = [];
export const fonts = [];
