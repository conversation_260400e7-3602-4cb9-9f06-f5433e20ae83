import"../chunks/CWj6FrbW.js";import{b as w,e as m,a as s,p as G,f,i as ha,aM as M,c as k,r as S,g as J,s as v,$ as _a,J as O,j as p,k as q,t as ga,n as Y,v as wa}from"../chunks/wnqW1tdD.js";import{d as $a,h as ba,s as Pa}from"../chunks/CDPCzm7q.js";import{i as C}from"../chunks/BjRbZGyQ.js";import{c as g}from"../chunks/ojdN50pv.js";import{s as ya}from"../chunks/Bz0_kaay.js";import{b as xa}from"../chunks/D5U2DSnR.js";import{a as Ca,s as ka}from"../chunks/D5ITLM2v.js";import{p as Sa}from"../chunks/B6j3ckjV.js";import{S as Ta,A as Aa}from"../chunks/Bb-q3EXb.js";import{a as Ea,C as Fa}from"../chunks/Dk7oig4_.js";import"../chunks/Cvx8ZW61.js";import"../chunks/CSxrUSuj.js";import{b as Na}from"../chunks/uGb05Syq.js";import{c as Ba}from"../chunks/Py3C97LR.js";import{g as Va}from"../chunks/DyGaIYLH.js";import{s as D}from"../chunks/BDqVm3Gq.js";import{l as T,s as za,p as Ia}from"../chunks/Cmdkv-7M.js";import{I as Ma}from"../chunks/CX_t0Ed_.js";import{a as Oa}from"../chunks/rh_XW2Tv.js";import{i as Ya}from"../chunks/BxG_UISn.js";import{c as j}from"../chunks/BMdVdstb.js";function ja(c,t){const r=T(t,["children","$$slots","$$events","$$legacy"]),d=[["path",{d:"M20 6 9 17l-5-5"}]];Ma(c,za({name:"check"},()=>r,{get iconNode(){return d},children:(i,a)=>{var n=w(),l=m(n);D(l,t,"default",{},null),s(i,n)},$$slots:{default:!0}}))}var Ga=f("<div><!></div>");function Ja(c,t){const r=T(t,["children","$$slots","$$events","$$legacy"]),d=T(r,["class"]);G(t,!1);let i=Ia(t,"class",8,void 0);Ya();var a=Ga();Oa(a,l=>({class:l,...d}),[()=>(M(j),M(i()),ha(()=>j("text-sm [&_p]:leading-relaxed",i())))]);var n=k(a);D(n,t,"default",{},null),S(a),s(c,a),J()}const qa=(c,t,r,d,i,a,n)=>{var h;p(t)&&(p(t).disabled=!0,p(t).textContent="Sending...");let l=r==null?void 0:r.user.email;l&&d.auth.resetPasswordForEmail(l,{redirectTo:`${i().url.origin}/auth/callback?next=%2Fdashboard%2F${(h=a.value)==null?void 0:h.name}%2Fsettings%2Freset_password`}).then($=>{q(n,!$.error,!0)})};var Da=f(`<div class="font-bold">Set Password By Email</div> <div>You use oAuth to sign in ("Sign in with Github" or similar). You can
            continue to access your account using only oAuth if you like!</div>`,1),Ha=f('<div class="font-bold">Change Password By Email</div>'),Ka=f("<!> <!>",1),La=f('<div class="flex flex-col gap-y-4"><!> <div> </div> <button>Send Set Password Email</button> <!></div>'),Qa=f('<h1 class="text-2xl font-bold mb-6">Change Password</h1> <!>',1);function bt(c,t){var E,F;G(t,!0);const[r,d]=Ca(),i=()=>ka(Sa,"$page",r);let{session:a,supabase:n}=t.data;const l=Va();let h=!!(a!=null&&a.user.user_metadata.hasPassword),$=!!["google","github"].includes(((F=(E=a==null?void 0:a.user)==null?void 0:E.app_metadata)==null?void 0:F.provider)??""),b=O(void 0),P=O(!1);var A=Qa();ba(u=>{_a.title="Change Password"});var H=v(m(A),2);{var K=u=>{Ta(u,{get data(){return t.data.form},get schema(){return Ba},title:"Change Password",editable:!0,saveButtonTitle:"Change Password",successTitle:"Password Changed",successBody:"On next sign in, use your new password.",formTarget:"/api?/updatePassword",fields:[{id:"newPassword1",label:"New Password",initialValue:"",inputType:"password"},{id:"newPassword2",label:"Confirm New Password",initialValue:"",inputType:"password"},{id:"currentPassword",label:"Current Password",initialValue:"",inputType:"password"}]})},L=u=>{var N=w(),Q=m(N);g(Q,()=>Fa,(R,U)=>{U(R,{children:(W,Ra)=>{var B=w(),X=m(B);g(X,()=>Ea,(Z,aa)=>{aa(Z,{children:(ta,Ua)=>{var y=La(),V=k(y);{var ea=e=>{var o=Da();Y(2),s(e,o)},sa=e=>{var o=Ha();s(e,o)};C(V,e=>{$?e(ea):e(sa,!1)})}var x=v(V,2),ra=k(x);S(x);var _=v(x,2);_.__click=[qa,b,a,n,i,l,P],xa(_,e=>q(b,e),()=>p(b));var oa=v(_,2);{var ia=e=>{var o=w(),na=m(o);g(na,()=>Aa,(la,da)=>{da(la,{children:(ua,Wa)=>{var z=Ka(),I=m(z);ja(I,{class:"h-4 w-4 "});var ca=v(I,2);g(ca,()=>Ja,(ma,pa)=>{pa(ma,{children:(fa,Xa)=>{Y();var va=wa(`Sent email! Please check your inbox and use the link to set your
              password.`);s(fa,va)},$$slots:{default:!0}})}),s(ua,z)},$$slots:{default:!0}})}),s(e,o)};C(oa,e=>{p(P)&&e(ia)})}S(y),ga(e=>{var o;Pa(ra,`The button below will send you an email at ${((o=a==null?void 0:a.user)==null?void 0:o.email)??""} which
          will allow you to set your password.`),ya(_,1,`${e??""} ${p(P)?"hidden":""}`)},[()=>Na({variant:"outline"})]),s(ta,y)},$$slots:{default:!0}})}),s(W,B)},$$slots:{default:!0}})}),s(u,N)};C(H,u=>{h?u(K):u(L,!1)})}s(c,A),J(),d()}$a(["click"]);export{bt as component};
