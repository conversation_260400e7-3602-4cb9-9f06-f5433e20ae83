import"../chunks/CWj6FrbW.js";import{o as p}from"../chunks/CfBaWyh2.js";import{p as m,u as f,b as c,e as d,a as s,g as h}from"../chunks/wnqW1tdD.js";import{s as b}from"../chunks/yk44OJLy.js";import{i as l}from"../chunks/COZ5WnQL.js";function M(n,a){m(a,!0);let{supabase:r,session:t}=a.data;f(()=>{({supabase:r,session:t}=a.data)}),p(()=>{const{data:i}=r.auth.onAuthStateChange((v,e)=>{(e==null?void 0:e.expires_at)!==(t==null?void 0:t.expires_at)&&l("supabase:auth")});return()=>i.subscription.unsubscribe()});var u=c(),o=d(u);b(o,()=>a.children),s(n,u),h()}export{M as component};
