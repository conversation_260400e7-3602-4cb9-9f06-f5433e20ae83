import"../chunks/CWj6FrbW.js";import{p as D,f as m,s as o,e as x,a as g,g as j,$ as z,j as i,o as s,c as A,n as M,r as U,t as W}from"../chunks/wnqW1tdD.js";import{h as Y}from"../chunks/CDPCzm7q.js";import{i as Z}from"../chunks/BjRbZGyQ.js";import{c as q,s as F}from"../chunks/Bz0_kaay.js";import{S as r}from"../chunks/Bb-q3EXb.js";import{g as G}from"../chunks/DyGaIYLH.js";import{b as H}from"../chunks/uGb05Syq.js";var I=m("<!> <!> <!> <!> <!> <!>",1),J=m('<p>You need to <a href="/login/sign_up">sign up</a> to update your settings</p>'),K=m('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function se(S,c){D(c,!0);let{profile:t,auth:b}=c.data;const l=G();var p=K();Y(n=>{z.title="Settings"});var B=o(x(p),2);{var k=n=>{var a=I(),d=x(a);const u=s(()=>[{id:"fullName",label:"Name",initialValue:(t==null?void 0:t.full_name)??""},{id:"companyName",label:"Company Name",initialValue:(t==null?void 0:t.company_name)??""},{id:"website",label:"Company Website",initialValue:(t==null?void 0:t.website)??""}]),w=s(()=>{var e;return(e=l.value)==null?void 0:e.slug});r(d,{title:"Profile",editable:!1,get fields(){return i(u)},editButtonTitle:"Edit Profile",get editLink(){return`/dashboard/${i(w)??""}/settings/edit_profile`}});var _=o(d,2);const y=s(()=>{var e;return[{id:"email",initialValue:((e=b.user)==null?void 0:e.email)||""}]}),L=s(()=>{var e;return(e=l.value)==null?void 0:e.slug});r(_,{title:"Email",editable:!1,get fields(){return i(y)},editButtonTitle:"Change Email",get editLink(){return`/dashboard/${i(L)??""}/settings/change_email`}});var f=o(_,2);const T=s(()=>{var e;return(e=l.value)==null?void 0:e.slug});r(f,{title:"Password",editable:!1,fields:[{id:"password",initialValue:"••••••••••••••••"}],editButtonTitle:"Change Password",get editLink(){return`/dashboard/${i(T)??""}/settings/change_password`}});var v=o(f,2);const $=s(()=>[{id:"subscriptionStatus",initialValue:t!=null&&t.unsubscribed?"Unsubscribed":"Subscribed"}]),C=s(()=>{var e;return(e=l.value)==null?void 0:e.slug});r(v,{title:"Email Subscription",editable:!1,get fields(){return i($)},editButtonTitle:"Change Subscription",get editLink(){return`/dashboard/${i(C)??""}/settings/change_email_subscription`}});var h=o(v,2);const E=s(()=>{var e;return(e=l.value)==null?void 0:e.slug});r(h,{title:"Billing",editable:!1,fields:[],editButtonTitle:"Manage Billing",get editLink(){return`/dashboard/${i(E)??""}/billing`}});var N=o(h,2);const P=s(()=>{var e;return(e=l.value)==null?void 0:e.slug});r(N,{title:"Danger Zone",editable:!1,dangerous:!0,fields:[],editButtonTitle:"Delete Account",get editLink(){return`/dashboard/${i(P)??""}/settings/delete_account`}}),g(n,a)},V=n=>{var a=J(),d=o(A(a));M(),U(a),W(u=>F(d,1,u),[()=>q(H({size:"sm"}))]),g(n,a)};Z(B,n=>{var a;(a=b.user)!=null&&a.is_anonymous?n(V,!1):n(k)})}g(S,p),j()}export{se as component};
