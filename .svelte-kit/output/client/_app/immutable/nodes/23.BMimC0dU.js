import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{o as Rr}from"../chunks/CfBaWyh2.js";import{b as ke,e as $e,a as p,p as Ir,l as Dr,h as Nr,f as u,t as I,j as r,g as Pr,$ as qr,c as o,s as n,d as _e,i as d,k as x,m as X,r as a,n as _,aU as jr,aM as ee}from"../chunks/wnqW1tdD.js";import{h as Fr,e as D,r as zr,s as N}from"../chunks/CDPCzm7q.js";import{i as P}from"../chunks/BjRbZGyQ.js";import{e as O,i as Z}from"../chunks/CsnEE4l9.js";import{h as Qe}from"../chunks/D8kkBG2G.js";import{c as Br}from"../chunks/ojdN50pv.js";import{b as Er,s as Xe,c as Lr}from"../chunks/rh_XW2Tv.js";import{s as Ur}from"../chunks/Bz0_kaay.js";import{b as Wr}from"../chunks/CJ-FD9ng.js";import{i as Hr}from"../chunks/BxG_UISn.js";import{a as Or,s as er}from"../chunks/D5ITLM2v.js";import{w as Zr}from"../chunks/BvpDAKCq.js";import{p as Gr}from"../chunks/B6j3ckjV.js";import{s as Ce}from"../chunks/BDqVm3Gq.js";import{l as Se,s as Me}from"../chunks/Cmdkv-7M.js";import{I as Ae}from"../chunks/CX_t0Ed_.js";import{Z as rr,C as ar,B as Yr}from"../chunks/Wm_FtPOB.js";import{T as Kr,a as Vr,C as Jr,U as Qr,B as tr,S as Xr}from"../chunks/JZlAgLJx.js";import{C as we}from"../chunks/ZAWXEYb0.js";import{D as ea}from"../chunks/BVqp3nk9.js";function ra(q,w){const M=Se(w,["children","$$slots","$$events","$$legacy"]),j=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"}],["path",{d:"M10 6h4"}],["path",{d:"M10 10h4"}],["path",{d:"M10 14h4"}],["path",{d:"M10 18h4"}]];Ae(q,Me({name:"building-2"},()=>M,{get iconNode(){return j},children:($,A)=>{var f=ke(),v=$e(f);Ce(v,w,"default",{},null),p($,f)},$$slots:{default:!0}}))}function aa(q,w){const M=Se(w,["children","$$slots","$$events","$$legacy"]),j=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"}],["circle",{cx:"12",cy:"12",r:"3"}]];Ae(q,Me({name:"eye"},()=>M,{get iconNode(){return j},children:($,A)=>{var f=ke(),v=$e(f);Ce(v,w,"default",{},null),p($,f)},$$slots:{default:!0}}))}function ta(q,w){const M=Se(w,["children","$$slots","$$events","$$legacy"]),j=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}]];Ae(q,Me({name:"message-square"},()=>M,{get iconNode(){return j},children:($,A)=>{var f=ke(),v=$e(f);Ce(v,w,"default",{},null),p($,f)},$$slots:{default:!0}}))}var oa=u('<button class="prompt-card p-4 border-2 transition-all duration-200 hover:-translate-y-1 text-left group" style="background: var(--background); border-color: var(--border); border-radius: 0.5rem;"><span class="text-sm font-medium group-hover:text-primary transition-colors" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1 mt-2 text-primary opacity-0 group-hover:opacity-100 transition-opacity"><span class="text-xs font-bold">Try This</span> <!></div></button>'),sa=u('<button class="template-card p-6 border-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg text-left group" style="background: var(--card); border-color: var(--border); border-radius: 0.5rem;"><div class="mb-3"><!></div> <h4 class="text-lg font-bold mb-2 group-hover:text-primary transition-colors" style="color: var(--foreground);"> </h4> <p class="text-sm leading-relaxed" style="color: var(--muted-foreground);"> </p> <div class="flex items-center gap-1 mt-3 text-primary opacity-0 group-hover:opacity-100 transition-opacity"><span class="text-xs font-bold">Use Template</span> <!></div></button>'),na=u('<div class="text-center py-8"><div class="flex items-center justify-center gap-2 mb-6"><!> <h3 class="text-2xl font-bold" style="color: var(--foreground);">Ready to Research</h3></div> <p class="font-medium mb-8 max-w-2xl mx-auto" style="color: var(--muted-foreground);">Get marketing intelligence on any company. Choose a template below or ask your custom question.</p> <div class="mb-8"><h4 class="text-sm font-bold mb-4 text-center" style="color: var(--muted-foreground);">Marketing Research Prompts</h4> <div class="grid md:grid-cols-2 gap-3 max-w-4xl mx-auto mb-6"></div></div> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"></div></div>'),ia=u('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown"><!> Download</button>'),la=u('<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);"> </p></div>'),da=u('<div class="p-4 border-b-2 border-border bg-muted/50"><div class="flex items-start gap-2"><span class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded">TL;DR</span> <div class="text-sm font-medium formatted-summary" style="color: var(--foreground);"><!></div></div></div>'),ca=u('<span class="text-xs font-bold px-2 py-1 border border-border rounded" style="background: var(--accent); color: var(--accent-foreground);"> </span>'),va=u('<div class="px-6 pt-4 pb-2"><div class="flex flex-wrap gap-2"></div></div>'),pa=u('<div class="border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"><!> <!> <div class="p-6"><div class="formatted-content max-w-none"><!></div></div> <div class="px-6 pb-4 border-t border-border"><div class="flex flex-wrap gap-2 mt-4"><button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"><!> Compare with competitor</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"><!> Add visuals</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"><!> Turn into slides</button></div></div></div>'),ma=u('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"><!></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1"><!> <span class="text-xs" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),ua=u('<div class="flex gap-4"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">Athena</span> <span class="text-xs" style="color: var(--muted-foreground);">Scanning recent campaigns...</span></div> <div class="p-4 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="flex items-center space-x-2"><div class="w-2 h-2 rounded-full animate-pulse" style="background: var(--primary);"></div> <div class="w-2 h-2 rounded-full animate-pulse animation-delay-2000" style="background: var(--primary);"></div> <div class="w-2 h-2 rounded-full animate-pulse animation-delay-4000" style="background: var(--primary);"></div> <span class="text-sm font-medium" style="color: var(--muted-foreground);">Reading earnings call notes and marketing data...</span></div></div></div></div>'),fa=u("<option> </option>"),ba=u('<div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>'),ga=u('<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div><h1 class="text-3xl font-black flex items-center gap-3" style="color: var(--foreground);"><!> Athena</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Your AI market researcher</p></div></div> <div class="flex items-center space-x-4"><div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Real-time Data</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">Athena</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-container"><div class="space-y-6"><!> <!> <!></div></div> <div class="input-wrapper p-6"><div class="flex items-center gap-2 mb-4"><span class="text-sm font-bold" style="color: var(--muted-foreground);">Format:</span> <select class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded" style="border-radius: 0.375rem;"></select></div> <div class="relative"><div class="spotlight-input-container"><textarea class="spotlight-input flex-1 resize-none min-h-[120px] p-6 pr-32 text-lg"></textarea> <button class="spotlight-button"><!> <span class="font-bold">Research</span></button></div></div></div></div></div></div></div>');function La(q,w){Ir(w,!1);const[M,j]=Or(),$=()=>er(Gr,"$page",M),A=()=>er(f,"$messages",M),f=Zr([]);let v=X(""),T=X(!1),re=0,ae=X(""),R=X("executive");const te=["Compare Drift and Intercom's messaging and channel mix based on the last 30 days...","How is Adobe marketing Firefly across its channels based on recent data...","What influencer or social campaigns has Notion run recently...","Map Figma's demand-gen strategy from 2022 to now...","Analyze Stripe's developer marketing evolution over the past quarter..."],or=["Compare Drift and Intercom's messaging and channel mix based on the last 30 days","How is Adobe marketing Firefly across its channels based on recent data?","What influencer or social campaigns has Notion run recently?","Map Figma's demand-gen strategy from 2022 to now"],sr=[{icon:ar,title:"Company Snapshot",description:"Get a summary of performance, team, and competitors",prompt:"Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics."},{icon:Vr,title:"Go-to-Market Audit",description:"Evaluate positioning, messaging, channels, and campaigns",prompt:"Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience."},{icon:Yr,title:"Brand Perception & Category",description:"Analyze how a brand is perceived in its space",prompt:"Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception."}],nr=[{value:"executive",label:"Executive Summary",description:"Bullet points + key insights"},{value:"slide-ready",label:"Slide-ready",description:"Sections + headers for export"},{value:"battlecard",label:"Competitive Battlecard",description:"Strategic comparison format"}];function oe(){return Math.random().toString(36).substr(2,9)}async function Te(){if(!r(v).trim()||r(T))return;let t="";r(R)==="executive"?t="[Executive Summary Format] Provide bullet points and key insights. ":r(R)==="slide-ready"?t="[Slide-ready Format] Structure with clear sections and headers for presentation export. ":r(R)==="battlecard"&&(t="[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. ");const e=t+r(v).trim();x(v,""),x(T,!0),f.update(s=>[...s,{id:oe(),role:"user",content:e,timestamp:new Date}]);try{const s=await fetch(`/dashboard/${$().params.envSlug}/researcher`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e})});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const i=await s.json(),l=oe();f.update(m=>[...m,{id:l,role:"assistant",content:i.response,timestamp:new Date,isReport:!0}])}catch(s){console.error("Error sending message:",s),f.update(i=>[...i,{id:oe(),role:"assistant",content:"I apologize, but Athena encountered an error while processing your request. Please try again.",timestamp:new Date}])}finally{x(T,!1)}}function ir(t){t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),Te())}function lr(t){x(v,t.prompt);const e=document.querySelector("textarea");e&&e.focus()}function dr(t){x(v,t);const e=document.querySelector("textarea");e&&e.focus()}function cr(t){const e=t.split(`
`).filter(m=>m.trim()),s=e.slice(0,2).join(" ").substring(0,200)+"...",i=e.filter(m=>m.includes("key")||m.includes("important")||m.includes("significant")).slice(0,3),l=[];return(t.includes("growth")||t.includes("increase"))&&l.push("↑ Trending"),(t.includes("insight")||t.includes("analysis"))&&l.push("💡 Insight"),(t.includes("challenge")||t.includes("weakness"))&&l.push("⚠ Weakness"),{summary:s,insights:i,badges:l}}Rr(()=>{x(ae,te[0]);const t=setInterval(()=>{re=(re+1)%te.length,x(ae,te[re])},4e3);return()=>clearInterval(t)});function vr(t){const e=pr(t.content),s=t.timestamp.toISOString().split("T")[0],i=`${e||"research-report"}-${s}.md`,l=`# Marketing Research Report by Athena
**Generated on:** ${t.timestamp.toLocaleDateString()}
**Time:** ${t.timestamp.toLocaleTimeString()}
**Format:** ${r(R).charAt(0).toUpperCase()+r(R).slice(1)}

---

${t.content}

---

*Report generated by Athena - Your AI Market Researcher*
`,m=new Blob([l],{type:"text/markdown"}),F=URL.createObjectURL(m),k=document.createElement("a");k.href=F,k.download=i,k.click(),URL.revokeObjectURL(F)}function pr(t){var s;const e=t.split(`
`);for(const i of e.slice(0,5)){if(i.includes("Company:")||i.includes("Company Name:"))return((s=i.split(":")[1])==null?void 0:s.trim().replace(/[^a-zA-Z0-9-]/g,""))||"";if(i.startsWith("# ")&&!i.includes("Research")&&!i.includes("Report"))return i.replace("# ","").trim().replace(/[^a-zA-Z0-9-]/g,"")||""}return""}function Re(t){let e=t.replace(/^# (.*$)/gim,'<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>').replace(/^#### (.*$)/gim,'<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/\*(.*?)\*/g,'<em class="italic" style="color: var(--muted-foreground);">$1</em>').replace(/```([\s\S]*?)```/g,'<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>').replace(/\|(.+)\|/g,s=>"<tr>"+s.split("|").filter(l=>l.trim()).map(l=>l.trim()).map(l=>`<td class="border border-border px-3 py-2" style="color: var(--foreground);">${l}</td>`).join("")+"</tr>").replace(/^[\s]*[-*+] (.+)$/gim,'<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>').replace(/^[\s]*\d+\. (.+)$/gim,'<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: decimal;">$1</li>').replace(/^> (.+)$/gim,'<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>').replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>').replace(/\n\n/g,'</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">').replace(/\n/g,"<br>");return!e.startsWith("<h")&&!e.startsWith("<p")&&!e.startsWith("<ul")&&!e.startsWith("<ol")&&!e.startsWith("<blockquote")&&(e='<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">'+e+"</p>"),e=e.replace(/(<li[^>]*>.*?<\/li>)/gs,s=>s.includes("list-style-type: disc")?'<ul class="mb-4">'+s+"</ul>":s.includes("list-style-type: decimal")?'<ol class="mb-4">'+s+"</ol>":s),e.includes("<tr>")&&(e=e.replace(/(<tr>.*?<\/tr>)/gs,'<table class="w-full border-collapse border border-border my-4">$1</table>')),e}Dr(()=>A(),()=>{A().length>0&&setTimeout(()=>{const t=document.querySelector(".messages-container");t&&(t.scrollTop=t.scrollHeight)},100)}),Nr(),Hr();var se=ga();Fr(t=>{qr.title="Athena - AI Market Researcher"});var ne=o(se),Ie=o(ne),De=o(Ie),ie=o(De),le=o(ie),mr=o(le);ra(mr,{class:"w-6 h-6",style:"color: var(--primary-foreground);"}),a(le);var Ne=n(le,2),Pe=o(Ne),ur=o(Pe);rr(ur,{class:"w-8 h-8",style:"color: var(--primary);"}),_(),a(Pe),_(2),a(Ne),a(ie);var qe=n(ie,2),je=o(qe),fr=o(je);Kr(fr,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),_(2),a(je),a(qe),a(De),a(Ie),a(ne);var de=n(ne,2),Fe=o(de),ze=o(Fe),br=n(ze,2);we(br,{class:"w-4 h-4"}),_(2),a(Fe),a(de);var Be=n(de,2),Ee=o(Be),Le=o(Ee),ce=o(Le),Ue=o(ce),We=o(Ue);{var gr=t=>{var e=na(),s=o(e),i=o(s);rr(i,{class:"w-6 h-6",style:"color: var(--primary);"}),_(2),a(s);var l=n(s,4),m=n(o(l),2);O(m,5,()=>or,Z,(k,b)=>{var g=oa(),C=o(g),z=o(C,!0);a(C);var S=n(C,2),U=n(o(S),2);we(U,{class:"w-3 h-3"}),a(S),a(g),I(()=>N(z,r(b))),D("click",g,()=>dr(r(b))),p(k,g)}),a(m),a(l);var F=n(l,2);O(F,5,()=>sr,Z,(k,b)=>{var g=sa(),C=o(g),z=o(C);Br(z,()=>r(b).icon,(fe,be)=>{be(fe,{class:"w-8 h-8",style:"color: var(--primary);"})}),a(C);var S=n(C,2),U=o(S,!0);a(S);var W=n(S,2),me=o(W,!0);a(W);var Y=n(W,2),ue=n(o(Y),2);we(ue,{class:"w-3 h-3"}),a(Y),a(g),I(()=>{N(U,(r(b),d(()=>r(b).title))),N(me,(r(b),d(()=>r(b).description)))}),D("click",g,()=>lr(r(b))),p(k,g)}),a(F),a(e),p(t,e)};P(We,t=>{A(),d(()=>A().length===0)&&t(gr)})}var He=n(We,2);O(He,1,A,Z,(t,e)=>{var s=ma(),i=o(s),l=o(i);{var m=c=>{Qr(c,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},F=c=>{tr(c,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};P(l,c=>{r(e),d(()=>r(e).role==="user")?c(m):c(F,!1)})}a(i);var k=n(i,2),b=o(k),g=o(b),C=o(g,!0);a(g);var z=n(g,2),S=o(z);Jr(S,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var U=n(S,2),W=o(U,!0);a(U),a(z);var me=n(z,2);{var Y=c=>{var y=ia(),h=o(y);ea(h,{class:"w-3 h-3"}),_(),a(y),D("click",y,()=>vr(r(e))),p(c,y)};P(me,c=>{r(e),d(()=>r(e).role==="assistant"&&r(e).isReport)&&c(Y)})}a(b);var ue=n(b,2);{var fe=c=>{var y=la(),h=o(y),K=o(h,!0);a(h),a(y),I(()=>N(K,(r(e),d(()=>r(e).content)))),p(c,y)},be=c=>{var y=pa();const h=_e(()=>(r(e),d(()=>cr(r(e).content))));var K=o(y);{var kr=B=>{var E=da(),H=o(E),Q=n(o(H),2),ye=o(Q);Qe(ye,()=>(ee(r(h)),d(()=>Re(r(h).summary)))),a(Q),a(H),a(E),p(B,E)};P(K,B=>{ee(r(h)),d(()=>r(h).summary)&&B(kr)})}var Ye=n(K,2);{var $r=B=>{var E=va(),H=o(E);O(H,5,()=>(ee(r(h)),d(()=>r(h).badges)),Z,(Q,ye)=>{var xe=ca(),Tr=o(xe,!0);a(xe),I(()=>N(Tr,r(ye))),p(Q,xe)}),a(H),a(E),p(B,E)};P(Ye,B=>{ee(r(h)),d(()=>r(h).badges.length>0)&&B($r)})}var ge=n(Ye,2),Ke=o(ge),Cr=o(Ke);Qe(Cr,()=>(r(e),d(()=>Re(r(e).content)))),a(Ke),a(ge);var Ve=n(ge,2),Je=o(Ve),V=o(Je),Sr=o(V);aa(Sr,{class:"w-3 h-3"}),_(),a(V);var J=n(V,2),Mr=o(J);ar(Mr,{class:"w-3 h-3"}),_(),a(J);var he=n(J,2),Ar=o(he);ta(Ar,{class:"w-3 h-3"}),_(),a(he),a(Je),a(Ve),a(y),D("click",V,()=>x(v,"Compare this analysis with their main competitor")),D("click",J,()=>x(v,"Add visual charts and graphs to this analysis")),D("click",he,()=>x(v,"Turn this analysis into presentation slides")),p(c,y)};P(ue,c=>{r(e),d(()=>r(e).role==="user")?c(fe):c(be,!1)})}a(k),a(s),I(c=>{Ur(s,1,`flex gap-4 ${r(e),d(()=>r(e).role==="user"?"flex-row-reverse":"")??""}`),Lr(i,`background: var(--${r(e),d(()=>r(e).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),N(C,(r(e),d(()=>r(e).role==="user"?"You":"Athena"))),N(W,c)},[()=>(r(e),d(()=>r(e).timestamp.toLocaleTimeString()))],_e),p(t,s)});var hr=n(He,2);{var yr=t=>{var e=ua(),s=o(e),i=o(s);tr(i,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),a(s),_(2),a(e),p(t,e)};P(hr,t=>{r(T)&&t(yr)})}a(Ue),a(ce);var Oe=n(ce,2),ve=o(Oe),pe=n(o(ve),2);I(()=>{r(R),jr(()=>{})}),O(pe,5,()=>nr,Z,(t,e)=>{var s=fa(),i=o(s,!0);a(s);var l={};I(()=>{N(i,(r(e),d(()=>r(e).label))),l!==(l=(r(e),d(()=>r(e).value)))&&(s.value=(s.__value=(r(e),d(()=>r(e).value)))??"")}),p(t,s)}),a(pe),a(ve);var Ze=n(ve,2),Ge=o(Ze),L=o(Ge);zr(L);var G=n(L,2),xr=o(G);{var _r=t=>{var e=ba();p(t,e)},wr=t=>{Xr(t,{class:"w-5 h-5"})};P(xr,t=>{r(T)?t(_r):t(wr,!1)})}_(2),a(G),a(Ge),a(Ze),a(Oe),a(Le),a(Ee),a(Be),a(se),I(t=>{Xe(ze,"href",`/dashboard/${$(),d(()=>$().params.envSlug)??""}`),Xe(L,"placeholder",r(ae)),L.disabled=r(T),G.disabled=t},[()=>(r(v),r(T),d(()=>!r(v).trim()||r(T)))],_e),Er(pe,()=>r(R),t=>x(R,t)),Wr(L,()=>r(v),t=>x(v,t)),D("keydown",L,ir),D("click",G,Te),p(q,se),Pr(),j()}export{La as component};
